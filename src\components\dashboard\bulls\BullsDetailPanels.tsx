import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '../../ui/tabs';
import { SociogramData, ProfilesData, ContextData, ComparisonsData } from '../../../types/bulls-dashboard';
import { SociogramPanel } from './panels/SociogramPanel';
import { ProfilesPanel } from './panels/ProfilesPanel';
import { ContextPanel } from './panels/ContextPanel';
import { ComparisonsPanel } from './panels/ComparisonsPanel';
import { CourseOverviewPanel } from './panels/CourseOverviewPanel';
import { RecommendedActionsPanel } from './panels/RecommendedActionsPanel';
import BullsCriteriaPanel from './BullsCriteriaPanel';
import { Network, Users, MapPin, BarChart3, BookOpen, Target, Shield } from 'lucide-react';
import { useBullsDashboard } from '../../../contexts/BullsDashboardContext';

interface BullsDetailPanelsProps {
  activePanel: 'overview' | 'sociogram' | 'profiles' | 'context' | 'comparisons' | 'recommendations' | 'bulls-criteria';
  onPanelChange: (panel: 'overview' | 'sociogram' | 'profiles' | 'context' | 'comparisons' | 'recommendations' | 'bulls-criteria') => void;
}

export const BullsDetailPanels: React.FC<BullsDetailPanelsProps> = ({
  activePanel,
  onPanelChange
}) => {
  const { sociogramData, profilesData, contextData, comparisonsData, loading, courseOverviewData, recommendedActionsData } = useBullsDashboard();

  // Función para manejar navegación entre paneles
  const handleNavigateToPanel = (panelType: string, filters?: any) => {
    onPanelChange(panelType as any);
    // Aquí se podrían aplicar filtros específicos si fuera necesario
    console.log(`Navegando a ${panelType} con filtros:`, filters);
  };

  // Función para actualizar estado de recomendaciones
  const handleUpdateRecommendationStatus = (id: string, newStatus: string) => {
    // Aquí se implementaría la lógica para actualizar el estado en el servidor
    console.log(`Actualizando recomendación ${id} a estado: ${newStatus}`);
    // En una implementación real, esto haría una llamada a la API
  };

  // Función para navegar a perfiles específicos
  const handleNavigateToProfiles = (studentId?: string) => {
    onPanelChange('profiles');
    console.log(`Navegando a perfiles${studentId ? ` para estudiante: ${studentId}` : ''}`);
  };


  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border" data-testid="detail-panels">
      <Tabs value={activePanel} onValueChange={onPanelChange} className="w-full">
        <div className="border-b border-gray-200 px-6 pt-6">
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="overview" className="flex items-center space-x-2">
              <BookOpen className="h-4 w-4" />
              <span>Panorama del Curso</span>
            </TabsTrigger>
            <TabsTrigger value="bulls-criteria" className="flex items-center space-x-2">
              <Shield className="h-4 w-4" />
              <span>Criterio BULL-S</span>
            </TabsTrigger>
            <TabsTrigger value="sociogram" className="flex items-center space-x-2">
              <Network className="h-4 w-4" />
              <span>Sociograma</span>
            </TabsTrigger>
            <TabsTrigger value="profiles" className="flex items-center space-x-2">
              <Users className="h-4 w-4" />
              <span>Perfiles</span>
            </TabsTrigger>
            <TabsTrigger value="context" className="flex items-center space-x-2">
              <MapPin className="h-4 w-4" />
              <span>Contexto</span>
            </TabsTrigger>
            <TabsTrigger value="comparisons" className="flex items-center space-x-2">
              <BarChart3 className="h-4 w-4" />
              <span>Comparativas</span>
            </TabsTrigger>
            <TabsTrigger value="recommendations" className="flex items-center space-x-2">
              <Target className="h-4 w-4" />
              <span>Acciones Recomendadas</span>
            </TabsTrigger>
          </TabsList>
        </div>

        <div className="p-6">
          <TabsContent value="overview" className="mt-0">
            <CourseOverviewPanel 
              data={courseOverviewData}
              onNavigateToProfiles={handleNavigateToProfiles}
              onNavigateToPanel={handleNavigateToPanel}
            />
          </TabsContent>

          <TabsContent value="bulls-criteria" className="mt-0">
            <BullsCriteriaPanel />
          </TabsContent>

          <TabsContent value="sociogram" className="mt-0">
            <SociogramPanel data={sociogramData} />
          </TabsContent>

          <TabsContent value="profiles" className="mt-0">
            <ProfilesPanel data={profilesData} />
          </TabsContent>

          <TabsContent value="context" className="mt-0">
            <ContextPanel data={contextData} />
          </TabsContent>

          <TabsContent value="comparisons" className="mt-0">
            <ComparisonsPanel data={comparisonsData} />
          </TabsContent>

          <TabsContent value="recommendations" className="mt-0">
            <RecommendedActionsPanel 
              data={recommendedActionsData}
              onUpdateRecommendationStatus={handleUpdateRecommendationStatus}
              onNavigateToProfiles={handleNavigateToProfiles}
            />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};