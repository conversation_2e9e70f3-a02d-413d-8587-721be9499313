-- Script para crear la tabla preguntas con estructura completa
-- Basado en el análisis del código existente

CREATE TABLE IF NOT EXISTS public.preguntas (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    numero_pregunta INTEGER NOT NULL,
    pregunta TEXT NOT NULL,
    texto_pregunta TEXT, -- Alias o campo adicional para el texto
    categoria TEXT DEFAULT 'general',
    tipo_pregunta TEXT DEFAULT 'multiple_choice',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear índices para optimizar consultas
CREATE INDEX IF NOT EXISTS idx_preguntas_numero ON public.preguntas(numero_pregunta);
CREATE INDEX IF NOT EXISTS idx_preguntas_categoria ON public.preguntas(categoria, tipo_pregunta);
CREATE INDEX IF NOT EXISTS idx_preguntas_tipo ON public.preguntas(tipo_pregunta);

-- Insertar preguntas de ejemplo basadas en los datos encontrados
-- Preguntas sociométricas (1-10)
INSERT INTO public.preguntas (id, numero_pregunta, pregunta, texto_pregunta, categoria, tipo_pregunta) VALUES
('00000000-0000-0000-0000-000000000001', 1, '¿Con quién te gusta trabajar en equipo?', '¿Con quién te gusta trabajar en equipo?', 'sociometrica', 'eleccion'),
('00000000-0000-0000-0000-000000000002', 2, '¿Con quién te gusta jugar en el recreo?', '¿Con quién te gusta jugar en el recreo?', 'sociometrica', 'eleccion'),
('00000000-0000-0000-0000-000000000003', 3, '¿A quién elegirías como líder del grupo?', '¿A quién elegirías como líder del grupo?', 'sociometrica', 'eleccion'),
('00000000-0000-0000-0000-000000000004', 4, '¿Con quién te sientes más cómodo hablando?', '¿Con quién te sientes más cómodo hablando?', 'sociometrica', 'eleccion'),
('00000000-0000-0000-0000-000000000005', 5, '¿A quién invitarías a tu cumpleaños?', '¿A quién invitarías a tu cumpleaños?', 'sociometrica', 'eleccion'),
('00000000-0000-0000-0000-000000000006', 6, '¿Con quién no te gusta trabajar?', '¿Con quién no te gusta trabajar?', 'sociometrica', 'rechazo'),
('00000000-0000-0000-0000-000000000007', 7, '¿Con quién prefieres no jugar?', '¿Con quién prefieres no jugar?', 'sociometrica', 'rechazo'),
('00000000-0000-0000-0000-000000000008', 8, '¿A quién no elegirías como líder?', '¿A quién no elegirías como líder?', 'sociometrica', 'rechazo'),
('00000000-0000-0000-0000-000000000009', 9, '¿Con quién te sientes incómodo?', '¿Con quién te sientes incómodo?', 'sociometrica', 'rechazo'),
('00000000-0000-0000-0000-000000000010', 10, '¿A quién no invitarías a tu cumpleaños?', '¿A quién no invitarías a tu cumpleaños?', 'sociometrica', 'rechazo')
ON CONFLICT (id) DO UPDATE SET
    numero_pregunta = EXCLUDED.numero_pregunta,
    pregunta = EXCLUDED.pregunta,
    texto_pregunta = EXCLUDED.texto_pregunta,
    categoria = EXCLUDED.categoria,
    tipo_pregunta = EXCLUDED.tipo_pregunta,
    updated_at = NOW();

-- Preguntas abiertas (11-15)
INSERT INTO public.preguntas (id, numero_pregunta, pregunta, texto_pregunta, categoria, tipo_pregunta) VALUES
('00000000-0000-0000-0000-000000000011', 11, '¿Cómo te sientes en el colegio?', '¿Cómo te sientes en el colegio?', 'bienestar', 'abierta'),
('00000000-0000-0000-0000-000000000012', 12, '¿Qué situaciones te hacen sentir inseguro en el colegio?', '¿Qué situaciones te hacen sentir inseguro en el colegio?', 'seguridad', 'abierta'),
('00000000-0000-0000-0000-000000000013', 13, '¿Has observado situaciones de agresión en tu clase?', '¿Has observado situaciones de agresión en tu clase?', 'bullying', 'abierta'),
('00000000-0000-0000-0000-000000000014', 14, '¿Qué harías si vieras a alguien siendo maltratado?', '¿Qué harías si vieras a alguien siendo maltratado?', 'bullying', 'abierta'),
('00000000-0000-0000-0000-000000000015', 15, '¿Cómo mejorarías el ambiente en tu clase?', '¿Cómo mejorarías el ambiente en tu clase?', 'bienestar', 'abierta')
ON CONFLICT (id) DO UPDATE SET
    numero_pregunta = EXCLUDED.numero_pregunta,
    pregunta = EXCLUDED.pregunta,
    texto_pregunta = EXCLUDED.texto_pregunta,
    categoria = EXCLUDED.categoria,
    tipo_pregunta = EXCLUDED.tipo_pregunta,
    updated_at = NOW();

-- Preguntas adicionales de bullying (16-18) basadas en los patrones encontrados
INSERT INTO public.preguntas (id, numero_pregunta, pregunta, texto_pregunta, categoria, tipo_pregunta) VALUES
('00000000-0000-0000-0000-000000000016', 16, '¿Has sufrido agresiones físicas en el colegio?', '¿Has sufrido agresiones físicas en el colegio?', 'bullying', 'multiple_choice'),
('00000000-0000-0000-0000-000000000017', 17, '¿Has recibido insultos o burlas de tus compañeros?', '¿Has recibido insultos o burlas de tus compañeros?', 'bullying', 'multiple_choice'),
('00000000-0000-0000-0000-000000000018', 18, '¿Te han excluido o aislado del grupo?', '¿Te han excluido o aislado del grupo?', 'bullying', 'multiple_choice')
ON CONFLICT (id) DO UPDATE SET
    numero_pregunta = EXCLUDED.numero_pregunta,
    pregunta = EXCLUDED.pregunta,
    texto_pregunta = EXCLUDED.texto_pregunta,
    categoria = EXCLUDED.categoria,
    tipo_pregunta = EXCLUDED.tipo_pregunta,
    updated_at = NOW();

-- Función para actualizar timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger para actualizar automáticamente updated_at
CREATE TRIGGER update_preguntas_updated_at 
    BEFORE UPDATE ON public.preguntas 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Comentarios para documentar la tabla
COMMENT ON TABLE public.preguntas IS 'Tabla que almacena las preguntas del cuestionario sociométrico y de bullying';
COMMENT ON COLUMN public.preguntas.id IS 'Identificador único de la pregunta (UUID)';
COMMENT ON COLUMN public.preguntas.numero_pregunta IS 'Número secuencial de la pregunta (1-18)';
COMMENT ON COLUMN public.preguntas.pregunta IS 'Texto principal de la pregunta';
COMMENT ON COLUMN public.preguntas.texto_pregunta IS 'Texto alternativo o completo de la pregunta';
COMMENT ON COLUMN public.preguntas.categoria IS 'Categoría de la pregunta: sociometrica, bullying, bienestar, seguridad';
COMMENT ON COLUMN public.preguntas.tipo_pregunta IS 'Tipo de pregunta: eleccion, rechazo, abierta, multiple_choice';