import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig(() => ({
  // Base path para desarrollo local (cambiar a '/Bull-S/' para producción)
  base: '/',
  plugins: [react()],

  // Optimizaciones de build simplificadas
  build: {
    // Configuración básica
    minify: 'esbuild',
    sourcemap: false,
    target: 'es2015',
    assetsDir: 'assets',
    chunkSizeWarningLimit: 1000
  },

  // Optimizaciones de desarrollo
  server: {
    port: 3002,  // Puerto consistente con el servidor actual
    host: true,
    strictPort: false,
    open: false,
    hmr: {
      overlay: false,
      port: 3002
    },

    proxy: {
      '/functions': {
        target: 'https://eckuozleqbbcecaycmjt.supabase.co',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path
      }
    },
    cors: true,
    // Configuración para BrowserRouter
    historyApiFallback: true
  },

  // Configuración de preview
  preview: {
    port: 3002,
    strictPort: false
  },

  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@pages': path.resolve(__dirname, './src/pages'),
      '@hooks': path.resolve(__dirname, './src/hooks'),
      '@utils': path.resolve(__dirname, './src/utils'),
      '@services': path.resolve(__dirname, './src/services'),
      '@lib': path.resolve(__dirname, './src/lib'),
      '@types': path.resolve(__dirname, './src/types'),
      '@assets': path.resolve(__dirname, './src/assets'),
      '@contexts': path.resolve(__dirname, './src/contexts'),
      '@layouts': path.resolve(__dirname, './src/layouts')
    },
  },

  // Optimizaciones de dependencias
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@supabase/supabase-js',
      'lucide-react',
      'framer-motion',
      'recharts',
      'clsx',
      'html2canvas',
      'jspdf'
    ],
    exclude: ['@testing-library/react']
  },

  esbuild: {
    target: 'es2015'
  },
}))
