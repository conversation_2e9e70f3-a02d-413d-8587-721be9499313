/**
 * Script para aplicar las vistas del dashboard a Supabase
 * Crea las vistas necesarias para calcular métricas de convivencia escolar
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Error: Variables de entorno de Supabase no encontradas');
  console.error('Asegúrate de que VITE_SUPABASE_URL y VITE_SUPABASE_ANON_KEY estén configuradas en .env');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// SQL para crear las vistas del dashboard
const createViewsSQL = `
-- Vista para indicadores de bullying del dashboard
CREATE OR REPLACE VIEW view_dashboard_bullying_indicators AS
WITH student_responses AS (
  SELECT 
    e.id,
    e.nombre_estudiante,
    e.apellido_estudiante,
    e.grupo_id,
    e.institucion_id,
    e.genero,
    e.edad,
    -- Contar respuestas de agresión recibidas (víctima)
    COUNT(CASE WHEN rc_victim.pregunta_id LIKE '%victim%' OR rc_victim.pregunta_id LIKE '%agresion_recibida%' THEN 1 END) as victimization_score,
    -- Contar respuestas de agresión emitidas (agresor)
    COUNT(CASE WHEN rc_bully.pregunta_id LIKE '%bully%' OR rc_bully.pregunta_id LIKE '%agresion_emitida%' THEN 1 END) as aggression_score,
    -- Contar nominaciones positivas recibidas
    COUNT(CASE WHEN rc_pos.pregunta_id LIKE '%eleccion%' OR rc_pos.pregunta_id LIKE '%positive%' THEN 1 END) as positive_nominations,
    -- Contar nominaciones negativas recibidas
    COUNT(CASE WHEN rc_neg.pregunta_id LIKE '%rechazo%' OR rc_neg.pregunta_id LIKE '%negative%' THEN 1 END) as negative_nominations
  FROM estudiantes e
  LEFT JOIN respuestas_cuestionario rc_victim ON e.codigo_anonimizado = ANY(rc_victim.companeros_seleccionados)
  LEFT JOIN respuestas_cuestionario rc_bully ON e.codigo_anonimizado = ANY(rc_bully.companeros_seleccionados)
  LEFT JOIN respuestas_cuestionario rc_pos ON e.codigo_anonimizado = ANY(rc_pos.companeros_seleccionados)
  LEFT JOIN respuestas_cuestionario rc_neg ON e.codigo_anonimizado = ANY(rc_neg.companeros_seleccionados)
  GROUP BY e.id, e.nombre_estudiante, e.apellido_estudiante, e.grupo_id, e.institucion_id, e.genero, e.edad
),
role_classification AS (
  SELECT 
    *,
    CASE 
      WHEN victimization_score > 2 AND aggression_score > 2 THEN 'Bully-Victim'
      WHEN victimization_score > 2 THEN 'Victim'
      WHEN aggression_score > 2 THEN 'Bully'
      ELSE 'Observer'
    END as bullying_role,
    CASE 
      WHEN positive_nominations > 5 THEN 'Popular'
      WHEN negative_nominations > 5 THEN 'Rejected'
      WHEN positive_nominations < 2 AND negative_nominations < 2 THEN 'Isolated'
      WHEN positive_nominations > 3 AND negative_nominations > 3 THEN 'Controversial'
      ELSE 'Average'
    END as sociometric_status
  FROM student_responses
)
SELECT 
  id,
  nombre_estudiante,
  apellido_estudiante,
  grupo_id,
  institucion_id,
  genero,
  edad,
  bullying_role,
  sociometric_status,
  positive_nominations,
  negative_nominations,
  victimization_score,
  aggression_score
FROM role_classification;
`;

const createStudentsViewSQL = `
-- Vista para estudiantes con respuestas
CREATE OR REPLACE VIEW view_estudiantes_con_respuestas AS
SELECT DISTINCT
  e.id,
  e.nombre_estudiante,
  e.apellido_estudiante,
  e.grupo_id,
  e.institucion_id,
  e.genero,
  e.edad,
  COUNT(rc.id) as total_respuestas
FROM estudiantes e
INNER JOIN respuestas_cuestionario rc ON e.id = rc.estudiante_id
GROUP BY e.id, e.nombre_estudiante, e.apellido_estudiante, e.grupo_id, e.institucion_id, e.genero, e.edad
HAVING COUNT(rc.id) > 0;
`;

const createGroupsViewSQL = `
-- Vista para grupos con respuestas
CREATE OR REPLACE VIEW view_grupos_con_respuestas AS
SELECT 
  g.id,
  g.nombre,
  g.descripcion,
  g.grado,
  g.seccion,
  g.ano_escolar,
  g.institucion_id,
  COUNT(DISTINCT e.id) as total_estudiantes,
  COUNT(DISTINCT rc.estudiante_id) as estudiantes_con_respuestas,
  ROUND(
    (COUNT(DISTINCT rc.estudiante_id)::DECIMAL / NULLIF(COUNT(DISTINCT e.id), 0)) * 100, 
    2
  ) as porcentaje_participacion
FROM grupos g
LEFT JOIN estudiantes e ON g.id = e.grupo_id
LEFT JOIN respuestas_cuestionario rc ON e.id = rc.estudiante_id
GROUP BY g.id, g.nombre, g.descripcion, g.grado, g.seccion, g.ano_escolar, g.institucion_id;
`;

const createMetricsViewSQL = `
-- Vista para métricas de convivencia escolar
CREATE OR REPLACE VIEW view_metricas_convivencia AS
WITH bullying_metrics AS (
  SELECT 
    grupo_id,
    institucion_id,
    -- Tasas de Incidencia General
    COUNT(CASE WHEN bullying_role = 'Bully' THEN 1 END) as agresores,
    COUNT(CASE WHEN bullying_role = 'Victim' THEN 1 END) as victimas,
    COUNT(CASE WHEN bullying_role = 'Bully-Victim' THEN 1 END) as agresores_victimas,
    COUNT(CASE WHEN bullying_role = 'Observer' THEN 1 END) as observadores,
    COUNT(*) as total_estudiantes,
    
    -- Estatus Sociométrico
    COUNT(CASE WHEN sociometric_status = 'Popular' THEN 1 END) as populares,
    COUNT(CASE WHEN sociometric_status = 'Average' THEN 1 END) as promedio,
    COUNT(CASE WHEN sociometric_status = 'Isolated' THEN 1 END) as aislados,
    COUNT(CASE WHEN sociometric_status = 'Rejected' THEN 1 END) as rechazados,
    COUNT(CASE WHEN sociometric_status = 'Controversial' THEN 1 END) as controvertidos,
    
    -- Cohesión Grupal (basada en nominaciones positivas)
    AVG(positive_nominations) as promedio_nominaciones_positivas,
    STDDEV(positive_nominations) as desviacion_nominaciones_positivas
  FROM view_dashboard_bullying_indicators
  GROUP BY grupo_id, institucion_id
),
cohesion_calculation AS (
  SELECT 
    *,
    CASE 
      WHEN promedio_nominaciones_positivas > 4 AND desviacion_nominaciones_positivas < 2 THEN 'Alta'
      WHEN promedio_nominaciones_positivas > 2 AND desviacion_nominaciones_positivas < 3 THEN 'Media'
      ELSE 'Baja'
    END as cohesion_grupal,
    ROUND(
      CASE 
        WHEN promedio_nominaciones_positivas > 4 AND desviacion_nominaciones_positivas < 2 THEN 85
        WHEN promedio_nominaciones_positivas > 2 AND desviacion_nominaciones_positivas < 3 THEN 65
        ELSE 35
      END, 2
    ) as cohesion_score
  FROM bullying_metrics
)
SELECT 
  grupo_id,
  institucion_id,
  -- Tasas de Incidencia (porcentajes)
  ROUND((agresores::DECIMAL / NULLIF(total_estudiantes, 0)) * 100, 2) as tasa_agresores,
  ROUND((victimas::DECIMAL / NULLIF(total_estudiantes, 0)) * 100, 2) as tasa_victimas,
  ROUND((agresores_victimas::DECIMAL / NULLIF(total_estudiantes, 0)) * 100, 2) as tasa_agresores_victimas,
  ROUND((observadores::DECIMAL / NULLIF(total_estudiantes, 0)) * 100, 2) as tasa_observadores,
  
  -- Estatus Sociométrico (porcentajes)
  ROUND((populares::DECIMAL / NULLIF(total_estudiantes, 0)) * 100, 2) as porcentaje_populares,
  ROUND((promedio::DECIMAL / NULLIF(total_estudiantes, 0)) * 100, 2) as porcentaje_promedio,
  ROUND((aislados::DECIMAL / NULLIF(total_estudiantes, 0)) * 100, 2) as porcentaje_aislados,
  ROUND((rechazados::DECIMAL / NULLIF(total_estudiantes, 0)) * 100, 2) as porcentaje_rechazados,
  ROUND((controvertidos::DECIMAL / NULLIF(total_estudiantes, 0)) * 100, 2) as porcentaje_controvertidos,
  
  -- Cohesión Grupal
  cohesion_grupal,
  cohesion_score,
  promedio_nominaciones_positivas,
  
  -- Dinámicas de Acoso Predominantes
  CASE 
    WHEN agresores > victimas THEN 'Agresión Dominante'
    WHEN victimas > agresores THEN 'Victimización Prevalente'
    WHEN agresores_victimas > (agresores + victimas) / 2 THEN 'Ciclos de Violencia'
    ELSE 'Ambiente Observacional'
  END as dinamica_predominante,
  
  total_estudiantes
FROM cohesion_calculation;
`;

async function aplicarVistas() {
  console.log('🔄 Aplicando vistas del dashboard a Supabase...');
  
  try {
    // Aplicar vista de indicadores de bullying
    console.log('📊 Creando vista de indicadores de bullying...');
    const { error: error1 } = await supabase.rpc('exec_sql', { sql: createViewsSQL });
    if (error1) {
      console.error('❌ Error creando vista de indicadores:', error1);
    } else {
      console.log('✅ Vista view_dashboard_bullying_indicators creada');
    }

    // Aplicar vista de estudiantes con respuestas
    console.log('👥 Creando vista de estudiantes con respuestas...');
    const { error: error2 } = await supabase.rpc('exec_sql', { sql: createStudentsViewSQL });
    if (error2) {
      console.error('❌ Error creando vista de estudiantes:', error2);
    } else {
      console.log('✅ Vista view_estudiantes_con_respuestas creada');
    }

    // Aplicar vista de grupos con respuestas
    console.log('🏫 Creando vista de grupos con respuestas...');
    const { error: error3 } = await supabase.rpc('exec_sql', { sql: createGroupsViewSQL });
    if (error3) {
      console.error('❌ Error creando vista de grupos:', error3);
    } else {
      console.log('✅ Vista view_grupos_con_respuestas creada');
    }

    // Aplicar vista de métricas de convivencia
    console.log('📈 Creando vista de métricas de convivencia...');
    const { error: error4 } = await supabase.rpc('exec_sql', { sql: createMetricsViewSQL });
    if (error4) {
      console.error('❌ Error creando vista de métricas:', error4);
    } else {
      console.log('✅ Vista view_metricas_convivencia creada');
    }

    console.log('\n🎉 Proceso completado. Verificando vistas creadas...');
    
    // Verificar que las vistas existen
    const vistas = [
      'view_dashboard_bullying_indicators',
      'view_estudiantes_con_respuestas', 
      'view_grupos_con_respuestas',
      'view_metricas_convivencia'
    ];
    
    for (const vista of vistas) {
      const { data, error } = await supabase
        .from(vista)
        .select('*')
        .limit(1);
      
      if (error) {
        console.log(`❌ ${vista}: Error - ${error.message}`);
      } else {
        console.log(`✅ ${vista}: Disponible`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error general:', error);
  }
}

// Ejecutar el script
aplicarVistas().then(() => {
  console.log('\n📋 Resumen:');
  console.log('- Las vistas han sido creadas para calcular métricas de convivencia escolar');
  console.log('- Tasas de Incidencia General: Agresores, Víctimas, Observadores');
  console.log('- Estatus Sociométrico Promedio: Popular, Aislado, Rechazado, etc.');
  console.log('- Cohesión Grupal: Basada en nominaciones positivas');
  console.log('- Dinámicas de Acoso: Patrones predominantes identificados');
  console.log('\n🔗 Las vistas están listas para ser utilizadas por el dashboard');
});