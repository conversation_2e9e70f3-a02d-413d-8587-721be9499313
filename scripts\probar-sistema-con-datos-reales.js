/**
 * Script para probar el sistema optimizado con datos reales
 * Simula el comportamiento del sistema usando los datos CSV directamente
 */

import fs from 'fs';
import path from 'path';
import csv from 'csv-parser';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Mapeo de IDs de preguntas sociométricas
const PREGUNTAS_SOCIOMETRICAS = {
  'd90ddd09-3878-4efc-9059-7279570157bc': 'eleccion',     // ¿Con qué compañeros/as te gusta estar más?
  '47b56067-0c8c-4565-b645-80348852907f': 'rechazo',      // ¿Con qué compañeros/as te gusta estar menos?
  'dae67e87-db3e-4637-ace1-f1148f1d7d69': 'agresion',     // ¿Quiénes suelen intimidar o maltratar?
  'd2888d67-9878-4cdf-8a58-592c251c1cb6': 'victimizacion', // ¿Quiénes suelen ser víctimas?
  '775af389-a84d-4f40-8fb9-7b94cbea5498': 'colaboracion'   // ¿Con quién te gusta trabajar?
};

/**
 * Lee un archivo CSV y devuelve los datos como array
 */
function leerCSV(rutaArchivo) {
  return new Promise((resolve, reject) => {
    const datos = [];
    fs.createReadStream(rutaArchivo)
      .pipe(csv())
      .on('data', (fila) => datos.push(fila))
      .on('end', () => resolve(datos))
      .on('error', reject);
  });
}

/**
 * Procesa las respuestas sociométricas
 */
function procesarRespuestasSociometricas(respuestas) {
  const nominaciones = [];
  const estadisticas = {
    totalRespuestas: respuestas.length,
    respuestasSociometricas: 0,
    nominacionesGeneradas: 0,
    tiposNominacion: {}
  };
  
  respuestas.forEach(respuesta => {
    const { estudiante_id, grupo_id, pregunta_id, respuesta_texto } = respuesta;
    
    // Solo procesar preguntas sociométricas
    if (!PREGUNTAS_SOCIOMETRICAS[pregunta_id]) {
      return;
    }
    
    estadisticas.respuestasSociometricas++;
    
    // Parsear la respuesta JSON que contiene los IDs de estudiantes nominados
    let estudiantesNominados = [];
    try {
      if (respuesta_texto && respuesta_texto.startsWith('[')) {
        estudiantesNominados = JSON.parse(respuesta_texto);
      }
    } catch (error) {
      console.warn(`Error parseando respuesta sociométrica para estudiante ${estudiante_id}:`, error);
      return;
    }
    
    const tipoNominacion = PREGUNTAS_SOCIOMETRICAS[pregunta_id];
    if (!estadisticas.tiposNominacion[tipoNominacion]) {
      estadisticas.tiposNominacion[tipoNominacion] = 0;
    }
    
    // Crear nominaciones individuales
    estudiantesNominados.forEach(nominado_id => {
      nominaciones.push({
        estudiante_nominador_id: estudiante_id,
        estudiante_nominado_id: nominado_id,
        grupo_id: grupo_id,
        tipo_nominacion: tipoNominacion,
        pregunta_id: pregunta_id,
        fecha_nominacion: respuesta.fecha_respuesta || new Date().toISOString()
      });
      
      estadisticas.nominacionesGeneradas++;
      estadisticas.tiposNominacion[tipoNominacion]++;
    });
  });
  
  return { nominaciones, estadisticas };
}

/**
 * Calcula estadísticas sociométricas básicas
 */
function calcularEstadisticasSociometricas(estudiantes, nominaciones) {
  const estadisticasPorEstudiante = {};
  const estadisticasPorGrupo = {};
  
  // Inicializar estadísticas por estudiante
  estudiantes.forEach(estudiante => {
    estadisticasPorEstudiante[estudiante.id] = {
      id: estudiante.id,
      nombre: `${estudiante.nombre_estudiante} ${estudiante.apellido_estudiante}`,
      grupo_id: estudiante.grupo_id,
      elecciones_recibidas: 0,
      rechazos_recibidos: 0,
      agresiones_recibidas: 0,
      victimizaciones_recibidas: 0,
      colaboraciones_recibidas: 0,
      elecciones_realizadas: 0,
      rechazos_realizados: 0,
      agresiones_realizadas: 0,
      victimizaciones_realizadas: 0,
      colaboraciones_realizadas: 0
    };
    
    if (!estadisticasPorGrupo[estudiante.grupo_id]) {
      estadisticasPorGrupo[estudiante.grupo_id] = {
        grupo_id: estudiante.grupo_id,
        total_estudiantes: 0,
        total_nominaciones: 0,
        estudiantes: []
      };
    }
    
    estadisticasPorGrupo[estudiante.grupo_id].total_estudiantes++;
    estadisticasPorGrupo[estudiante.grupo_id].estudiantes.push(estudiante.id);
  });
  
  // Procesar nominaciones
  nominaciones.forEach(nominacion => {
    const { estudiante_nominador_id, estudiante_nominado_id, tipo_nominacion, grupo_id } = nominacion;
    
    // Contar nominaciones recibidas
    if (estadisticasPorEstudiante[estudiante_nominado_id]) {
      estadisticasPorEstudiante[estudiante_nominado_id][`${tipo_nominacion}es_recibidas`]++;
    }
    
    // Contar nominaciones realizadas
    if (estadisticasPorEstudiante[estudiante_nominador_id]) {
      estadisticasPorEstudiante[estudiante_nominador_id][`${tipo_nominacion}es_realizadas`]++;
    }
    
    // Contar por grupo
    if (estadisticasPorGrupo[grupo_id]) {
      estadisticasPorGrupo[grupo_id].total_nominaciones++;
    }
  });
  
  // Calcular estatus sociométrico
  Object.values(estadisticasPorGrupo).forEach(grupo => {
    const estudiantesGrupo = grupo.estudiantes.map(id => estadisticasPorEstudiante[id]);
    
    // Calcular promedios del grupo
    const promedioElecciones = estudiantesGrupo.reduce((sum, est) => sum + est.elecciones_recibidas, 0) / estudiantesGrupo.length;
    const promedioRechazos = estudiantesGrupo.reduce((sum, est) => sum + est.rechazos_recibidos, 0) / estudiantesGrupo.length;
    
    // Clasificar estudiantes
    estudiantesGrupo.forEach(estudiante => {
      const elecciones = estudiante.elecciones_recibidas;
      const rechazos = estudiante.rechazos_recibidos;
      
      if (elecciones > promedioElecciones && rechazos < promedioRechazos) {
        estudiante.estatus_sociometrico = 'popular';
      } else if (elecciones < promedioElecciones && rechazos > promedioRechazos) {
        estudiante.estatus_sociometrico = 'rechazado';
      } else if (elecciones < promedioElecciones && rechazos < promedioRechazos) {
        estudiante.estatus_sociometrico = 'aislado';
      } else if (elecciones > promedioElecciones && rechazos > promedioRechazos) {
        estudiante.estatus_sociometrico = 'controversial';
      } else {
        estudiante.estatus_sociometrico = 'promedio';
      }
      
      // Calcular nivel de riesgo
      const riesgoAcoso = (estudiante.agresiones_recibidas + estudiante.victimizaciones_recibidas) > 0 ? 'alto' : 'bajo';
      const riesgoSocial = estudiante.estatus_sociometrico === 'aislado' || estudiante.estatus_sociometrico === 'rechazado' ? 'alto' : 'bajo';
      
      estudiante.nivel_riesgo_acoso = riesgoAcoso;
      estudiante.nivel_riesgo_social = riesgoSocial;
    });
  });
  
  return { estadisticasPorEstudiante, estadisticasPorGrupo };
}

/**
 * Genera un resumen ejecutivo
 */
function generarResumenEjecutivo(estadisticasPorGrupo, estadisticasPorEstudiante) {
  const totalGrupos = Object.keys(estadisticasPorGrupo).length;
  const totalEstudiantes = Object.keys(estadisticasPorEstudiante).length;
  
  let estudiantesPopulares = 0;
  let estudiantesRechazados = 0;
  let estudiantesAislados = 0;
  let estudiantesControversiales = 0;
  let estudiantesRiesgoAlto = 0;
  
  Object.values(estadisticasPorEstudiante).forEach(estudiante => {
    switch (estudiante.estatus_sociometrico) {
      case 'popular': estudiantesPopulares++; break;
      case 'rechazado': estudiantesRechazados++; break;
      case 'aislado': estudiantesAislados++; break;
      case 'controversial': estudiantesControversiales++; break;
    }
    
    if (estudiante.nivel_riesgo_acoso === 'alto' || estudiante.nivel_riesgo_social === 'alto') {
      estudiantesRiesgoAlto++;
    }
  });
  
  const porcentajeRiesgo = (estudiantesRiesgoAlto / totalEstudiantes) * 100;
  
  return {
    resumen_general: `Análisis de ${totalEstudiantes} estudiantes en ${totalGrupos} grupos`,
    nivel_riesgo_general: porcentajeRiesgo > 30 ? 'alto' : porcentajeRiesgo > 15 ? 'medio' : 'bajo',
    indicadores_clave: {
      total_grupos: totalGrupos,
      total_estudiantes: totalEstudiantes,
      estudiantes_riesgo_alto: estudiantesRiesgoAlto,
      porcentaje_riesgo: Math.round(porcentajeRiesgo * 100) / 100
    },
    distribucion_sociometrica: {
      populares: estudiantesPopulares,
      rechazados: estudiantesRechazados,
      aislados: estudiantesAislados,
      controversiales: estudiantesControversiales,
      promedio: totalEstudiantes - estudiantesPopulares - estudiantesRechazados - estudiantesAislados - estudiantesControversiales
    },
    recomendaciones: [
      estudiantesRiesgoAlto > 0 ? `Intervenir con ${estudiantesRiesgoAlto} estudiantes en riesgo alto` : 'Mantener monitoreo preventivo',
      estudiantesAislados > 0 ? `Implementar actividades de integración para ${estudiantesAislados} estudiantes aislados` : 'Promover cohesión grupal',
      estudiantesRechazados > 0 ? `Desarrollar programas de apoyo para ${estudiantesRechazados} estudiantes rechazados` : 'Fortalecer clima positivo'
    ]
  };
}

/**
 * Función principal
 */
async function main() {
  console.log('🚀 Probando sistema optimizado con datos reales del BULL-S');
  console.log('=' .repeat(60));
  
  try {
    // 1. Cargar datos
    console.log('📚 Cargando estudiantes...');
    const rutaEstudiantes = path.join(__dirname, '..', 'respuestas bulls-s', 'estudiantes_rows.csv');
    const estudiantes = await leerCSV(rutaEstudiantes);
    console.log(`   ✅ ${estudiantes.length} estudiantes cargados`);
    
    console.log('📝 Cargando respuestas...');
    const rutaRespuestas = path.join(__dirname, '..', 'respuestas bulls-s', 'respuestas_rows.csv');
    const respuestas = await leerCSV(rutaRespuestas);
    console.log(`   ✅ ${respuestas.length} respuestas cargadas`);
    
    // 2. Procesar datos sociométricos
    console.log('🔄 Procesando datos sociométricas...');
    const { nominaciones, estadisticas } = procesarRespuestasSociometricas(respuestas);
    
    console.log('📊 Estadísticas de procesamiento:');
    console.log(`   - Total respuestas: ${estadisticas.totalRespuestas}`);
    console.log(`   - Respuestas sociométricas: ${estadisticas.respuestasSociometricas}`);
    console.log(`   - Nominaciones generadas: ${estadisticas.nominacionesGeneradas}`);
    console.log('   - Por tipo:');
    Object.entries(estadisticas.tiposNominacion).forEach(([tipo, cantidad]) => {
      console.log(`     * ${tipo}: ${cantidad}`);
    });
    
    // 3. Calcular estadísticas sociométricas
    console.log('📈 Calculando estadísticas sociométricas...');
    const { estadisticasPorEstudiante, estadisticasPorGrupo } = calcularEstadisticasSociometricas(estudiantes, nominaciones);
    
    // 4. Generar resumen ejecutivo
    console.log('📋 Generando resumen ejecutivo...');
    const resumen = generarResumenEjecutivo(estadisticasPorGrupo, estadisticasPorEstudiante);
    
    console.log('\n' + '=' .repeat(60));
    console.log('📊 RESUMEN EJECUTIVO');
    console.log('=' .repeat(60));
    console.log(`📝 ${resumen.resumen_general}`);
    console.log(`⚠️  Nivel de riesgo general: ${resumen.nivel_riesgo_general.toUpperCase()}`);
    
    console.log('\n🔢 Indicadores clave:');
    Object.entries(resumen.indicadores_clave).forEach(([indicador, valor]) => {
      console.log(`   - ${indicador.replace(/_/g, ' ')}: ${valor}`);
    });
    
    console.log('\n👥 Distribución sociométrica:');
    Object.entries(resumen.distribucion_sociometrica).forEach(([estatus, cantidad]) => {
      console.log(`   - ${estatus}: ${cantidad} estudiantes`);
    });
    
    console.log('\n💡 Recomendaciones:');
    resumen.recomendaciones.forEach((recomendacion, index) => {
      console.log(`   ${index + 1}. ${recomendacion}`);
    });
    
    // 5. Mostrar ejemplos de estudiantes por estatus
    console.log('\n' + '=' .repeat(60));
    console.log('👤 EJEMPLOS POR ESTATUS SOCIOMÉTRICO');
    console.log('=' .repeat(60));
    
    const estatusEjemplos = {};
    Object.values(estadisticasPorEstudiante).forEach(estudiante => {
      const estatus = estudiante.estatus_sociometrico;
      if (!estatusEjemplos[estatus]) {
        estatusEjemplos[estatus] = [];
      }
      if (estatusEjemplos[estatus].length < 3) {
        estatusEjemplos[estatus].push(estudiante);
      }
    });
    
    Object.entries(estatusEjemplos).forEach(([estatus, estudiantes]) => {
      console.log(`\n${estatus.toUpperCase()}:`);
      estudiantes.forEach(estudiante => {
        console.log(`   - ${estudiante.nombre}: ${estudiante.elecciones_recibidas} elecciones, ${estudiante.rechazos_recibidos} rechazos`);
      });
    });
    
    console.log('\n' + '=' .repeat(60));
    console.log('🎉 ¡Análisis completado exitosamente!');
    console.log('Los datos reales del BULL-S han sido procesados correctamente.');
    console.log('El sistema optimizado está listo para usar en producción.');
    
  } catch (error) {
    console.error('❌ Error durante el análisis:', error);
    process.exit(1);
  }
}

// Ejecutar si es llamado directamente
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export {
  procesarRespuestasSociometricas,
  calcularEstadisticasSociometricas,
  generarResumenEjecutivo
};