/**
 * Script para aplicar migraciones SQL directamente a Supabase
 * Aplica las funciones y vistas necesarias para las métricas de comparación
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function aplicarMigraciones() {
  console.log('🚀 APLICANDO MIGRACIONES SQL A SUPABASE');
  console.log('============================================================');

  try {
    // Lista de archivos de migración en orden
    const migraciones = [
      'supabase/migrations/20241220000002_create_sociometric_views.sql',
      'supabase/migrations/20241225000001_create_bulls_dashboard_views.sql',
      'supabase/migrations/20241225000002_create_bulls_metrics_functions.sql'
    ];

    for (const archivo of migraciones) {
      console.log(`\n📄 Aplicando: ${path.basename(archivo)}`);
      
      try {
        const sql = fs.readFileSync(archivo, 'utf8');
        
        // Dividir el SQL en statements individuales
        const statements = sql
          .split(';')
          .map(s => s.trim())
          .filter(s => s.length > 0 && !s.startsWith('--'));

        for (let i = 0; i < statements.length; i++) {
          const statement = statements[i];
          if (statement.length > 10) { // Evitar statements vacíos
            console.log(`   Ejecutando statement ${i + 1}/${statements.length}...`);
            
            const { error } = await supabase.rpc('exec_sql', {
              sql: statement + ';'
            });
            
            if (error) {
              console.log(`   ⚠️ Error en statement ${i + 1}: ${error.message}`);
              // Continuar con el siguiente statement
            } else {
              console.log(`   ✅ Statement ${i + 1} ejecutado correctamente`);
            }
          }
        }
        
        console.log(`✅ ${path.basename(archivo)} aplicado`);
        
      } catch (error) {
        console.log(`❌ Error leyendo ${archivo}: ${error.message}`);
      }
    }

    console.log('\n🔍 Verificando funciones creadas...');
    
    // Verificar que las funciones existen
    const funciones = [
      'calcular_comparativas',
      'calcular_panorama_curso',
      'calcular_sociograma'
    ];

    for (const funcion of funciones) {
      try {
        const { data, error } = await supabase.rpc(funcion);
        if (error) {
          console.log(`❌ Función ${funcion}: ${error.message}`);
        } else {
          console.log(`✅ Función ${funcion}: Disponible`);
        }
      } catch (error) {
        console.log(`❌ Función ${funcion}: No disponible`);
      }
    }

    console.log('\n🔍 Verificando vistas creadas...');
    
    // Verificar que las vistas existen
    const vistas = ['vw_roles', 'vw_cohesion', 'vw_victims', 'vw_aggressors'];
    
    for (const vista of vistas) {
      try {
        const { data, error } = await supabase
          .from(vista)
          .select('*')
          .limit(1);
          
        if (error) {
          console.log(`❌ Vista ${vista}: ${error.message}`);
        } else {
          console.log(`✅ Vista ${vista}: Disponible`);
        }
      } catch (error) {
        console.log(`❌ Vista ${vista}: No disponible`);
      }
    }

  } catch (error) {
    console.error('❌ Error general:', error.message);
  }

  console.log('\n✅ PROCESO DE MIGRACIÓN COMPLETADO');
}

aplicarMigraciones();