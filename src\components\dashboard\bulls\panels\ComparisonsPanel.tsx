import React, { useState } from 'react';
import { ComparisonsData } from '../../../../types/bulls-dashboard';
import { Card, CardContent, CardHeader, CardTitle } from '../../../ui/card';
import { Badge } from '../../../ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '../../../ui/tabs';
import { Users, TrendingUp, TrendingDown, Minus, BookOpen, User, UserCheck } from 'lucide-react';
import { 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend,
  LineChart,
  Line,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  Cell,
  PieChart,
  Pie
} from 'recharts';

interface ComparisonsPanelProps {
  data: ComparisonsData;
}

export const ComparisonsPanel: React.FC<ComparisonsPanelProps> = ({ data }) => {
  const [selectedMetric, setSelectedMetric] = useState<'bullying' | 'cohesion' | 'safety'>('bullying');

  const COLORS = {
    primary: '#3b82f6',
    secondary: '#ef4444',
    tertiary: '#f59e0b',
    quaternary: '#10b981',
    quinary: '#8b5cf6',
    senary: '#ec4899'
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <Minus className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTrendColor = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return 'text-green-600 bg-green-100';
      case 'down':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getRiskLevel = (percentage: number): { label: string; color: string } => {
    if (percentage >= 30) return { label: 'Alto', color: 'text-red-600' };
    if (percentage >= 15) return { label: 'Medio', color: 'text-yellow-600' };
    return { label: 'Bajo', color: 'text-green-600' };
  };

  // Preparar datos para el radar de comparación
  const radarData = [
    {
      metric: 'Nivel de Bullying',
      ...(data.courseComparisons?.reduce((acc, course) => {
        acc[course.course] = course.studentsAtRisk;
        return acc;
      }, {} as Record<string, number>) || {})
    },
    {
      metric: 'Cohesión',
      ...(data.courseComparisons?.reduce((acc, course) => {
        acc[course.course] = course.cohesion;
        return acc;
      }, {} as Record<string, number>) || {})
    },
    {
      metric: 'Seguridad',
      ...(data.courseComparisons?.reduce((acc, course) => {
        acc[course.course] = course.safety;
        return acc;
      }, {} as Record<string, number>) || {})
    },
    {
      metric: 'Participación',
      ...(data.courseComparisons?.reduce((acc, course) => {
        acc[course.course] = 75; // Valor por defecto
        return acc;
      }, {} as Record<string, number>) || {})
    }
  ];

  // Usar datos reales de evolución temporal desde props
  const evolutionData = React.useMemo(() => {
    if (!data.evolutionData || data.evolutionData.length === 0) {
      return [];
    }
    
    // Transformar datos de evolución para el gráfico
    const periods = data.evolutionData[0]?.data?.map(d => d.period) || [];
    
    return periods.map(period => {
      const periodData: any = { month: period };
      data.evolutionData?.forEach(courseEvolution => {
        const value = courseEvolution.data.find(d => d.period === period)?.value || 0;
        periodData[courseEvolution.course] = value;
      });
      return periodData;
    });
  }, [data.evolutionData]);

  return (
    <div className="space-y-6">
      {/* Resumen ejecutivo */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Curso con Mayor Riesgo</p>
                <p className="font-semibold text-lg">
                  {data.courseComparisons && data.courseComparisons.length > 0
                    ? data.courseComparisons.reduce((max, course) =>
                        course.studentsAtRisk > max.studentsAtRisk ? course : max
                      ).course
                    : 'Sin datos'
                  }
                </p>
              </div>
              <BookOpen className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Mejor Cohesión</p>
                <p className="font-semibold text-lg">
                  {data.courseComparisons && data.courseComparisons.length > 0
                    ? data.courseComparisons.reduce((max, course) =>
                        course.cohesion > max.cohesion ? course : max
                      ).course
                    : 'Sin datos'
                  }
                </p>
              </div>
              <Users className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Brecha de Género</p>
                <p className="font-semibold text-lg">
                  {data.genders && data.genders.length >= 2
                    ? Math.abs(data.genders[0]?.bullyingLevel - data.genders[1]?.bullyingLevel).toFixed(1)
                    : '0.0'
                  }%
                </p>
              </div>
              <User className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="courses" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="courses">Por Cursos</TabsTrigger>
          <TabsTrigger value="genders">Por Género</TabsTrigger>
          <TabsTrigger value="roles">Por Roles</TabsTrigger>
          <TabsTrigger value="evolution">Evolución</TabsTrigger>
        </TabsList>

        <TabsContent value="courses" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BookOpen className="h-5 w-5 mr-2" />
                  Comparativa por Cursos
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex space-x-2 mb-4">
                    {(['bullying', 'cohesion', 'safety'] as const).map((metric) => (
                      <button
                        key={metric}
                        onClick={() => setSelectedMetric(metric)}
                        className={`px-3 py-1 rounded text-sm ${
                          selectedMetric === metric
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                        }`}
                      >
                        {metric === 'bullying' ? 'Bullying' : metric === 'cohesion' ? 'Cohesión' : 'Seguridad'}
                      </button>
                    ))}
                  </div>
                  
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={data.courseComparisons || []}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="course" />
                      <YAxis />
                      <Tooltip 
                        formatter={(value) => [
                          `${value}%`, 
                          selectedMetric === 'bullying' ? 'Nivel de Bullying' : 
                          selectedMetric === 'cohesion' ? 'Cohesión' : 'Seguridad'
                        ]}
                      />
                      <Bar 
                        dataKey={selectedMetric === 'bullying' ? 'studentsAtRisk' : selectedMetric}
                      >
                        {(data.courseComparisons || []).map((entry, index) => (
                          <Cell 
                            key={`cell-${index}`} 
                            fill={Object.values(COLORS)[index % Object.values(COLORS).length]} 
                          />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Análisis Detallado por Curso</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {(data.courseComparisons || []).map((course, index) => {
                    const riskLevel = getRiskLevel(course.studentsAtRisk);
                    return (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-semibold text-lg">{course.course}</h4>
                          <div className="flex items-center space-x-2">
                            {getTrendIcon(course.trend)}
                            <Badge className={getTrendColor(course.trend)}>
                              {course.trend === 'up' ? 'Mejorando' : 
                               course.trend === 'down' ? 'Empeorando' : 'Estable'}
                            </Badge>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Estudiantes:</span>
                            <span className="font-medium ml-2">25</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Participación:</span>
                            <span className="font-medium ml-2">75%</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Bullying:</span>
                            <span className={`font-medium ml-2 ${riskLevel.color}`}>
                              {course.studentsAtRisk}% ({riskLevel.label})
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-600">Cohesión:</span>
                            <span className="font-medium ml-2">{course.cohesion}%</span>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Radar de Comparación Multidimensional</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <RadarChart data={radarData}>
                  <PolarGrid />
                  <PolarAngleAxis dataKey="metric" tick={{ fontSize: 12 }} />
                  <PolarRadiusAxis 
                    angle={90} 
                    domain={[0, 100]} 
                    tick={{ fontSize: 10 }}
                    tickCount={5}
                  />
                  {(data.courseComparisons || []).map((course, index) => (
                    <Radar
                      key={course.course}
                      name={course.course}
                      dataKey={course.course}
                      stroke={Object.values(COLORS)[index % Object.values(COLORS).length]}
                      fill={Object.values(COLORS)[index % Object.values(COLORS).length]}
                      fillOpacity={0.1}
                      strokeWidth={2}
                      dot={{ r: 4, fill: Object.values(COLORS)[index % Object.values(COLORS).length] }}
                    />
                  ))}
                  <Legend />
                  <Tooltip 
                    formatter={(value, name) => [`${value}%`, name]}
                    labelFormatter={(label) => `Métrica: ${label}`}
                  />
                </RadarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="genders" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  Comparativa por Género
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={data.genders || []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="gender" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="bullyingLevel" fill={COLORS.secondary} name="Nivel de Bullying (%)" />
                    <Bar dataKey="cohesion" fill={COLORS.quaternary} name="Cohesión (%)" />
                    <Bar dataKey="safety" fill={COLORS.primary} name="Seguridad (%)" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Análisis de Brechas de Género</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {(data.genders || []).map((gender, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-semibold text-lg">
                          {gender.gender === 'M' ? 'Masculino' : 'Femenino'}
                        </h4>
                        <Badge className={`${
                          gender.gender === 'M' ? 'bg-blue-100 text-blue-600' : 'bg-pink-100 text-pink-600'
                        }`}>
                          {gender.totalStudents} estudiantes
                        </Badge>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">Nivel de Bullying:</span>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{gender.bullyingLevel}%</span>
                            <div className="w-20 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-red-500 h-2 rounded-full"
                                style={{ width: `${gender.bullyingLevel}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">Cohesión:</span>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{gender.cohesion}%</span>
                            <div className="w-20 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-green-500 h-2 rounded-full"
                                style={{ width: `${gender.cohesion}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">Seguridad:</span>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{gender.safety}%</span>
                            <div className="w-20 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-blue-500 h-2 rounded-full"
                                style={{ width: `${gender.safety}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="roles" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <UserCheck className="h-5 w-5 mr-2" />
                  Distribución por Roles
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={data.roles || []}
                      cx="50%"
                      cy="50%"
                      outerRadius={100}
                      dataKey="count"
                      label={({ role, percentage }) => `${role} (${percentage}%)`}
                    >
                      {(data.roles || []).map((entry, index) => (
                        <Cell 
                          key={`cell-${index}`} 
                          fill={Object.values(COLORS)[index % Object.values(COLORS).length]} 
                        />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [value, 'Estudiantes']} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Análisis Detallado por Rol</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {(data.roles || []).map((role, index) => {
                    const roleColor = role.role === 'Agresor' ? 'text-red-600 bg-red-100' :
                                     role.role === 'Víctima' ? 'text-orange-600 bg-orange-100' :
                                     'text-green-600 bg-green-100';
                    
                    return (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-semibold">{role.role}</h4>
                          <Badge className={roleColor}>
                            {role.count} estudiantes
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Porcentaje:</span>
                            <span className="font-medium ml-2">{role.percentage}%</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Riesgo:</span>
                            <span className={`font-medium ml-2 ${
                              role.riskLevel === 'Alto' ? 'text-red-600' :
                              role.riskLevel === 'Medio' ? 'text-yellow-600' : 'text-green-600'
                            }`}>
                              {role.riskLevel}
                            </span>
                          </div>
                        </div>
                        
                        <div className="mt-3">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className={`h-2 rounded-full ${
                                role.role === 'Agresor' ? 'bg-red-500' :
                                role.role === 'Víctima' ? 'bg-orange-500' : 'bg-green-500'
                              }`}
                              style={{ width: `${role.percentage}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="evolution" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Evolución Temporal por Curso
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={evolutionData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis label={{ value: 'Nivel de Bullying (%)', angle: -90, position: 'insideLeft' }} />
                  <Tooltip formatter={(value) => [`${value}%`, 'Nivel de Bullying']} />
                  <Legend />
                  {(data.courseComparisons || []).map((course, index) => (
                    <Line
                      key={course.course}
                      type="monotone"
                      dataKey={course.course}
                      stroke={Object.values(COLORS)[index]}
                      strokeWidth={3}
                      dot={{ r: 6 }}
                    />
                  ))}
                </LineChart>
              </ResponsiveContainer>
              <div className="mt-4 text-sm text-gray-600">
                <p>* Evolución basada en datos reales del período académico actual.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};