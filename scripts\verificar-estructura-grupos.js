import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function verificarEstructuraGrupos() {
  try {
    console.log('Verificando estructura de la tabla grupos...');
    
    // Intentar obtener un registro para ver la estructura
    const { data, error } = await supabase
      .from('grupos')
      .select('*')
      .limit(1);
    
    if (error) {
      console.log('Error al consultar grupos:', error);
      return;
    }
    
    if (data && data.length > 0) {
      console.log('Columnas disponibles en grupos:', Object.keys(data[0]));
      console.log('Primer registro:', data[0]);
    } else {
      console.log('La tabla grupos está vacía');
      
      // Intentar insertar un registro de prueba para ver qué columnas acepta
      console.log('Probando inserción con columnas básicas...');
      const { data: insertData, error: insertError } = await supabase
        .from('grupos')
        .insert({
          nombre: 'TEST'
        })
        .select();
      
      if (insertError) {
        console.log('Error al insertar registro básico:', insertError);
        
        // Probar con institucion_id
        console.log('Probando con institucion_id...');
        const { data: insertData2, error: insertError2 } = await supabase
          .from('grupos')
          .insert({
            nombre: 'TEST2',
            institucion_id: '00000000-0000-0000-0000-000000000001'
          })
          .select();
          
        if (insertError2) {
          console.log('Error con institucion_id:', insertError2);
        } else {
          console.log('Éxito con institucion_id. Columnas:', Object.keys(insertData2[0]));
          // Limpiar
          await supabase.from('grupos').delete().eq('id', insertData2[0].id);
        }
      } else {
        console.log('Registro básico insertado exitosamente. Columnas:', Object.keys(insertData[0]));
        // Limpiar
        await supabase.from('grupos').delete().eq('id', insertData[0].id);
      }
    }
    
  } catch (error) {
    console.error('Error general:', error);
  }
}

verificarEstructuraGrupos();