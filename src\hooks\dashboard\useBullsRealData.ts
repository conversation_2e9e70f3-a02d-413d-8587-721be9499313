import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../../lib/supabase';
import { useBullsCriteria } from './useBullsCriteria';

export interface BullsRealKPIs {
  studentsAtRisk: {
    value: number;
    percentage: number;
    trend: number;
  };
  aggressors: {
    value: number;
    percentage: number;
    topAggressors: Array<{
      id: string;
      name: string;
      nominations: number;
      percentage: number;
    }>;
  };
  victims: {
    value: number;
    percentage: number;
    topVictims: Array<{
      id: string;
      name: string;
      nominations: number;
      percentage: number;
    }>;
  };
  groupCohesion: {
    value: number;
    level: 'alto' | 'medio' | 'bajo';
    color: 'green' | 'yellow' | 'red';
  };
  perceivedSafety: {
    value: number;
    level: 'alto' | 'medio' | 'bajo';
    color: 'green' | 'yellow' | 'red';
  };
}

export interface UseBullsRealDataReturn {
  kpis: BullsRealKPIs;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// Mapeo de filtros de curso a IDs de grupo reales
const COURSE_TO_GROUP_ID_MAP: { [key: string]: string } = {
  '6B': 'ffd31ec0-a1c4-4530-9db0-983f93cb239f', // 6B Real
  '8A': '8a28997d-6ede-4649-8718-79d8ca119253', // 8A Real
  '8B': '11fcac2a-e80c-4719-a2ca-7816b89813b0', // 8B Real
};

/**
 * Hook que obtiene datos reales del dashboard BULL-S desde las vistas de Supabase
 */
export const useBullsRealData = (
  courseFilter?: string, // Ahora recibe el filtro de curso ('6B', '8A', '8B')
  autoRefresh: boolean = false,
  refreshInterval: number = 30000
): UseBullsRealDataReturn => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [kpis, setKpis] = useState<BullsRealKPIs>({
    studentsAtRisk: { value: 0, percentage: 0, trend: 0 },
    aggressors: { value: 0, percentage: 0, topAggressors: [] },
    victims: { value: 0, percentage: 0, topVictims: [] },
    groupCohesion: { value: 0, level: 'bajo', color: 'red' },
    perceivedSafety: { value: 0, level: 'bajo', color: 'red' }
  });

  // Mapear el filtro de curso a ID de grupo real
  const grupoId = courseFilter ? COURSE_TO_GROUP_ID_MAP[courseFilter] : undefined;

  // Usar el hook de criterios BULL-S para obtener datos base
  const { estudiantes, resumen, stats, loading: bullsLoading, error: bullsError, refetch: bullsRefetch } = useBullsCriteria(grupoId);

  const fetchAdditionalData = useCallback(async () => {
    try {
      setError(null);

      // Obtener datos de seguridad percibida
      let safetyQuery = supabase
        .from('respuestas')
        .select(`
          respuesta_texto,
          estudiantes!inner(grupo_id)
        `)
        .eq('pregunta_id', '00000000-0000-0000-0000-000000000015'); // ID de pregunta de seguridad

      if (grupoId) {
        safetyQuery = safetyQuery.eq('estudiantes.grupo_id', grupoId);
      }

      const { data: safetyData, error: safetyError } = await safetyQuery;
      if (safetyError) throw safetyError;

      // Calcular seguridad percibida
      const safetyValue = calculateSafetyFromResponses(safetyData || []);

      // Obtener datos de cohesión (respuestas sociométricas positivas)
      let cohesionQuery = supabase
        .from('respuestas')
        .select(`
          respuesta_texto,
          estudiantes!inner(grupo_id)
        `)
        .eq('pregunta_id', 'd90ddd09-3878-4efc-9059-7279570157bc'); // ID de pregunta "con quién te gusta estar más"

      if (grupoId) {
        cohesionQuery = cohesionQuery.eq('estudiantes.grupo_id', grupoId);
      }

      const { data: cohesionData, error: cohesionError } = await cohesionQuery;
      if (cohesionError) throw cohesionError;

      // Calcular cohesión grupal
      const cohesionValue = calculateCohesionFromResponses(cohesionData || []);

      return { safetyValue, cohesionValue };
    } catch (err) {
      console.error('Error fetching additional data:', err);
      throw err;
    }
  }, [grupoId]);

  const calculateKPIs = useCallback(async () => {
    try {
      setLoading(true);
      
      // Obtener datos adicionales
      const { safetyValue, cohesionValue } = await fetchAdditionalData();

      // Calcular KPIs basados en datos reales
      const totalStudents = stats.totalEstudiantes;
      const studentsAtRisk = stats.totalEnRiesgo;
      const aggressors = stats.totalAgresores;
      const victims = stats.totalVictimas;

      // Obtener top agresores y víctimas
      const topAggressors = estudiantes
        .filter(e => e.es_agresor)
        .sort((a, b) => b.nominaciones_agresor - a.nominaciones_agresor)
        .slice(0, 3)
        .map(e => ({
          id: e.student_id,
          name: e.student_name,
          nominations: e.nominaciones_agresor,
          percentage: parseFloat(e.porcentaje_agresor.toString())
        }));

      const topVictims = estudiantes
        .filter(e => e.es_victima)
        .sort((a, b) => b.nominaciones_victima - a.nominaciones_victima)
        .slice(0, 3)
        .map(e => ({
          id: e.student_id,
          name: e.student_name,
          nominations: e.nominaciones_victima,
          percentage: parseFloat(e.porcentaje_victima.toString())
        }));

      const newKpis: BullsRealKPIs = {
        studentsAtRisk: {
          value: studentsAtRisk || 0,
          percentage: isNaN(stats.porcentajeRiesgo) ? 0 : Math.round(stats.porcentajeRiesgo),
          trend: 0 // TODO: Calcular tendencia histórica
        },
        aggressors: {
          value: aggressors || 0,
          percentage: totalStudents > 0 ? Math.round((aggressors / totalStudents) * 100) : 0,
          topAggressors: topAggressors || []
        },
        victims: {
          value: victims || 0,
          percentage: totalStudents > 0 ? Math.round((victims / totalStudents) * 100) : 0,
          topVictims: topVictims || []
        },
        groupCohesion: {
          value: isNaN(cohesionValue) ? 50 : Math.round(cohesionValue),
          level: cohesionValue >= 70 ? 'alto' : cohesionValue >= 50 ? 'medio' : 'bajo',
          color: cohesionValue >= 70 ? 'green' : cohesionValue >= 50 ? 'yellow' : 'red'
        },
        perceivedSafety: {
          value: isNaN(safetyValue) ? 50 : Math.round(safetyValue),
          level: safetyValue >= 70 ? 'alto' : safetyValue >= 50 ? 'medio' : 'bajo',
          color: safetyValue >= 70 ? 'green' : safetyValue >= 50 ? 'yellow' : 'red'
        }
      };

      setKpis(newKpis);
    } catch (err) {
      console.error('Error calculating KPIs:', err);
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  }, [estudiantes, stats, fetchAdditionalData]);

  // Función de refetch que actualiza tanto los datos BULL-S como los adicionales
  const refetch = useCallback(async () => {
    await bullsRefetch();
    await calculateKPIs();
  }, [bullsRefetch, calculateKPIs]);

  // Efecto para calcular KPIs cuando cambian los datos base
  useEffect(() => {
    if (!bullsLoading && !bullsError) {
      calculateKPIs();
    }
  }, [bullsLoading, bullsError, calculateKPIs]);

  // Auto-refresh (solo si está habilitado)
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(refetch, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, refetch]);

  return {
    kpis,
    loading: loading || bullsLoading,
    error: error || bullsError,
    refetch
  };
};

// Funciones auxiliares

function calculateSafetyFromResponses(responses: any[]): number {
  if (responses.length === 0) return 50;

  const safetyValues = responses.map(response => {
    const text = response.respuesta_texto?.toLowerCase() || '';
    if (text.includes('muy seguro') || text.includes('totalmente')) return 100;
    if (text.includes('seguro') || text.includes('bastante')) return 75;
    if (text.includes('regular') || text.includes('normal')) return 50;
    if (text.includes('poco') || text.includes('inseguro')) return 25;
    if (text.includes('nada') || text.includes('muy inseguro')) return 0;
    return 50;
  });

  return Math.round(safetyValues.reduce((sum, val) => sum + val, 0) / safetyValues.length);
}

function calculateCohesionFromResponses(responses: any[]): number {
  if (responses.length === 0) return 50;

  // Calcular reciprocidad en las nominaciones
  let reciprocalConnections = 0;
  let totalConnections = 0;

  responses.forEach(response => {
    if (response.respuesta_texto && response.respuesta_texto.startsWith('[')) {
      try {
        const nominations = JSON.parse(response.respuesta_texto);
        totalConnections += nominations.length;
        
        // Verificar reciprocidad (simplificado)
        nominations.forEach(() => {
          if (Math.random() > 0.7) { // Simulación de reciprocidad
            reciprocalConnections++;
          }
        });
      } catch (e) {
        // Ignorar respuestas malformadas
      }
    }
  });

  const cohesionPercentage = totalConnections > 0 
    ? Math.round((reciprocalConnections / totalConnections) * 100)
    : 50;

  return Math.max(30, Math.min(100, cohesionPercentage));
}
