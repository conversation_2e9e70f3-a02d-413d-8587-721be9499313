const fs = require('fs');
const path = require('path');

// Función para generar reporte de estudiantes con códigos únicos
function generarReporteEstudiantesCodigos() {
  try {
    console.log('Iniciando generación de reporte de estudiantes con códigos únicos...');
    
    // Leer archivo de estudiantes
    const estudiantesPath = path.join(__dirname, 'respuestas bulls-s', 'estudiantes_rows.csv');
    const estudiantesData = fs.readFileSync(estudiantesPath, 'utf8');
    
    // Procesar datos de estudiantes
    const lineas = estudiantesData.split('\n').filter(linea => linea.trim());
    const headers = lineas[0].split(',');
    
    // Encontrar índices de columnas
    const nombreIdx = headers.indexOf('nombre_estudiante');
    const apellidoIdx = headers.indexOf('apellido_estudiante');
    const codigoIdx = headers.indexOf('codigo_anonimizado');
    const gradoIdx = headers.indexOf('grado');
    
    // Procesar estudiantes
    const estudiantes = [];
    
    for (let i = 1; i < lineas.length; i++) {
      const campos = lineas[i].split(',');
      
      if (campos.length >= Math.max(nombreIdx, apellidoIdx, codigoIdx, gradoIdx) + 1) {
        const estudiante = {
          nombre_completo: `${campos[nombreIdx]} ${campos[apellidoIdx]}`,
          codigo_unico: campos[codigoIdx],
          grado: campos[gradoIdx]
        };
        
        estudiantes.push(estudiante);
      }
    }
    
    // Organizar por grados
    const estudiantesPorGrado = {
      '6B': [],
      '8A': [],
      '8B': []
    };
    
    estudiantes.forEach(estudiante => {
      if (estudiantesPorGrado[estudiante.grado]) {
        estudiantesPorGrado[estudiante.grado].push(estudiante);
      }
    });
    
    // Ordenar por código dentro de cada grado
    Object.keys(estudiantesPorGrado).forEach(grado => {
      estudiantesPorGrado[grado].sort((a, b) => a.codigo_unico.localeCompare(b.codigo_unico));
    });
    
    // Generar fecha actual
    const fechaActual = new Date().toISOString().split('T')[0];
    
    // Crear estructura del reporte
    const reporte = {
      metadata: {
        fecha_generacion: new Date().toISOString(),
        titulo: "Reporte de Estudiantes con Códigos Únicos",
        descripcion: "Lista de todos los estudiantes organizados por grado con sus códigos únicos",
        total_estudiantes: estudiantes.length
      },
      grados: []
    };
    
    // Agregar datos por grado
    ['6B', '8A', '8B'].forEach(grado => {
      const estudiantesGrado = estudiantesPorGrado[grado];
      
      reporte.grados.push({
        grado: grado,
        total_estudiantes: estudiantesGrado.length,
        estudiantes: estudiantesGrado.map(est => ({
          nombre_completo: est.nombre_completo,
          codigo_unico: est.codigo_unico
        }))
      });
    });
    
    // Generar archivo JSON
    const jsonPath = path.join(__dirname, `reporte-estudiantes-codigos-${fechaActual}.json`);
    fs.writeFileSync(jsonPath, JSON.stringify(reporte, null, 2), 'utf8');
    
    // Generar archivo CSV
    const csvPath = path.join(__dirname, `reporte-estudiantes-codigos-${fechaActual}.csv`);
    let csvContent = 'Grado,Codigo_Unico,Nombre_Completo\n';
    
    reporte.grados.forEach(grado => {
      grado.estudiantes.forEach(estudiante => {
        csvContent += `${grado.grado},${estudiante.codigo_unico},"${estudiante.nombre_completo}"\n`;
      });
    });
    
    fs.writeFileSync(csvPath, csvContent, 'utf8');
    
    // Generar resumen en texto
    const txtPath = path.join(__dirname, `resumen-estudiantes-codigos-${fechaActual}.txt`);
    let txtContent = `REPORTE DE ESTUDIANTES CON CÓDIGOS ÚNICOS\n`;
    txtContent += `Fecha de generación: ${new Date().toLocaleString('es-ES')}\n\n`;
    
    txtContent += `RESUMEN GENERAL:\n`;
    txtContent += `- Total de estudiantes: ${estudiantes.length}\n\n`;
    
    reporte.grados.forEach(grado => {
      txtContent += `GRADO ${grado.grado}:\n`;
      txtContent += `- Total de estudiantes: ${grado.total_estudiantes}\n`;
      txtContent += `- Códigos: ${grado.estudiantes[0]?.codigo_unico} - ${grado.estudiantes[grado.estudiantes.length - 1]?.codigo_unico}\n\n`;
      
      txtContent += `Lista de estudiantes:\n`;
      grado.estudiantes.forEach(estudiante => {
        txtContent += `  ${estudiante.codigo_unico} - ${estudiante.nombre_completo}\n`;
      });
      txtContent += `\n`;
    });
    
    fs.writeFileSync(txtPath, txtContent, 'utf8');
    
    console.log('\n=== REPORTE GENERADO EXITOSAMENTE ===');
    console.log(`Archivos generados:`);
    console.log(`- JSON: ${jsonPath}`);
    console.log(`- CSV: ${csvPath}`);
    console.log(`- TXT: ${txtPath}`);
    
    console.log('\n=== RESUMEN POR GRADOS ===');
    reporte.grados.forEach(grado => {
      console.log(`${grado.grado}: ${grado.total_estudiantes} estudiantes`);
    });
    
    console.log(`\nTotal general: ${estudiantes.length} estudiantes`);
    
  } catch (error) {
    console.error('Error al generar el reporte:', error);
    throw error;
  }
}

// Ejecutar la función
generarReporteEstudiantesCodigos();