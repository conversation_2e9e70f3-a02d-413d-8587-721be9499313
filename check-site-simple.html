<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificador de Sitio BULL-S</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .url {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Verificador de Sitio BULL-S</h1>
        
        <div class="info">
            <strong>URL del sitio:</strong> 
            <span class="url">https://activa-tumente.github.io/Bull-S/</span>
        </div>

        <button onclick="checkSite()">Verificar Sitio</button>
        
        <div id="results"></div>

        <h2>✅ Problemas Resueltos</h2>
        <ul>
            <li><strong>Favicon 404:</strong> Reemplazado favicon.ico inválido con favicon.svg funcional</li>
            <li><strong>Referencias de archivos:</strong> Actualizadas las rutas en index.html</li>
            <li><strong>Build optimizado:</strong> Configuración simplificada para mejor rendimiento</li>
            <li><strong>Assets limpios:</strong> Build limpio con rutas correctas</li>
        </ul>

        <h2>🔧 Cambios Implementados</h2>
        <ul>
            <li>✅ Creado favicon.svg con branding BULL-S</li>
            <li>✅ Actualizado index.html para usar favicon.svg</li>
            <li>✅ Simplificada configuración de Vite</li>
            <li>✅ Eliminados chunks manuales complejos</li>
            <li>✅ Build limpio y optimizado</li>
        </ul>

        <h2>📋 Instrucciones</h2>
        <ol>
            <li>Haz clic en "Verificar Sitio" para probar la conectividad</li>
            <li>Si el sitio carga correctamente, los errores 404 deberían estar resueltos</li>
            <li>El favicon ahora debería aparecer correctamente en la pestaña del navegador</li>
            <li>Todos los assets deberían cargar sin errores 404</li>
        </ol>
    </div>

    <script>
        async function checkSite() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="info">🔍 Verificando sitio...</div>';

            try {
                // Verificar sitio principal
                const response = await fetch('https://activa-tumente.github.io/Bull-S/', {
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                
                resultsDiv.innerHTML = `
                    <div class="success">
                        ✅ Sitio accesible correctamente<br>
                        🔗 <a href="https://activa-tumente.github.io/Bull-S/" target="_blank">Abrir sitio</a>
                    </div>
                    <div class="info">
                        <strong>Estado:</strong> Los errores 404 deberían estar resueltos<br>
                        <strong>Favicon:</strong> Ahora usando favicon.svg<br>
                        <strong>Assets:</strong> Rutas corregidas con base /Bull-S/
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">
                        ❌ Error al verificar el sitio: ${error.message}<br>
                        Esto puede ser normal debido a CORS. Intenta abrir el sitio directamente.
                    </div>
                    <div class="info">
                        🔗 <a href="https://activa-tumente.github.io/Bull-S/" target="_blank">Abrir sitio manualmente</a>
                    </div>
                `;
            }
        }

        // Verificar automáticamente al cargar la página
        window.onload = function() {
            setTimeout(checkSite, 1000);
        };
    </script>
</body>
</html>
