{"name": "bulls-analysis-platform", "private": true, "version": "2.0.0", "type": "module", "homepage": "https://activa-tumente.github.io", "description": "Sistema completo de análisis sociométrico para la detección y prevención de bullying escolar basado en el Test BULL-S", "author": "BULL-S Development Team", "license": "MIT", "keywords": ["bullying", "sociometric", "analysis", "education", "react", "typescript"], "scripts": {"dev": "npx vite --port 3001", "build": "npx vite build && npm run copy-404", "copy-404": "copy dist\\index.html dist\\404.html", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "npx vite preview", "type-check": "tsc --noEmit", "clean": "rm -rf dist node_modules/.vite", "start": "npm run dev", "build:analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html", "crear-usuarios": "node scripts/crear_usuarios_prueba.js", "actualizar-estudiantes": "node scripts/actualizar_estudiantes_6b.js", "crear-grupo-6b": "node scripts/crear_grupo_6b_y_estudiantes.js", "crear-estudiantes-6b": "node scripts/crear_estudiantes_6b_simple.js", "crear-estudiantes-8a-8b": "node scripts/crear_estudiantes_8a_8b_lasalle.js", "db:migrate": "echo 'Ejecutar migraciones SQL en Supabase Dashboard'", "db:seed": "echo 'Poblar base de datos con datos de prueba'", "test": "echo '<PERSON><PERSON><PERSON> no configuradas aún'", "test:coverage": "echo 'Cobertura de pruebas no configurada aún'", "check-deployment": "node scripts/check-deployment.js", "test-build": "node scripts/test-build.js", "deploy-preview": "npm run build && npx vite preview --port 4173", "predeploy": "npm run build", "deploy": "gh-pages -d dist -r https://github.com/activa-tumente/activa-tumente.github.io.git"}, "dependencies": {"@emotion/is-prop-valid": "^1.3.1", "@rollup/rollup-win32-x64-msvc": "^4.39.0", "@supabase/supabase-js": "^2.52.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "csv-parser": "^3.2.0", "date-fns": "^4.1.0", "framer-motion": "^12.6.3", "gh-pages": "^6.3.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.487.0", "node-fetch": "^3.3.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.30.0", "react-select": "^5.10.1", "recharts": "^2.15.4", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/js": "^9.23.0", "@types/node": "^20.17.30", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "@types/react-select": "^5.0.1", "@types/recharts": "^1.8.29", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "dotenv": "^16.6.1", "eslint": "^9.23.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "rollup": "^4.39.0", "tailwindcss": "^3.4.17", "terser": "^5.43.1", "typescript": "^5.8.2", "typescript-eslint": "^8.29.0", "vite": "^6.2.5"}}