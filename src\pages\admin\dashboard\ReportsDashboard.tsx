import React from 'react';
import { Download, Printer } from 'lucide-react';
import { DashboardLayout } from '../../../components/dashboard/layouts/DashboardLayout';
import { DashboardGrid, GridItem } from '../../../components/dashboard/layouts/DashboardGrid';
import { ChartWidget } from '../../../components/dashboard/widgets/ChartWidget';
import { TableWidget } from '../../../components/dashboard/widgets/TableWidget';
import { useDashboardData } from '../../../hooks/dashboard/useDashboardData';
import { useDashboardFilters } from '../../../hooks/dashboard/useDashboardFilters';
import { useDashboardMetrics } from '../../../hooks/dashboard/useDashboardMetrics';
import { DateRangeFilter } from '../../../components/dashboard/filters/DateRangeFilter';
import { InstitutionFilter } from '../../../components/dashboard/filters/InstitutionFilter';
import { Button } from '../../../components/ui/button';

export const ReportsDashboard: React.FC = () => {
  const { filters, updateFilter, setDateRange, clearFilters } = useDashboardFilters();
  const { data, loading, error, lastUpdated, refreshData } = useDashboardData(filters);
  const { chartData } = useDashboardMetrics(data);

  const handleDateRangeChange = (startDate: string, endDate: string) => {
    setDateRange(startDate, endDate);
  };

  const handleDateClear = () => {
    clearFilters();
  };

  const handleExport = (format: 'pdf' | 'excel') => {
    // Implement export functionality
    console.log(`Exporting to ${format}...`);
  };

  const handlePrint = () => {
    window.print();
  };

  if (error) {
    return (
      <DashboardLayout
        title="Reportes Visuales"
        subtitle="Visualizaciones y reportes descargables"
        onRefresh={refreshData}
        loading={loading}
      >
        <div className="text-center py-8">
          <div className="text-red-500">Error: {error}</div>
          <Button onClick={refreshData} className="mt-4">
            Reintentar
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Reportes Visuales"
      subtitle="Visualizaciones y reportes descargables"
      onRefresh={refreshData}
      lastUpdated={lastUpdated}
      loading={loading}
      actions={
        <div className="flex items-center space-x-4">
          <DateRangeFilter
            startDate={filters.startDate}
            endDate={filters.endDate}
            onDateRangeChange={handleDateRangeChange}
            onClear={handleDateClear}
          />
          <InstitutionFilter
            value={filters.institutionId}
            onChange={(value) => updateFilter('institutionId', value)}
          />
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport('pdf')}
            className="flex items-center space-x-2"
          >
            <Download className="h-4 w-4" />
            <span>PDF</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport('excel')}
            className="flex items-center space-x-2"
          >
            <Download className="h-4 w-4" />
            <span>Excel</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handlePrint}
            className="flex items-center space-x-2"
          >
            <Printer className="h-4 w-4" />
            <span>Imprimir</span>
          </Button>
        </div>
      }
    >
      {/* Executive Summary */}
      <DashboardGrid columns={1}>
        <GridItem span={1}>
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border">
            <h3 className="text-lg font-semibold mb-2">Resumen Ejecutivo</h3>
            <p className="text-sm text-gray-600 mb-4">
              Reporte generado el {new Date().toLocaleDateString('es-ES')} con datos actualizados hasta {lastUpdated ? new Date(lastUpdated).toLocaleDateString('es-ES') : 'la fecha actual'}.
            </p>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">{data?.totalPatients || 0}</div>
                <div className="text-sm text-gray-600">Total Pacientes</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">{data?.totalInstitutions || 0}</div>
                <div className="text-sm text-gray-600">Instituciones</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">{data?.totalResults || 0}</div>
                <div className="text-sm text-gray-600">Evaluaciones</div>
              </div>
            </div>
          </div>
        </GridItem>
      </DashboardGrid>

      {/* Comprehensive Charts */}
      <DashboardGrid columns={2}>
        <GridItem span={1}>
          <ChartWidget
            title="Análisis de Instituciones"
            data={chartData?.institutionChart}
            type="pie"
            loading={loading}
            height={400}
          />
        </GridItem>
        
        <GridItem span={1}>
          <ChartWidget
            title="Tendencia de Resultados"
            data={chartData?.resultsChart}
            type="line"
            loading={loading}
            height={400}
          />
        </GridItem>
      </DashboardGrid>

      {/* Detailed Reports */}
      <DashboardGrid columns={2}>
        <GridItem span={1}>
          <ChartWidget
            title="Distribución de Puntajes"
            data={chartData?.scoreDistribution}
            type="bar"
            loading={loading}
            height={350}
          />
        </GridItem>
        
        <GridItem span={1}>
          <TableWidget
            title="Reporte Detallado por Institución"
            data={data?.institutionMetrics || []}
            columns={[
              { key: 'name', label: 'Institución' },
              { key: 'patientCount', label: 'Pacientes', type: 'number' },
              { key: 'resultCount', label: 'Evaluaciones', type: 'number' },
              { key: 'averageScore', label: 'Promedio', type: 'badge' },
              { key: 'lastActivity', label: 'Última Actividad', type: 'date' }
            ]}
            loading={loading}
            maxHeight={350}
          />
        </GridItem>
      </DashboardGrid>

      {/* Performance Analysis */}
      <DashboardGrid columns={1}>
        <GridItem span={1}>
          <TableWidget
            title="Análisis de Rendimiento Global"
            data={data?.resultMetrics.slice(0, 20) || []}
            columns={[
              { key: 'patientName', label: 'Paciente' },
              { key: 'institutionName', label: 'Institución' },
              { key: 'score', label: 'Puntaje', type: 'badge' },
              { key: 'testType', label: 'Tipo de Prueba' },
              { key: 'createdAt', label: 'Fecha', type: 'date' }
            ]}
            loading={loading}
            maxHeight={400}
          />
        </GridItem>
      </DashboardGrid>
    </DashboardLayout>
  );
};

export default ReportsDashboard;