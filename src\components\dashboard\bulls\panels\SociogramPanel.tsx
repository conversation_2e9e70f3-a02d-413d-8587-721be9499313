import React, { useState, useRef, useEffect } from 'react';
import { SociogramData, SociogramNode } from '../../../../types/bulls-dashboard';
import SociogramChart from '../../../charts/SociogramChart';
import { But<PERSON> } from '../../../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../ui/select';
import { Badge } from '../../../ui/badge';
import { Info, ZoomIn, ZoomOut, RotateCcw, Filter, Eye, Users } from 'lucide-react';

interface SociogramPanelProps {
  data: SociogramData;
}

export const SociogramPanel: React.FC<SociogramPanelProps> = ({ data }) => {
  const [selectedNode, setSelectedNode] = useState<SociogramNode | null>(null);
  const [relationshipFilter, setRelationshipFilter] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'full' | 'ego'>('full');
  const [highlightMode, setHighlightMode] = useState<'none' | 'bridges' | 'islands'>('none');
  const [layoutMode, setLayoutMode] = useState<'force' | 'circular' | 'grid'>('force');
  const [nodeSpacing, setNodeSpacing] = useState<number>(1);
  const svgRef = useRef<SVGSVGElement>(null);
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });

  const filteredEdges = data.edges.filter(edge => {
    if (relationshipFilter === 'all') return true;
    return edge.type === relationshipFilter;
  });

  const getNodeColor = (node: SociogramNode) => {
    if (highlightMode === 'bridges' && isBridge(node)) return '#ff6b35';
    if (highlightMode === 'islands' && isIsland(node)) return '#6366f1';
    
    switch (node.status) {
      case 'popular': return '#10b981';
      case 'rechazado': return '#ef4444';
      case 'aislado': return '#6b7280';
      case 'controvertido': return '#f59e0b';
      default: return '#3b82f6';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'agresor': return '⚡';
      case 'victima': return '🛡️';
      case 'observador': return '👁️';
      default: return '👤';
    }
  };

  const isBridge = (node: SociogramNode): boolean => {
    // Lógica simplificada para detectar puentes
    const connections = filteredEdges.filter(edge => 
      edge.source === node.id || edge.target === node.id
    );
    return connections.length >= 3; // Simplificado
  };

  const isIsland = (node: SociogramNode): boolean => {
    const connections = filteredEdges.filter(edge => 
      edge.source === node.id || edge.target === node.id
    );
    return connections.length === 0;
  };

  const handleNodeClick = (node: SociogramNode) => {
    setSelectedNode(node);
    if (viewMode === 'ego') {
      // En modo ego, centrar en el nodo seleccionado
    }
  };

  const resetView = () => {
    setZoom(1);
    setPan({ x: 0, y: 0 });
    setSelectedNode(null);
    setViewMode('full');
  };

  const egoNetworkNodes = viewMode === 'ego' && selectedNode 
    ? [selectedNode, ...data.nodes.filter(node => 
        filteredEdges.some(edge => 
          (edge.source === selectedNode.id && edge.target === node.id) ||
          (edge.target === selectedNode.id && edge.source === node.id)
        )
      )]
    : data.nodes;

  const egoNetworkEdges = viewMode === 'ego' && selectedNode
    ? filteredEdges.filter(edge => 
        edge.source === selectedNode.id || edge.target === selectedNode.id
      )
    : filteredEdges;

  return (
    <div className="space-y-6">
      {/* Controles del Sociograma */}
      <div className="flex flex-wrap items-center justify-between gap-4 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-600" />
            <Select value={relationshipFilter} onValueChange={setRelationshipFilter}>
              <SelectTrigger className="w-40">
                <SelectValue
                  selectedValue={
                    relationshipFilter === 'all' ? 'Todas las relaciones' :
                    relationshipFilter === 'positive' ? 'Elecciones' :
                    relationshipFilter === 'negative' ? 'Rechazos' :
                    relationshipFilter === 'aggression' ? 'Agresiones' :
                    'Todas las relaciones'
                  }
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas las relaciones</SelectItem>
                <SelectItem value="eleccion">Elecciones</SelectItem>
                <SelectItem value="rechazo">Rechazos</SelectItem>
                <SelectItem value="agresion">Agresiones</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <Eye className="h-4 w-4 text-gray-600" />
            <Select value={viewMode} onValueChange={(value: 'full' | 'ego') => setViewMode(value)}>
              <SelectTrigger className="w-32">
                <SelectValue
                  selectedValue={
                    viewMode === 'full' ? 'Vista completa' :
                    viewMode === 'ego' ? 'Red personal' :
                    'Vista completa'
                  }
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="full">Vista completa</SelectItem>
                <SelectItem value="ego">Red personal</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <Users className="h-4 w-4 text-gray-600" />
            <Select value={highlightMode} onValueChange={(value: 'none' | 'bridges' | 'islands') => setHighlightMode(value)}>
              <SelectTrigger className="w-32">
                <SelectValue
                  selectedValue={
                    highlightMode === 'none' ? 'Normal' :
                    highlightMode === 'bridges' ? 'Puentes' :
                    highlightMode === 'islands' ? 'Islas' :
                    'Normal'
                  }
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">Normal</SelectItem>
                <SelectItem value="bridges">Puentes</SelectItem>
                <SelectItem value="islands">Islas</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Layout:</span>
            <Select value={layoutMode} onValueChange={(value: 'force' | 'circular' | 'grid') => setLayoutMode(value)}>
              <SelectTrigger className="w-28">
                <SelectValue
                  selectedValue={
                    layoutMode === 'force' ? 'Fuerza' :
                    layoutMode === 'circular' ? 'Circular' :
                    layoutMode === 'grid' ? 'Rejilla' :
                    'Fuerza'
                  }
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="force">Fuerza</SelectItem>
                <SelectItem value="circular">Circular</SelectItem>
                <SelectItem value="grid">Rejilla</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Espaciado:</span>
            <Select value={nodeSpacing.toString()} onValueChange={(value) => setNodeSpacing(parseFloat(value))}>
              <SelectTrigger className="w-20">
                <SelectValue
                  selectedValue={`${nodeSpacing}x`}
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0.5">0.5x</SelectItem>
                <SelectItem value="1">1x</SelectItem>
                <SelectItem value="1.5">1.5x</SelectItem>
                <SelectItem value="2">2x</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={() => setZoom(zoom * 1.2)}>
            <ZoomIn className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" onClick={() => setZoom(zoom * 0.8)}>
            <ZoomOut className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" onClick={resetView}>
            <RotateCcw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sociograma */}
        <div className="lg:col-span-3">
          <div className="border rounded-lg p-4 bg-white" style={{ height: '600px' }}>
            <SociogramChart
              nodes={egoNetworkNodes.map(node => ({
                id: node.id,
                name: node.name,
                fullName: node.name,
                age: 12,
                socialStatus: node.status as any,
                popularityScore: node.popularity,
                rejectionScore: node.rejection,
                isolationLevel: 0,
                bullyingRisk: 'low' as any,
                victimizationRisk: 'low' as any
              }))}
              edges={egoNetworkEdges.map(edge => ({
                source: edge.source,
                target: edge.target,
                type: edge.type === 'positive' ? 'positive' : 'negative' as any,
                weight: edge.weight,
                reciprocal: edge.reciprocal
              }))}
              clusters={data.clusters.map(cluster => ({
                id: cluster.id,
                name: cluster.name,
                members: cluster.members,
                cohesion: cluster.cohesion,
                density: 0.5,
                avgDistance: 2.0
              }))}
              onNodeClick={(node) => setSelectedNode({
                id: node.id,
                name: node.name,
                status: node.socialStatus,
                role: 'student',
                popularity: node.popularityScore,
                rejection: node.rejectionScore,
                x: 0,
                y: 0,
                size: 10,
                color: '#3b82f6'
              })}
              layoutMode={layoutMode}
              nodeSpacing={nodeSpacing}
              width={800}
              height={600}
            />
          </div>
        </div>

        {/* Panel de información */}
        <div className="space-y-4">
          {/* Métricas del sociograma */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
              <Info className="h-4 w-4 mr-2" />
              Métricas de Red
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Densidad:</span>
                <span className="font-medium">{(data.metrics.density * 100).toFixed(1)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Reciprocidad:</span>
                <span className="font-medium">{(data.metrics.reciprocity * 100).toFixed(1)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Transitividad:</span>
                <span className="font-medium">{(data.metrics.transitivity * 100).toFixed(1)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Modularidad:</span>
                <span className="font-medium">{data.metrics.modularity.toFixed(3)}</span>
              </div>
            </div>
          </div>

          {/* Leyenda de estados */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-semibold text-gray-900 mb-3">Estados Sociométricos</h4>
            <div className="space-y-2">
              {[
                { status: 'popular', color: '#10b981', label: 'Popular' },
                { status: 'promedio', color: '#3b82f6', label: 'Promedio' },
                { status: 'controvertido', color: '#f59e0b', label: 'Controvertido' },
                { status: 'rechazado', color: '#ef4444', label: 'Rechazado' },
                { status: 'aislado', color: '#6b7280', label: 'Aislado' }
              ].map((item) => (
                <div key={item.status} className="flex items-center space-x-2">
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: item.color }}
                  ></div>
                  <span className="text-sm text-gray-700">{item.label}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Información del nodo seleccionado */}
          {selectedNode && (
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-3">Información del Estudiante</h4>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Nombre:</span>
                  <p className="text-gray-900">{selectedNode.name}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Estado:</span>
                  <Badge variant="outline" className="ml-2">
                    {selectedNode.status}
                  </Badge>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Rol:</span>
                  <Badge variant="outline" className="ml-2">
                    {selectedNode.role}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Popularidad:</span>
                  <span className="font-medium">{selectedNode.popularity.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Rechazo:</span>
                  <span className="font-medium">{selectedNode.rejection.toFixed(2)}</span>
                </div>
              </div>
            </div>
          )}

          {/* Clusters */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-semibold text-gray-900 mb-3">Grupos Identificados</h4>
            <div className="space-y-2">
              {data.clusters.map((cluster, index) => (
                <div key={cluster.id} className="text-sm">
                  <div className="font-medium text-gray-700">{cluster.name}</div>
                  <div className="text-gray-600">
                    {cluster.members.length} miembros • Cohesión: {(cluster.cohesion * 100).toFixed(1)}%
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};