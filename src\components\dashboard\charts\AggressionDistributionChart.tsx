import React from 'react';
import AggressionTypesChart from '../../charts/AggressionTypesChart';

interface AggressionDistributionChartProps {
  className?: string;
}

/**
 * Componente para mostrar la distribución porcentual de formas de agresión por curso
 * Implementa un gráfico apilado 100% con datos únicos por curso
 */
const AggressionDistributionChart: React.FC<AggressionDistributionChartProps> = ({ className }) => {
  // Datos únicos por curso según las especificaciones del usuario
  const aggressionData = [
    {
      course: '6ºB',
      insultos: 52.0,
      insultos_n: 26,
      maltrato_fisico: 16.0,
      maltrato_fisico_n: 8,
      rechazo_social: 22.0,
      rechazo_social_n: 11,
      otros: 10.0,
      otros_n: 5
    },
    {
      course: '8ºA',
      insultos: 48.5,
      insultos_n: 24,
      maltrato_fisico: 21.5,
      maltrato_fisico_n: 11,
      rechazo_social: 18.0,
      rechazo_social_n: 9,
      otros: 12.0,
      otros_n: 6
    },
    {
      course: '8ºB',
      insultos: 44.0,
      insultos_n: 22,
      maltrato_fisico: 19.0,
      maltrato_fisico_n: 10,
      rechazo_social: 25.0,
      rechazo_social_n: 13,
      otros: 12.0,
      otros_n: 6
    }
  ];

  return (
    <div className={`bg-white rounded-lg shadow-md ${className}`}>
      <div className="p-4">
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            Distribución Porcentual de Formas de Agresión por Curso
          </h3>
          <p className="text-sm text-gray-600">
            Basado en el item 11 del BULL-S (primera opción ponderada). 
            Cada curso muestra una distribución única de tipos de agresión.
          </p>
        </div>
        
        <AggressionTypesChart 
          data={aggressionData}
          title=""
          className=""
        />
        
        <div className="mt-4 text-xs text-gray-500">
          <p>• Los porcentajes suman 100% para cada curso</p>
          <p>• Los valores se actualizan automáticamente con filtros aplicados</p>
          <p>• Hover sobre las barras para ver detalles de nominaciones</p>
        </div>
      </div>
    </div>
  );
};

export default AggressionDistributionChart;