-- Migración para crear funciones específicas de métricas del dashboard BULL-S
-- Estas funciones implementan los cálculos exactos requeridos por cada módulo

-- Función para calcular métricas del Panorama del Curso (Módulo 1)
CREATE OR REPLACE FUNCTION public.calcular_panorama_curso(
    p_curso text DEFAULT NULL,
    p_genero text DEFAULT NULL,
    p_periodo_inicio date DEFAULT NULL,
    p_periodo_fin date DEFAULT NULL
)
RETURNS TABLE (
    total_estudiantes bigint,
    estudiantes_en_riesgo bigint,
    porcentaje_en_riesgo numeric,
    cohesion_grupal numeric,
    seguridad_percibida numeric,
    alertas text[]
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_alertas text[] := ARRAY[]::text[];
    v_total bigint;
    v_riesgo bigint;
    v_victimas bigint;
BEGIN
    -- Calcular total de estudiantes con filtros
    SELECT COUNT(DISTINCT e.id)
    INTO v_total
    FROM public.estudiantes e
    JOIN public.grupos g ON e.grupo_id = g.id
    WHERE (p_curso IS NULL OR g.nombre = p_curso)
        AND (p_genero IS NULL OR e.genero = p_genero);
    
    -- Calcular estudiantes en riesgo
    SELECT COUNT(DISTINCT vr.student_id)
    INTO v_riesgo
    FROM public.vw_roles vr
    WHERE vr.role IN ('agresor', 'victima', 'agresor-victima')
        AND (p_curso IS NULL OR vr.course = p_curso)
        AND (p_genero IS NULL OR vr.gender = p_genero);
    
    -- Calcular cohesión grupal promedio
    WITH cohesion_data AS (
        SELECT AVG(vc.cohesion_pct) as avg_cohesion
        FROM public.vw_cohesion vc
        WHERE (p_curso IS NULL OR vc.course = p_curso)
    )
    SELECT COALESCE(avg_cohesion, 0) INTO cohesion_grupal FROM cohesion_data;
    
    -- Calcular seguridad percibida promedio usando item = 15
    WITH seguridad_data AS (
        SELECT 
            AVG(r.response) as avg_seguridad,
            SUM(CASE WHEN r.response >= 3 THEN 1 ELSE 0 END) * 100.0 / COUNT(*) as pct_seguro
        FROM public.answers r
        WHERE r.item = 15
            AND (p_curso IS NULL OR r.course = p_curso)
            AND (p_genero IS NULL OR r.student_id IN (
                SELECT id FROM public.students WHERE gender = p_genero
            ))
    )
    SELECT COALESCE(avg_seguridad, 2.6) INTO seguridad_percibida FROM seguridad_data;
    
    -- Generar alertas automáticas
    SELECT COUNT(*) INTO v_victimas
    FROM public.vw_roles
    WHERE role = 'victima'
        AND (p_curso IS NULL OR course = p_curso)
        AND (p_genero IS NULL OR gender = p_genero);
    
    IF v_victimas >= 5 THEN
        v_alertas := array_append(v_alertas, 'ALERTA: ≥5 víctimas identificadas');
    END IF;
    
    IF cohesion_grupal < 25 THEN
        v_alertas := array_append(v_alertas, 'ALERTA: Cohesión grupal crítica (<25%)');
    END IF;
    
    IF seguridad_percibida < 50 THEN
        v_alertas := array_append(v_alertas, 'ALERTA: Baja percepción de seguridad (<50%)');
    END IF;
    
    RETURN QUERY SELECT 
        v_total,
        v_riesgo,
        CASE WHEN v_total > 0 THEN ROUND((v_riesgo::numeric / v_total::numeric) * 100, 2) ELSE 0 END,
        ROUND(cohesion_grupal, 2),
        ROUND(seguridad_percibida, 2),
        v_alertas;
END;
$$;

-- ============================================================================
-- FUNCIONES ESPECÍFICAS PARA MÉTRICAS DEL MÓDULO CONTEXTO
-- Implementación según especificaciones del usuario
-- ============================================================================

-- 1. Formas de Agresión – Distribución Porcentual (Item 11)
CREATE OR REPLACE FUNCTION public.obtener_formas_agresion(
    p_curso text DEFAULT NULL
)
RETURNS TABLE (
    course text,
    forma text,
    pct numeric,
    total_respuestas bigint
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    WITH first_choice AS (
        SELECT 
            a.course,
            SPLIT_PART(a.response, ',', 1)::text AS forma,
            1 AS cnt
        FROM public.answers a
        WHERE a.item = 11
            AND (p_curso IS NULL OR a.course = p_curso)
    )
    SELECT 
        fc.course,
        fc.forma,
        ROUND(100.0 * SUM(fc.cnt) / SUM(SUM(fc.cnt)) OVER (PARTITION BY fc.course), 1) AS pct,
        SUM(fc.cnt) AS total_respuestas
    FROM first_choice fc
    GROUP BY fc.course, fc.forma 
    ORDER BY fc.course, pct DESC;
END;
$$;

-- 2. Lugares – Distribución Porcentual (Item 12)
CREATE OR REPLACE FUNCTION public.obtener_lugares_agresion(
    p_curso text DEFAULT NULL
)
RETURNS TABLE (
    course text,
    lugar text,
    pct numeric,
    total_respuestas bigint
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    WITH first_place AS (
        SELECT 
            a.course,
            SPLIT_PART(a.response, ',', 1)::text AS lugar,
            1 AS cnt
        FROM public.answers a
        WHERE a.item = 12
            AND (p_curso IS NULL OR a.course = p_curso)
    )
    SELECT 
        fp.course,
        fp.lugar,
        ROUND(100.0 * SUM(fp.cnt) / SUM(SUM(fp.cnt)) OVER (PARTITION BY fp.course), 1) AS pct,
        SUM(fp.cnt) AS total_respuestas
    FROM first_place fp
    GROUP BY fp.course, fp.lugar 
    ORDER BY fp.course, pct DESC;
END;
$$;

-- 3. Dimensión Temporal (Item 13)
CREATE OR REPLACE FUNCTION public.obtener_dimension_temporal(
    p_curso text DEFAULT NULL
)
RETURNS TABLE (
    course text,
    frecuencia text,
    n bigint,
    pct numeric
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.course,
        a.response AS frecuencia,
        COUNT(*) AS n,
        ROUND(100.0 * COUNT(*) / SUM(COUNT(*)) OVER (PARTITION BY a.course), 1) AS pct
    FROM public.answers a
    WHERE a.item = 13
        AND (p_curso IS NULL OR a.course = p_curso)
    GROUP BY a.course, a.response 
    ORDER BY a.course, pct DESC;
END;
$$;

-- 4. Análisis Cruzado (Items 11 x 12)
CREATE OR REPLACE FUNCTION public.obtener_analisis_cruzado(
    p_curso text DEFAULT NULL
)
RETURNS TABLE (
    forma text,
    lugar text,
    cruce bigint,
    pct_cruce numeric
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        f.response AS forma,
        l.response AS lugar,
        COUNT(*) AS cruce,
        ROUND(100.0 * COUNT(*) / SUM(COUNT(*)) OVER (), 1) AS pct_cruce
    FROM public.answers f 
    JOIN public.answers l 
        ON f.student_id = l.student_id 
        AND f.course = l.course
    WHERE f.item = 11 
        AND l.item = 12
        AND (p_curso IS NULL OR f.course = p_curso)
    GROUP BY f.response, l.response 
    ORDER BY pct_cruce DESC;
END;
$$;

-- 5. Función helper para verificar datos disponibles
CREATE OR REPLACE FUNCTION public.verificar_datos_contexto()
RETURNS TABLE (
    item integer,
    total_respuestas bigint,
    cursos_con_datos bigint,
    estudiantes_respondieron bigint
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.item,
        COUNT(*) as total_respuestas,
        COUNT(DISTINCT a.course) as cursos_con_datos,
        COUNT(DISTINCT a.student_id) as estudiantes_respondieron
    FROM public.answers a
    WHERE a.item IN (11, 12, 13)
    GROUP BY a.item
    ORDER BY a.item;
END;
$$;

-- Función para calcular métricas del Sociograma (Módulo 2)
CREATE OR REPLACE FUNCTION public.calcular_sociograma(
    p_curso text DEFAULT NULL
)
RETURNS TABLE (
    student_id uuid,
    name text,
    popularidad numeric,
    rechazo numeric,
    reciprocidades bigint,
    role text,
    estatus_sociometrico text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    WITH reciprocidades_calc AS (
        SELECT 
            e.id as student_id,
            COUNT(CASE WHEN r1.estudiante_id IS NOT NULL AND r2.estudiante_id IS NOT NULL THEN 1 END) as reciprocidades
        FROM public.estudiantes e
        LEFT JOIN public.respuestas r1 ON e.id = r1.estudiante_id
        LEFT JOIN public.respuestas r2 ON 
            r1.grupo_id = r2.grupo_id
            AND e.id::text = ANY(string_to_array(trim(r2.respuesta_texto, '[]"'), ','))
            AND r2.estudiante_id::text = ANY(string_to_array(trim(r1.respuesta_texto, '[]"'), ','))
        JOIN public.preguntas p1 ON r1.pregunta_id = p1.id
        JOIN public.preguntas p2 ON r2.pregunta_id = p2.id
        WHERE p1.categoria = 'sociometrica' AND p2.categoria = 'sociometrica'
        GROUP BY e.id
    )
    SELECT 
        vp.student_id,
        vp.name,
        vp.Pe as popularidad,
        vr.Sp as rechazo,
        COALESCE(rc.reciprocidades, 0) as reciprocidades,
        vro.role,
        CASE 
            WHEN vp.Pe >= 70 AND vr.Sp <= 30 THEN 'popular'
            WHEN vp.Pe <= 30 AND vr.Sp >= 70 THEN 'rechazado'
            WHEN vp.Pe <= 30 AND vr.Sp <= 30 THEN 'aislado'
            WHEN vp.Pe >= 50 AND vr.Sp >= 50 THEN 'controvertido'
            ELSE 'promedio'
        END as estatus_sociometrico
    FROM public.vw_popularity vp
    JOIN public.vw_rejection vr ON vp.student_id = vr.student_id
    JOIN public.vw_roles vro ON vp.student_id = vro.student_id
    LEFT JOIN reciprocidades_calc rc ON vp.student_id = rc.student_id
    WHERE (p_curso IS NULL OR vp.course = p_curso);
END;
$$;

-- Función para calcular total de nominaciones de agresión (items 5-10)
CREATE OR REPLACE FUNCTION public.calcular_nominaciones_agresion(
    p_curso text DEFAULT NULL,
    p_genero text DEFAULT NULL
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    total_nominaciones integer;
    top_agresion_tipo text;
    top_agresion_count integer;
BEGIN
    -- Calcular total de nominaciones de agresión (items 5-10)
    SELECT COALESCE(SUM(n.value), 0)
    INTO total_nominaciones
    FROM public.nominations n
    WHERE n.item IN (5,6,7,8,9,10)
        AND (p_curso IS NULL OR n.course = p_curso);
    
    -- Obtener top forma de agresión del item 11
    SELECT r.response, COUNT(*)
    INTO top_agresion_tipo, top_agresion_count
    FROM public.answers r
    WHERE r.item = 11
        AND (p_curso IS NULL OR r.course = p_curso)
        AND (p_genero IS NULL OR r.student_id IN (
            SELECT id FROM public.students WHERE gender = p_genero
        ))
    GROUP BY r.response
    ORDER BY COUNT(*) DESC
    LIMIT 1;
    
    RETURN jsonb_build_object(
        'total_nominaciones_agresion', total_nominaciones,
        'top_forma_agresion', COALESCE(top_agresion_tipo, 'Sin datos'),
        'top_forma_count', COALESCE(top_agresion_count, 0)
    );
END;
$$;

-- Función para calcular métricas de Contexto (Módulo 4)
-- Actualizada según especificaciones: items 11, 12, 13 de tabla answers
CREATE OR REPLACE FUNCTION public.calcular_contexto(
    p_curso text DEFAULT NULL,
    p_genero text DEFAULT NULL
)
RETURNS TABLE (
    forma_agresion jsonb,
    lugar_agresion jsonb,
    frecuencia jsonb,
    analisis_cruzado jsonb
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    WITH forma_agresion_data AS (
        -- Item 11: "Las agresiones suelen ser..." - usar solo primera opción
        SELECT jsonb_agg(
            jsonb_build_object(
                'forma', SPLIT_PART(r.response, ',', 1)::text,
                'count', COUNT(*),
                'percentage', ROUND(100.0 * COUNT(*) / SUM(COUNT(*)) OVER (), 1)
            )
        ) as forma_agresion
        FROM public.answers r
        WHERE r.item = 11
            AND (p_curso IS NULL OR r.course = p_curso)
            AND (p_genero IS NULL OR r.student_id IN (
                SELECT id FROM public.students WHERE gender = p_genero
            ))
        GROUP BY SPLIT_PART(r.response, ',', 1)
    ),
    lugar_agresion_data AS (
        -- Item 12: "¿Dónde suelen ocurrir...?" - usar solo primera opción
        SELECT jsonb_agg(
            jsonb_build_object(
                'lugar', SPLIT_PART(r.response, ',', 1)::text,
                'count', COUNT(*),
                'percentage', ROUND(100.0 * COUNT(*) / SUM(COUNT(*)) OVER (), 1)
            )
        ) as lugar_agresion
        FROM public.answers r
        WHERE r.item = 12
            AND (p_curso IS NULL OR r.course = p_curso)
            AND (p_genero IS NULL OR r.student_id IN (
                SELECT id FROM public.students WHERE gender = p_genero
            ))
        GROUP BY SPLIT_PART(r.response, ',', 1)
    ),
    frecuencia_data AS (
        -- Item 13: "¿Con qué frecuencia...?" - respuesta única
        SELECT jsonb_agg(
            jsonb_build_object(
                'frecuencia', r.response,
                'count', COUNT(*),
                'percentage', ROUND(100.0 * COUNT(*) / SUM(COUNT(*)) OVER (), 1)
            )
        ) as frecuencia
        FROM public.answers r
        WHERE r.item = 13
            AND (p_curso IS NULL OR r.course = p_curso)
            AND (p_genero IS NULL OR r.student_id IN (
                SELECT id FROM public.students WHERE gender = p_genero
            ))
        GROUP BY r.response
    ),
    analisis_cruzado_data AS (
        -- Análisis cruzado: forma x lugar (items 11 x 12)
        SELECT jsonb_agg(
            jsonb_build_object(
                'forma', f.response,
                'lugar', l.response,
                'count', COUNT(*),
                'percentage', ROUND(100.0 * COUNT(*) / SUM(COUNT(*)) OVER (), 1)
            )
        ) as analisis_cruzado
        FROM public.answers f
        JOIN public.answers l ON f.student_id = l.student_id AND f.course = l.course
        WHERE f.item = 11 AND l.item = 12
            AND (p_curso IS NULL OR f.course = p_curso)
            AND (p_genero IS NULL OR f.student_id IN (
                SELECT id FROM public.students WHERE gender = p_genero
            ))
        GROUP BY f.response, l.response
    )
    SELECT 
        COALESCE(fad.forma_agresion, '[]'::jsonb),
        COALESCE(lad.lugar_agresion, '[]'::jsonb),
        COALESCE(fd.frecuencia, '[]'::jsonb),
        COALESCE(acd.analisis_cruzado, '[]'::jsonb)
    FROM forma_agresion_data fad
    CROSS JOIN lugar_agresion_data lad
    CROSS JOIN frecuencia_data fd
    CROSS JOIN analisis_cruzado_data acd;
END;
$$;

-- Función para calcular métricas Comparativas (Módulo 5)
CREATE OR REPLACE FUNCTION public.calcular_comparativas()
RETURNS TABLE (
    comparacion_curso jsonb,
    comparacion_genero jsonb,
    evolucion_temporal jsonb
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    WITH comparacion_curso_data AS (
        SELECT jsonb_agg(
            jsonb_build_object(
                'course', base.course,
                'estudiantes', base.estudiantes,
                'participacion', base.participacion,
                'bullying_pct', base.bullying_pct,
                'cohesion_pct', base.cohesion_pct,
                'seguridad_avg', base.seguridad_avg,
                'studentsAtRisk', base.bullying_pct,
                'cohesion', base.cohesion_pct,
                'safety', ROUND((base.seguridad_avg - 1) * 100.0 / 3, 1)
            )
        ) as comparacion_curso
        FROM (
            SELECT 
                s.course,
                COUNT(DISTINCT s.id) AS estudiantes,
                ROUND(COUNT(DISTINCT a.student_id) * 100.0 / COUNT(DISTINCT s.id), 1) AS participacion,
                ROUND(100.0 * COUNT(DISTINCT r.student_id) FILTER (WHERE r.role IN ('agresor','victima','agresor-victima')) / COUNT(DISTINCT s.id), 1) AS bullying_pct,
                ROUND(COALESCE(c.cohesion_pct, 0), 1) AS cohesion_pct,
                ROUND(AVG(a15.response), 2) AS seguridad_avg
            FROM public.students s 
            LEFT JOIN public.answers a ON s.id = a.student_id 
            LEFT JOIN public.answers a15 ON s.id = a15.student_id AND a15.item = 15 
            LEFT JOIN public.vw_roles r ON s.id = r.student_id 
            LEFT JOIN public.vw_cohesion c ON s.course = c.course 
            GROUP BY s.course, c.cohesion_pct
        ) base
    ),
    comparacion_genero_data AS (
        SELECT jsonb_agg(
            jsonb_build_object(
                'gender', vr.gender,
                'role', vr.role,
                'count', COUNT(*)
            )
        ) as comparacion_genero
        FROM public.vw_roles vr
        GROUP BY vr.gender, vr.role
    ),
    evolucion_temporal_data AS (
        SELECT jsonb_agg(
            jsonb_build_object(
                'test_date', r.fecha_respuesta,
                'role', vr.role,
                'count', COUNT(*)
            ) ORDER BY r.fecha_respuesta
        ) as evolucion_temporal
        FROM public.vw_roles vr
        JOIN public.respuestas r ON vr.student_id = r.estudiante_id
        GROUP BY r.fecha_respuesta, vr.role
    )
    SELECT 
        COALESCE(ccd.comparacion_curso, '[]'::jsonb),
        COALESCE(cgd.comparacion_genero, '[]'::jsonb),
        COALESCE(etd.evolucion_temporal, '[]'::jsonb)
    FROM comparacion_curso_data ccd
    CROSS JOIN comparacion_genero_data cgd
    CROSS JOIN evolucion_temporal_data etd;
END;
$$;

-- Función para generar acciones recomendadas (Módulo 6)
CREATE OR REPLACE FUNCTION public.generar_acciones_recomendadas(
    p_curso text DEFAULT NULL
)
RETURNS TABLE (
    prioridad text,
    tipo_intervencion text,
    descripcion text,
    estudiantes_objetivo text[]
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_victimas bigint;
    v_agresores bigint;
    v_cohesion numeric;
BEGIN
    -- Contar víctimas
    SELECT COUNT(*) INTO v_victimas
    FROM public.vw_victims
    WHERE (p_curso IS NULL OR course = p_curso);
    
    -- Contar agresores
    SELECT COUNT(*) INTO v_agresores
    FROM public.vw_aggressors
    WHERE (p_curso IS NULL OR course = p_curso);
    
    -- Obtener cohesión promedio
    SELECT AVG(cohesion_pct) INTO v_cohesion
    FROM public.vw_cohesion
    WHERE (p_curso IS NULL OR course = p_curso);
    
    -- Generar recomendaciones basadas en criterios
    IF v_victimas > 5 OR COALESCE(v_cohesion, 0) < 25 THEN
        RETURN QUERY SELECT 
            'CRÍTICA'::text,
            'Institucional'::text,
            'Intervención inmediata requerida: Alto nivel de victimización o cohesión crítica'::text,
            ARRAY(SELECT name FROM public.vw_victims WHERE (p_curso IS NULL OR course = p_curso));
    END IF;
    
    IF v_agresores > 3 THEN
        RETURN QUERY SELECT 
            'ALTA'::text,
            'Individual'::text,
            'Intervención con agresores identificados'::text,
            ARRAY(SELECT name FROM public.vw_aggressors WHERE (p_curso IS NULL OR course = p_curso));
    END IF;
    
    -- Siempre incluir recomendación grupal
    RETURN QUERY SELECT 
        'MEDIA'::text,
        'Grupal'::text,
        'Programa de fortalecimiento de convivencia escolar'::text,
        ARRAY[]::text[];
END;
$$;

-- Otorgar permisos para las funciones
GRANT EXECUTE ON FUNCTION public.calcular_panorama_curso TO authenticated;
GRANT EXECUTE ON FUNCTION public.calcular_sociograma TO authenticated;
GRANT EXECUTE ON FUNCTION public.calcular_nominaciones_agresion TO authenticated;
GRANT EXECUTE ON FUNCTION public.calcular_contexto TO authenticated;
GRANT EXECUTE ON FUNCTION public.calcular_comparativas TO authenticated;
GRANT EXECUTE ON FUNCTION public.generar_acciones_recomendadas TO authenticated;

-- Comentarios para documentación
COMMENT ON FUNCTION public.calcular_panorama_curso IS 'Calcula métricas del módulo Panorama del Curso: total estudiantes, en riesgo, cohesión, seguridad y alertas';
COMMENT ON FUNCTION public.calcular_sociograma IS 'Calcula métricas del módulo Sociograma: popularidad, rechazo, reciprocidades y roles';
COMMENT ON FUNCTION public.calcular_nominaciones_agresion IS 'Calcula total de nominaciones de agresión usando items 5-10 y top forma de agresión del item 11';
COMMENT ON FUNCTION public.calcular_contexto IS 'Calcula métricas del módulo Contexto: formas, lugares, frecuencia y gravedad de agresiones';
COMMENT ON FUNCTION public.calcular_comparativas IS 'Calcula métricas del módulo Comparativas: por curso, género y evolución temporal';
COMMENT ON FUNCTION public.generar_acciones_recomendadas IS 'Genera acciones recomendadas basadas en criterios de riesgo y prioridad';