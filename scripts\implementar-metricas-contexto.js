import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Cargar variables de entorno
dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

/**
 * MÉTRICAS REALES PARA EL MÓDULO "CONTEXTO" (BULL-S)
 * Implementación según especificaciones del usuario
 * Fuente: tabla `answers` (items 11–15) + `nominations` (5–10)
 */

class MetricasContexto {
  constructor() {
    this.supabase = supabase;
  }

  /**
   * 1. Formas de Agresión – Distribución Porcentual
   * Item 11: "Las agresiones suelen ser…"
   * Respuestas múltiples ordenadas → usar solo la 1ª opción (peso = 3)
   */
  async obtenerFormasAgresion(curso = null) {
    console.log('\n📊 1. FORMAS DE AGRESIÓN (Item 11)');
    
    const query = `
      WITH first_choice AS (
        SELECT 
          course,
          SPLIT_PART(response, ',', 1)::text AS forma,
          1 AS cnt
        FROM answers 
        WHERE item = 11
        ${curso ? `AND course = '${curso}'` : ''}
      )
      SELECT 
        course,
        forma,
        ROUND(100.0 * SUM(cnt) / SUM(SUM(cnt)) OVER (PARTITION BY course), 1) AS pct,
        SUM(cnt) AS total_respuestas
      FROM first_choice 
      GROUP BY course, forma 
      ORDER BY course, pct DESC;
    `;

    try {
      const { data, error } = await this.supabase.rpc('execute_sql', {
        query: query
      });

      if (error) {
        console.error('❌ Error en consulta formas de agresión:', error);
        return [];
      }

      console.log('✅ Resultados Formas de Agresión:');
      console.table(data);
      return data;
    } catch (err) {
      console.error('❌ Error ejecutando consulta:', err);
      return [];
    }
  }

  /**
   * 2. Lugares – Distribución Porcentual
   * Item 12: "¿Dónde suelen ocurrir…?" (misma lógica)
   */
  async obtenerLugaresAgresion(curso = null) {
    console.log('\n📍 2. LUGARES DE AGRESIÓN (Item 12)');
    
    const query = `
      WITH first_place AS (
        SELECT 
          course,
          SPLIT_PART(response, ',', 1)::text AS lugar,
          1 AS cnt
        FROM answers 
        WHERE item = 12
        ${curso ? `AND course = '${curso}'` : ''}
      )
      SELECT 
        course,
        lugar,
        ROUND(100.0 * SUM(cnt) / SUM(SUM(cnt)) OVER (PARTITION BY course), 1) AS pct,
        SUM(cnt) AS total_respuestas
      FROM first_place 
      GROUP BY course, lugar 
      ORDER BY course, pct DESC;
    `;

    try {
      const { data, error } = await this.supabase.rpc('execute_sql', {
        query: query
      });

      if (error) {
        console.error('❌ Error en consulta lugares de agresión:', error);
        return [];
      }

      console.log('✅ Resultados Lugares de Agresión:');
      console.table(data);
      return data;
    } catch (err) {
      console.error('❌ Error ejecutando consulta:', err);
      return [];
    }
  }

  /**
   * 3. Dimensión Temporal
   * Item 13: "¿Con qué frecuencia…?" (una sola respuesta)
   */
  async obtenerDimensionTemporal(curso = null) {
    console.log('\n⏰ 3. DIMENSIÓN TEMPORAL (Item 13)');
    
    const query = `
      SELECT 
        course,
        response AS frecuencia,
        COUNT(*) AS n,
        ROUND(100.0 * COUNT(*) / SUM(COUNT(*)) OVER (PARTITION BY course), 1) AS pct
      FROM answers 
      WHERE item = 13
      ${curso ? `AND course = '${curso}'` : ''}
      GROUP BY course, response 
      ORDER BY course, pct DESC;
    `;

    try {
      const { data, error } = await this.supabase.rpc('execute_sql', {
        query: query
      });

      if (error) {
        console.error('❌ Error en consulta dimensión temporal:', error);
        return [];
      }

      console.log('✅ Resultados Dimensión Temporal:');
      console.table(data);
      return data;
    } catch (err) {
      console.error('❌ Error ejecutando consulta:', err);
      return [];
    }
  }

  /**
   * 4. Análisis Cruzado
   * Objetivo: ver qué forma de agresión ocurre en qué lugar
   */
  async obtenerAnalisisCruzado(curso = null) {
    console.log('\n🔄 4. ANÁLISIS CRUZADO (Items 11 x 12)');
    
    const query = `
      SELECT 
        f.response AS forma,
        l.response AS lugar,
        COUNT(*) AS cruce,
        ROUND(100.0 * COUNT(*) / SUM(COUNT(*)) OVER (), 1) AS pct_cruce
      FROM answers f 
      JOIN answers l 
        ON f.student_id = l.student_id 
        AND f.course = l.course
      WHERE f.item = 11 
        AND l.item = 12
        ${curso ? `AND f.course = '${curso}'` : ''}
      GROUP BY f.response, l.response 
      ORDER BY pct_cruce DESC;
    `;

    try {
      const { data, error } = await this.supabase.rpc('execute_sql', {
        query: query
      });

      if (error) {
        console.error('❌ Error en consulta análisis cruzado:', error);
        return [];
      }

      console.log('✅ Resultados Análisis Cruzado:');
      console.table(data);
      return data;
    } catch (err) {
      console.error('❌ Error ejecutando consulta:', err);
      return [];
    }
  }

  /**
   * 5. Verificar estructura de datos disponibles
   */
  async verificarDatosDisponibles() {
    console.log('\n🔍 VERIFICANDO DATOS DISPONIBLES...');
    
    // Verificar tabla answers
    const { data: answersData, error: answersError } = await this.supabase
      .from('answers')
      .select('item, course, count(*)', { count: 'exact' })
      .in('item', [11, 12, 13])
      .limit(1);

    if (answersError) {
      console.error('❌ Error verificando tabla answers:', answersError);
    } else {
      console.log('✅ Tabla answers encontrada');
    }

    // Contar respuestas por item
    const itemsQuery = `
      SELECT 
        item,
        COUNT(*) as total_respuestas,
        COUNT(DISTINCT course) as cursos_con_datos,
        COUNT(DISTINCT student_id) as estudiantes_respondieron
      FROM answers 
      WHERE item IN (11, 12, 13)
      GROUP BY item
      ORDER BY item;
    `;

    try {
      const { data, error } = await this.supabase.rpc('execute_sql', {
        query: itemsQuery
      });

      if (error) {
        console.error('❌ Error verificando items:', error);
      } else {
        console.log('\n📊 Resumen de datos por item:');
        console.table(data);
      }
    } catch (err) {
      console.error('❌ Error en verificación:', err);
    }
  }

  /**
   * Ejecutar todas las métricas del módulo Contexto
   */
  async ejecutarTodasLasMetricas(curso = null) {
    console.log('\n🎯 EJECUTANDO MÉTRICAS DEL MÓDULO CONTEXTO');
    console.log('=' .repeat(60));
    
    if (curso) {
      console.log(`📚 Curso seleccionado: ${curso}`);
    } else {
      console.log('📚 Analizando todos los cursos');
    }

    await this.verificarDatosDisponibles();
    
    const resultados = {
      formasAgresion: await this.obtenerFormasAgresion(curso),
      lugaresAgresion: await this.obtenerLugaresAgresion(curso),
      dimensionTemporal: await this.obtenerDimensionTemporal(curso),
      analisisCruzado: await this.obtenerAnalisisCruzado(curso)
    };

    console.log('\n📋 RESUMEN EJECUTIVO:');
    console.log('=' .repeat(40));
    console.log(`✅ Formas de agresión: ${resultados.formasAgresion.length} registros`);
    console.log(`✅ Lugares de agresión: ${resultados.lugaresAgresion.length} registros`);
    console.log(`✅ Dimensión temporal: ${resultados.dimensionTemporal.length} registros`);
    console.log(`✅ Análisis cruzado: ${resultados.analisisCruzado.length} registros`);

    return resultados;
  }
}

// Función principal
async function main() {
  console.log('🚀 INICIANDO ANÁLISIS DE MÉTRICAS DE CONTEXTO');
  console.log('=' .repeat(60));
  
  const metricas = new MetricasContexto();
  
  try {
    // Ejecutar para todos los cursos
    await metricas.ejecutarTodasLasMetricas();
    
    // Ejecutar para un curso específico (ejemplo)
    console.log('\n\n🎯 ANÁLISIS ESPECÍFICO PARA 6ºB:');
    await metricas.ejecutarTodasLasMetricas('6ºB');
    
  } catch (error) {
    console.error('❌ Error en ejecución principal:', error);
  }
}

// Ejecutar si es llamado directamente
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { MetricasContexto };