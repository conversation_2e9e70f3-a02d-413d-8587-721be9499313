import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Cargar variables de entorno
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Error: Variables de entorno de Supabase no encontradas');
    console.log('VITE_SUPABASE_URL:', supabaseUrl ? '✓ Configurada' : '✗ Faltante');
    console.log('VITE_SUPABASE_ANON_KEY:', supabaseKey ? '✓ Configurada' : '✗ Faltante');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function verificarMigraciones() {
    console.log('🔍 VERIFICANDO MIGRACIONES APLICADAS\n');
    
    try {
        // 1. Verificar que las tablas principales existen
        console.log('📋 1. Verificando tablas principales...');
        const tablas = ['grupos', 'estudiantes', 'preguntas', 'respuestas', 'students', 'answers', 'sociomatrix'];
        
        for (const tabla of tablas) {
            try {
                const { data, error } = await supabase
                    .from(tabla)
                    .select('*')
                    .limit(1);
                
                if (error) {
                    console.log(`   ❌ Tabla ${tabla}: ${error.message}`);
                } else {
                    console.log(`   ✅ Tabla ${tabla}: OK`);
                }
            } catch (err) {
                console.log(`   ❌ Tabla ${tabla}: Error - ${err.message}`);
            }
        }
        
        // 2. Verificar vistas
        console.log('\n👁️ 2. Verificando vistas...');
        const vistas = ['vw_roles', 'vw_cohesion'];
        
        for (const vista of vistas) {
            try {
                const { data, error } = await supabase
                    .from(vista)
                    .select('*')
                    .limit(1);
                
                if (error) {
                    console.log(`   ❌ Vista ${vista}: ${error.message}`);
                } else {
                    console.log(`   ✅ Vista ${vista}: OK (${data?.length || 0} registros de muestra)`);
                }
            } catch (err) {
                console.log(`   ❌ Vista ${vista}: Error - ${err.message}`);
            }
        }
        
        // 3. Verificar funciones
        console.log('\n⚙️ 3. Verificando funciones...');
        
        // Probar calcular_comparativas
        try {
            const { data, error } = await supabase.rpc('calcular_comparativas');
            if (error) {
                console.log(`   ❌ Función calcular_comparativas: ${error.message}`);
            } else {
                console.log(`   ✅ Función calcular_comparativas: OK (${data?.length || 0} cursos)`);
                if (data && data.length > 0) {
                    console.log('      📊 Muestra de datos:');
                    data.slice(0, 2).forEach(curso => {
                        console.log(`         - ${curso.curso}: ${curso.estudiantes} estudiantes, ${curso.bullying_pct}% bullying, ${curso.cohesion_pct}% cohesión`);
                    });
                }
            }
        } catch (err) {
            console.log(`   ❌ Función calcular_comparativas: Error - ${err.message}`);
        }
        
        // Probar calcular_panorama_curso
        try {
            const { data, error } = await supabase.rpc('calcular_panorama_curso', { curso_nombre: null });
            if (error) {
                console.log(`   ❌ Función calcular_panorama_curso: ${error.message}`);
            } else {
                console.log(`   ✅ Función calcular_panorama_curso: OK (${data?.length || 0} cursos)`);
            }
        } catch (err) {
            console.log(`   ❌ Función calcular_panorama_curso: Error - ${err.message}`);
        }
        
        // 4. Verificar datos de muestra
        console.log('\n📊 4. Verificando datos de muestra...');
        
        // Contar cursos disponibles
        try {
            const { data: grupos, error } = await supabase
                .from('grupos')
                .select('nombre');
            
            if (error) {
                console.log(`   ❌ Error al obtener grupos: ${error.message}`);
            } else {
                console.log(`   📚 Grupos disponibles: ${grupos?.length || 0}`);
                if (grupos && grupos.length > 0) {
                    grupos.slice(0, 3).forEach(grupo => {
                        console.log(`      - ${grupo.nombre}`);
                    });
                }
            }
        } catch (err) {
            console.log(`   ❌ Error al verificar grupos: ${err.message}`);
        }
        
        // Verificar respuestas de seguridad (item 15)
        try {
            const { data: seguridad, error } = await supabase
                .from('answers')
                .select('course, answer')
                .eq('item', 15)
                .limit(5);
            
            if (error) {
                console.log(`   ❌ Error al obtener datos de seguridad: ${error.message}`);
            } else {
                console.log(`   🛡️ Respuestas de seguridad (item 15): ${seguridad?.length || 0} registros`);
            }
        } catch (err) {
            console.log(`   ❌ Error al verificar seguridad: ${err.message}`);
        }
        
        console.log('\n✅ VERIFICACIÓN COMPLETADA');
        
    } catch (error) {
        console.error('❌ Error general:', error.message);
    }
}

// Ejecutar verificación
verificarMigraciones();