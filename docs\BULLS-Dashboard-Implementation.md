# BULL-S Dashboard - Implementación de Métricas

## Resumen

Este documento describe la implementación completa del dashboard BULL-S con todas las métricas requeridas según las especificaciones del proyecto. La implementación incluye vistas SQL optimizadas, funciones de cálculo y un sistema de métricas sociométricas para el análisis de convivencia escolar.

## Arquitectura de la Solución

### 1. Vistas SQL Implementadas

#### `vw_roles` - Clasificación de Roles
- **Propósito**: Clasifica a los estudiantes en roles específicos basándose en nominaciones
- **Roles identificados**: 
  - `agresor`: Estudiantes que ejercen agresión
  - `victima`: Estudiantes que sufren victimización
  - `observador`: Estudiantes que observan situaciones de conflicto
  - `sin_rol_especifico`: Estudiantes sin rol predominante
- **Criterio**: Basado en nominaciones relacionadas con agresión, victimización u observación

#### `vw_popularity` - Índice de Popularidad (Pe)
- **Propósito**: Calcula el índice de popularidad para cada estudiante
- **Fórmula**: `Pe = (elecciones_positivas / total_estudiantes_grupo) * 100`
- **Criterio**: Nominaciones positivas como "me gusta", "amigos", etc.

#### `vw_rejection` - Índice de Rechazo (Sp)
- **Propósito**: Calcula el índice de rechazo para cada estudiante
- **Fórmula**: `Sp = (rechazos / total_estudiantes_grupo) * 100`
- **Criterio**: Nominaciones negativas como "no me gusta", "rechazo", etc.

#### `vw_cohesion` - Cohesión Grupal
- **Propósito**: Mide la cohesión del grupo basándose en nominaciones recíprocas
- **Fórmula**: `cohesion_pct = (nominaciones_reciprocas / total_nominaciones_positivas) * 100`
- **Criterio**: Nominaciones mutuas positivas dentro del grupo

#### `vw_aggressors` - Identificación de Agresores
- **Propósito**: Identifica estudiantes que actúan como agresores
- **Criterio**: Reciben nominaciones de al menos 25% de los estudiantes del grupo
- **Campos**: `student_id`, `name`, `group_id`, `nomination_count`, `group_size`, `percentage`

#### `vw_victims` - Identificación de Víctimas
- **Propósito**: Identifica estudiantes que son víctimas de agresión
- **Criterio**: Reciben nominaciones de al menos 25% de los estudiantes del grupo
- **Campos**: `student_id`, `name`, `group_id`, `nomination_count`, `group_size`, `percentage`

#### `vw_interactions` - Matriz de Interacciones
- **Propósito**: Muestra la matriz de interacciones agresor-víctima
- **Campos**: `aggressor_id`, `aggressor_name`, `victim_id`, `victim_name`, `interaction_count`

### 2. Funciones SQL Implementadas

#### `calcular_panorama_curso()`
- **Propósito**: Calcula métricas generales del curso
- **Retorna**:
  - `total_estudiantes`: Número total de estudiantes
  - `estudiantes_en_riesgo`: Estudiantes identificados en riesgo
  - `porcentaje_en_riesgo`: Porcentaje de estudiantes en riesgo
  - `cohesion_grupal`: Porcentaje promedio de cohesión grupal
  - `seguridad_percibida`: Porcentaje de seguridad percibida
  - `alertas`: Array de alertas generadas automáticamente

#### `calcular_sociograma()`
- **Propósito**: Calcula métricas sociométricas detalladas
- **Retorna**:
  - `student_id`, `name`, `group_id`
  - `popularidad`: Índice de popularidad (Pe)
  - `rechazo`: Índice de rechazo (Sp)
  - `reciprocidades`: Número de nominaciones recíprocas
  - `estatus_sociometrico`: Clasificación sociométrica
    - `popular`: Pe > promedio + 1 SD, Sp < promedio
    - `rechazado`: Sp > promedio + 1 SD, Pe < promedio
    - `aislado`: Pe y Sp < promedio - 1 SD
    - `controvertido`: Pe y Sp > promedio + 1 SD
    - `promedio`: Resto de casos

#### `calcular_contexto()`
- **Propósito**: Analiza el contexto de las agresiones
- **Retorna**:
  - `forma_agresion`: Distribución de tipos de agresión
  - `lugar_agresion`: Distribución de lugares donde ocurre
  - `frecuencia`: Datos de frecuencia de agresiones
  - `gravedad_percibida`: Percepción de gravedad
  - `seguridad_percibida`: Nivel de seguridad percibida

#### `calcular_comparativas()`
- **Propósito**: Genera comparaciones entre grupos y temporal
- **Retorna**:
  - `comparacion_curso`: Métricas comparativas entre cursos
  - `comparacion_genero`: Distribución por género y rol
  - `evolucion_temporal`: Evolución de roles en el tiempo

#### `generar_acciones_recomendadas()`
- **Propósito**: Genera recomendaciones de intervención automáticas
- **Criterios**:
  - Número de víctimas y agresores identificados
  - Nivel de cohesión grupal
  - Seguridad percibida
- **Retorna**:
  - `tipo_intervencion`: Tipo de acción recomendada
  - `descripcion`: Descripción detallada
  - `prioridad`: Nivel de prioridad (Alta, Media, Baja)
  - `estudiantes_objetivo`: Estudiantes específicos a intervenir

## Estructura de Datos

### Tablas Base Utilizadas
- `students`: Información de estudiantes
- `respuestas`: Respuestas a cuestionarios
- `nominaciones_sociometricas`: Nominaciones optimizadas
- `cuestionarios`: Definición de cuestionarios

### Campos Clave en Respuestas
- **Pregunta 11 (q11)**: Tipos de agresión (JSON)
- **Pregunta 12 (q12)**: Lugares de agresión (JSON)
- **Nominaciones sociométricas**: Elecciones y rechazos entre pares

## Seguridad y Permisos

### Row Level Security (RLS)
Todas las vistas implementan RLS para garantizar que:
- Los usuarios solo accedan a datos de sus grupos asignados
- Los administradores tengan acceso completo
- Los profesores accedan solo a sus cursos

### Permisos de Funciones
- Todas las funciones tienen permisos `EXECUTE` para el rol `authenticated`
- Las funciones respetan las políticas RLS de las tablas subyacentes

## Optimización y Rendimiento

### Índices Creados
```sql
-- Índices para optimizar consultas de vistas
CREATE INDEX IF NOT EXISTS idx_vw_roles_student_group ON students(id, group_id);
CREATE INDEX IF NOT EXISTS idx_vw_popularity_nominations ON nominaciones_sociometricas(nominated_student_id, nominator_student_id);
CREATE INDEX IF NOT EXISTS idx_vw_rejection_nominations ON nominaciones_sociometricas(nominated_student_id, nomination_type);
CREATE INDEX IF NOT EXISTS idx_vw_cohesion_group ON nominaciones_sociometricas(nominator_student_id, nominated_student_id);
CREATE INDEX IF NOT EXISTS idx_vw_aggressors_nominations ON nominaciones_sociometricas(nominated_student_id, nomination_type);
CREATE INDEX IF NOT EXISTS idx_vw_victims_nominations ON nominaciones_sociometricas(nominated_student_id, nomination_type);
CREATE INDEX IF NOT EXISTS idx_vw_interactions_both ON nominaciones_sociometricas(nominator_student_id, nominated_student_id);
```

### Estrategias de Optimización
1. **Uso de CTEs**: Para mejorar legibilidad y reutilización de subconsultas
2. **Índices específicos**: Para acelerar las consultas más frecuentes
3. **Agregaciones eficientes**: Uso de `GROUP BY` y funciones de ventana
4. **Filtros tempranos**: Aplicación de filtros en las subconsultas

## Uso del Dashboard

### Frontend Integration
El frontend puede consumir estas métricas a través de:

```javascript
// Ejemplo de uso en React
import { useBullsMetrics } from './hooks/useBullsMetrics';

function DashboardComponent() {
  const {
    panoramaData,
    sociogramaData,
    contextoData,
    comparativasData,
    accionesData,
    loading,
    error
  } = useBullsMetrics();

  // Renderizar componentes del dashboard
}
```

### Llamadas a la API
```javascript
// Obtener panorama general
const { data: panorama } = await supabase.rpc('calcular_panorama_curso');

// Obtener datos del sociograma
const { data: sociograma } = await supabase.rpc('calcular_sociograma');

// Obtener datos de contexto
const { data: contexto } = await supabase.rpc('calcular_contexto');

// Obtener comparativas
const { data: comparativas } = await supabase.rpc('calcular_comparativas');

// Obtener acciones recomendadas
const { data: acciones } = await supabase.rpc('generar_acciones_recomendadas');
```

## Testing y Validación

### Script de Pruebas
Se ha creado un script completo de pruebas (`test-bulls-dashboard-views.js`) que:
- Verifica el funcionamiento de todas las vistas
- Prueba todas las funciones SQL
- Genera un reporte resumen del estado del dashboard
- Valida la integridad de los datos

### Ejecución de Pruebas
```bash
# Ejecutar pruebas del dashboard
node scripts/test-bulls-dashboard-views.js
```

## Migración y Despliegue

### Archivos de Migración
1. `20241225000001_create_bulls_dashboard_views.sql` - Vistas del dashboard
2. `20241225000002_create_bulls_metrics_functions.sql` - Funciones de métricas

### Orden de Aplicación
```bash
# Aplicar migraciones en orden
supabase db push
```

## Monitoreo y Mantenimiento

### Métricas de Rendimiento
- Tiempo de respuesta de las vistas
- Uso de índices en consultas
- Volumen de datos procesados

### Logs y Debugging
```sql
-- Verificar rendimiento de vistas
EXPLAIN ANALYZE SELECT * FROM vw_roles LIMIT 100;

-- Verificar uso de índices
SELECT schemaname, tablename, indexname, idx_scan 
FROM pg_stat_user_indexes 
WHERE schemaname = 'public';
```

## Conclusión

La implementación del dashboard BULL-S proporciona:

✅ **Métricas Completas**: Todas las métricas requeridas implementadas
✅ **Rendimiento Optimizado**: Índices y consultas optimizadas
✅ **Seguridad Robusta**: RLS y permisos adecuados
✅ **Escalabilidad**: Diseño preparado para crecimiento
✅ **Mantenibilidad**: Código documentado y estructurado
✅ **Testing**: Suite completa de pruebas

El sistema está listo para producción y puede manejar el análisis completo de convivencia escolar según las especificaciones del proyecto BULL-S.