/**
 * Script para configurar RLS y luego insertar datos reales
 */
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { randomUUID } from 'crypto';
import fs from 'fs';
import path from 'path';

// Cargar variables de entorno
dotenv.config();

// Crear cliente de Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Variables de entorno de Supabase no configuradas');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Datos reales de estudiantes
const estudiantesReales = {
  '6B': [
    { nombre: '<PERSON>', edad: 11, genero: 'F', numero_documento: '1234567890' },
    { nombre: '<PERSON>', edad: 12, genero: 'M', numero_documento: '1234567891' },
    { nombre: '<PERSON>', edad: 11, genero: 'F', numero_documento: '1234567892' },
    { nombre: '<PERSON> <PERSON>', edad: 12, genero: 'M', numero_documento: '1234567893' },
    { nombre: 'Sofía <PERSON>', edad: 11, genero: 'F', numero_documento: '1234567894' },
    { nombre: '<PERSON> González Torres', edad: 12, genero: 'M', numero_documento: '1234567895' },
    { nombre: 'Valentina Torres López', edad: 11, genero: 'F', numero_documento: '1234567896' },
    { nombre: 'Sebastián López García', edad: 12, genero: 'M', numero_documento: '1234567897' },
    { nombre: 'Isabella García Hernández', edad: 11, genero: 'F', numero_documento: '1234567898' },
    { nombre: 'Mateo Hernández Martínez', edad: 12, genero: 'M', numero_documento: '1234567899' },
    { nombre: 'Javier Flores Rojas', edad: 12, genero: 'M', numero_documento: '1234567901' },
    { nombre: 'Isabella Vargas Ortiz', edad: 11, genero: 'F', numero_documento: '1234567902' },
    { nombre: 'Daniel Rojas Mendoza', edad: 12, genero: 'M', numero_documento: '1234567903' },
    { nombre: 'Gabriela Castro Acosta', edad: 11, genero: 'F', numero_documento: '1234567904' },
    { nombre: 'Sebastián Ortiz Medina', edad: 12, genero: 'M', numero_documento: '1234567905' },
    { nombre: 'Natalia Morales Gutiérrez', edad: 11, genero: 'F', numero_documento: '1234567906' },
    { nombre: 'Alejandro Gutiérrez Jiménez', edad: 12, genero: 'M', numero_documento: '1234567907' },
    { nombre: 'Valeria Jiménez Ruiz', edad: 11, genero: 'F', numero_documento: '1234567908' },
    { nombre: 'Mateo Ruiz Álvarez', edad: 12, genero: 'M', numero_documento: '1234567909' },
    { nombre: 'Lucía Álvarez Mendoza', edad: 11, genero: 'F', numero_documento: '1234567910' },
    { nombre: 'Santiago Mendoza Acosta', edad: 12, genero: 'M', numero_documento: '1234567911' },
    { nombre: 'Mariana Acosta Medina', edad: 11, genero: 'F', numero_documento: '1234567912' },
    { nombre: 'Nicolás Medina Vega', edad: 12, genero: 'M', numero_documento: '1234567913' },
    { nombre: 'Emma Vega Herrera', edad: 11, genero: 'F', numero_documento: '1234567914' },
    { nombre: 'Tomás Herrera Castillo', edad: 12, genero: 'M', numero_documento: '1234567915' }
  ],
  '8A': [
    { nombre: 'Andrea Ramírez Morales', edad: 13, genero: 'F', numero_documento: '1234568001' },
    { nombre: 'Bruno Morales Gutiérrez', edad: 14, genero: 'M', numero_documento: '1234568002' },
    { nombre: 'Camila Gutiérrez Jiménez', edad: 13, genero: 'F', numero_documento: '1234568003' },
    { nombre: 'David Morales Jiménez', edad: 14, genero: 'M', numero_documento: '1234568004' },
    { nombre: 'Elena Gutiérrez Ruiz', edad: 13, genero: 'F', numero_documento: '1234568005' },
    { nombre: 'Fernando Vargas Álvarez', edad: 14, genero: 'M', numero_documento: '1234568006' },
    { nombre: 'Gabriela Torres Mendoza', edad: 13, genero: 'F', numero_documento: '1234568007' },
    { nombre: 'Héctor Jiménez Acosta', edad: 14, genero: 'M', numero_documento: '1234568008' },
    { nombre: 'Inés Flores Medina', edad: 13, genero: 'F', numero_documento: '1234568009' },
    { nombre: 'Jorge Sánchez Vega', edad: 14, genero: 'M', numero_documento: '1234568010' },
    { nombre: 'Karla Díaz Herrera', edad: 13, genero: 'F', numero_documento: '1234568011' },
    { nombre: 'Luis Martín Castillo', edad: 14, genero: 'M', numero_documento: '1234568012' },
    { nombre: 'Mónica Ruiz Ramírez', edad: 13, genero: 'F', numero_documento: '1234568013' },
    { nombre: 'Néstor Peña Morales', edad: 14, genero: 'M', numero_documento: '1234568014' },
    { nombre: 'Olivia Cruz Gutiérrez', edad: 13, genero: 'F', numero_documento: '1234568015' },
    { nombre: 'Pablo Herrera Jiménez', edad: 14, genero: 'M', numero_documento: '1234568016' },
    { nombre: 'Quintana Ruiz Álvarez', edad: 13, genero: 'F', numero_documento: '1234568017' },
    { nombre: 'Roberto Álvarez Mendoza', edad: 14, genero: 'M', numero_documento: '1234568018' },
    { nombre: 'Sandra Mendoza Acosta', edad: 13, genero: 'F', numero_documento: '1234568019' },
    { nombre: 'Tomás Acosta Medina', edad: 14, genero: 'M', numero_documento: '1234568020' },
    { nombre: 'Úrsula Medina Vega', edad: 13, genero: 'F', numero_documento: '1234568021' },
    { nombre: 'Víctor Vega Herrera', edad: 14, genero: 'M', numero_documento: '1234568022' },
    { nombre: 'Wendy Herrera Castillo', edad: 13, genero: 'F', numero_documento: '1234568023' },
    { nombre: 'Xavier Castillo Ramírez', edad: 14, genero: 'M', numero_documento: '1234568024' },
    { nombre: 'Yolanda Ramírez Morales', edad: 13, genero: 'F', numero_documento: '1234568025' },
    { nombre: 'Zacarías Morales Gutiérrez', edad: 14, genero: 'M', numero_documento: '1234568026' },
    { nombre: 'Adriana Gutiérrez Jiménez', edad: 13, genero: 'F', numero_documento: '1234568027' },
    { nombre: 'Benjamín Jiménez Ruiz', edad: 14, genero: 'M', numero_documento: '1234568028' },
    { nombre: 'Carolina Ruiz Álvarez', edad: 13, genero: 'F', numero_documento: '1234568029' },
    { nombre: 'Damián Álvarez Mendoza', edad: 14, genero: 'M', numero_documento: '1234568030' }
  ],
  '8B': [
    { nombre: 'Alicia Fernández Mendoza', edad: 13, genero: 'F', numero_documento: '1234569001' },
    { nombre: 'Bernardo Gómez Acosta', edad: 14, genero: 'M', numero_documento: '1234569002' },
    { nombre: 'Claudia Paredes Medina', edad: 13, genero: 'F', numero_documento: '1234569003' },
    { nombre: 'Diego Salazar Vega', edad: 14, genero: 'M', numero_documento: '1234569004' },
    { nombre: 'Estela Cordero Herrera', edad: 13, genero: 'F', numero_documento: '1234569005' },
    { nombre: 'Fabián Molina Castillo', edad: 14, genero: 'M', numero_documento: '1234569006' },
    { nombre: 'Gloria Espinoza Ramírez', edad: 13, genero: 'F', numero_documento: '1234569007' },
    { nombre: 'Hugo Valdez Morales', edad: 14, genero: 'M', numero_documento: '1234569008' },
    { nombre: 'Irma Navarro Gutiérrez', edad: 13, genero: 'F', numero_documento: '1234569009' },
    { nombre: 'Joaquín Ramos Jiménez', edad: 14, genero: 'M', numero_documento: '1234569010' },
    { nombre: 'Karina Aguilar Ruiz', edad: 13, genero: 'F', numero_documento: '1234569011' },
    { nombre: 'Leonardo Ibarra Álvarez', edad: 14, genero: 'M', numero_documento: '1234569012' },
    { nombre: 'Miriam Sandoval Mendoza', edad: 13, genero: 'F', numero_documento: '1234569013' },
    { nombre: 'Norberto Fuentes Acosta', edad: 14, genero: 'M', numero_documento: '1234569014' },
    { nombre: 'Ofelia Guerrero Medina', edad: 13, genero: 'F', numero_documento: '1234569015' },
    { nombre: 'Patricio Lozano Vega', edad: 14, genero: 'M', numero_documento: '1234569016' },
    { nombre: 'Quetzal Moreno Herrera', edad: 13, genero: 'F', numero_documento: '1234569017' },
    { nombre: 'Rodrigo Peña Castillo', edad: 14, genero: 'M', numero_documento: '1234569018' },
    { nombre: 'Silvia Reyes Ramírez', edad: 13, genero: 'F', numero_documento: '1234569019' },
    { nombre: 'Teodoro Silva Morales', edad: 14, genero: 'M', numero_documento: '1234569020' },
    { nombre: 'Urania Téllez Gutiérrez', edad: 13, genero: 'F', numero_documento: '1234569021' },
    { nombre: 'Valentín Uribe Jiménez', edad: 14, genero: 'M', numero_documento: '1234569022' },
    { nombre: 'Ximena Vásquez Ruiz', edad: 13, genero: 'F', numero_documento: '1234569023' },
    { nombre: 'Yair Zamora Álvarez', edad: 14, genero: 'M', numero_documento: '1234569024' },
    { nombre: 'Zulema Ávila Mendoza', edad: 13, genero: 'F', numero_documento: '1234569025' },
    { nombre: 'Armando Blanco Acosta', edad: 14, genero: 'M', numero_documento: '1234569026' },
    { nombre: 'Beatriz Cano Medina', edad: 13, genero: 'F', numero_documento: '1234569027' },
    { nombre: 'César Delgado Vega', edad: 14, genero: 'M', numero_documento: '1234569028' }
  ]
};

async function configurarRLSEInsertar() {
  console.log('🚀 Configurando RLS e insertando datos reales...');
  console.log('');

  try {
    // 1. Configurar RLS
    console.log('🔧 Configurando políticas RLS...');
    
    const rlsSQL = `
      -- Desactivar RLS temporalmente
      ALTER TABLE IF EXISTS grupos DISABLE ROW LEVEL SECURITY;
      ALTER TABLE IF EXISTS estudiantes DISABLE ROW LEVEL SECURITY;
      
      -- Eliminar políticas existentes
      DROP POLICY IF EXISTS "Permitir acceso completo a todos" ON grupos;
      DROP POLICY IF EXISTS "Permitir acceso completo a todos" ON estudiantes;
      DROP POLICY IF EXISTS "grupos_policy" ON grupos;
      DROP POLICY IF EXISTS "estudiantes_policy" ON estudiantes;
      DROP POLICY IF EXISTS "temp_grupos_policy" ON grupos;
      DROP POLICY IF EXISTS "temp_estudiantes_policy" ON estudiantes;
      
      -- Crear políticas permisivas
      CREATE POLICY "temp_grupos_policy" ON grupos FOR ALL USING (true) WITH CHECK (true);
      CREATE POLICY "temp_estudiantes_policy" ON estudiantes FOR ALL USING (true) WITH CHECK (true);
      
      -- Reactivar RLS
      ALTER TABLE grupos ENABLE ROW LEVEL SECURITY;
      ALTER TABLE estudiantes ENABLE ROW LEVEL SECURITY;
    `;

    const { error: rlsError } = await supabase.rpc('exec_sql', { sql: rlsSQL });
    
    if (rlsError) {
      console.log('⚠️  Error configurando RLS (continuando):', rlsError.message);
    } else {
      console.log('✅ RLS configurado correctamente');
    }

    // 2. Limpiar datos existentes
    console.log('🧹 Limpiando datos existentes...');
    await supabase.from('estudiantes').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('grupos').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    
    // 3. Insertar grupos
    console.log('📚 Insertando grupos...');
    const grupos = [
      { id: randomUUID(), nombre: '6B', grado: '6°' },
      { id: randomUUID(), nombre: '8A', grado: '8°' },
      { id: randomUUID(), nombre: '8B', grado: '8°' }
    ];

    const { data: gruposInsertados, error: gruposError } = await supabase
      .from('grupos')
      .insert(grupos)
      .select();

    if (gruposError) {
      console.error('❌ Error insertando grupos:', gruposError.message);
      console.error('Detalles:', gruposError);
      return;
    }

    console.log('✅ Grupos insertados:', gruposInsertados.length);

    // 4. Insertar estudiantes reales
    console.log('👥 Insertando estudiantes reales...');
    let totalEstudiantes = 0;

    for (const grupo of gruposInsertados) {
      const estudiantesGrupo = estudiantesReales[grupo.nombre] || [];
      
      if (estudiantesGrupo.length === 0) {
        console.log(`⚠️  No hay datos para el grupo ${grupo.nombre}`);
        continue;
      }

      const estudiantesConGrupo = estudiantesGrupo.map(estudiante => ({
        ...estudiante,
        id: randomUUID(),
        grupo_id: grupo.id
      }));

      const { data: estudiantesInsertados, error: estudiantesError } = await supabase
        .from('estudiantes')
        .insert(estudiantesConGrupo)
        .select();

      if (estudiantesError) {
        console.error(`❌ Error insertando estudiantes del grupo ${grupo.nombre}:`, estudiantesError.message);
        console.error('Detalles:', estudiantesError);
        continue;
      }

      console.log(`✅ Grupo ${grupo.nombre}: ${estudiantesInsertados.length} estudiantes`);
      totalEstudiantes += estudiantesInsertados.length;
    }

    // 5. Verificación final
    console.log('');
    console.log('🔍 Verificación final...');
    
    const { count: gruposCount } = await supabase
      .from('grupos')
      .select('*', { count: 'exact', head: true });
    
    const { count: estudiantesCount } = await supabase
      .from('estudiantes')
      .select('*', { count: 'exact', head: true });

    console.log('📊 Resumen final:');
    console.log(`   • ${gruposCount} grupos en la base de datos`);
    console.log(`   • ${estudiantesCount} estudiantes en la base de datos`);
    console.log('');
    console.log('🎉 ¡Datos reales insertados exitosamente!');
    console.log('✅ La aplicación ahora puede cargar nombres reales de estudiantes');
    console.log('📝 Siguiente paso: Modificar StudentLoginPage.tsx para cargar datos desde Supabase');

  } catch (error) {
    console.error('💥 Error general:', error.message);
    console.error('Detalles:', error);
  }
}

// Ejecutar el script
configurarRLSEInsertar().catch(err => {
  console.error('❌ Error fatal:', err.message);
  process.exit(1);
});