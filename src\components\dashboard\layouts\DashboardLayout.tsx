import React from 'react';
import { RefreshCw } from 'lucide-react';
import { Button } from '../../ui/button';

interface DashboardLayoutProps {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
  onRefresh?: () => void;
  lastUpdated?: string;
  loading?: boolean;
  actions?: React.ReactNode;
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  title,
  subtitle,
  children,
  onRefresh,
  lastUpdated,
  loading = false,
  actions
}) => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-4">
        {/* Title Section */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
            {subtitle && (
              <p className="text-muted-foreground mt-1">{subtitle}</p>
            )}
          </div>
          
          {onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              disabled={loading}
              className="flex items-center space-x-2"
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              <span>Actualizar</span>
            </Button>
          )}
        </div>
        
        {/* Filters Section */}
        {actions && (
          <div>
            {actions}
          </div>
        )}
      </div>

      {/* Last updated info */}
      {lastUpdated && (
        <div className="text-sm text-muted-foreground">
          Última actualización: {new Date(lastUpdated).toLocaleString('es-ES')}
        </div>
      )}

      {/* Content */}
      <div className="space-y-6">
        {children}
      </div>
    </div>
  );
};