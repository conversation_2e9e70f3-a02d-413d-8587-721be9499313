import React, { useState, useEffect } from 'react';
import { supabase } from '../../../lib/supabaseClient';

const SupabaseDiagnosticsPage: React.FC = () => {
  const [diagnostics, setDiagnostics] = useState({
    connection: null,
    user: null,
    session: null,
    tables: [],
    error: null
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    runDiagnostics();
  }, []);

  const runDiagnostics = async () => {
    try {
      setLoading(true);
      
      // Test connection
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      // Test basic table access
      const { data: tables, error: tablesError } = await supabase
        .from('usuarios')
        .select('*')
        .limit(1);

      setDiagnostics({
        connection: !userError && !sessionError ? 'success' : 'failed',
        user: user,
        session: session,
        tables: tables || [],
        error: userError || sessionError || tablesError
      });
    } catch (error) {
      setDiagnostics(prev => ({
        ...prev,
        error: error.message
      }));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-4">Diagnóstico de Supabase</h1>
      
      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Ejecutando diagnósticos...</p>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="font-semibold text-gray-900 mb-2">Conexión</h3>
            <p className={`text-sm ${diagnostics.connection === 'success' ? 'text-green-600' : 'text-red-600'}`}>
              {diagnostics.connection === 'success' ? '✅ Conexión exitosa' : '❌ Error de conexión'}
            </p>
          </div>

          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="font-semibold text-gray-900 mb-2">Usuario actual</h3>
            <pre className="text-xs bg-gray-50 p-2 rounded">
              {JSON.stringify(diagnostics.user, null, 2)}
            </pre>
          </div>

          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="font-semibold text-gray-900 mb-2">Sesión</h3>
            <pre className="text-xs bg-gray-50 p-2 rounded">
              {JSON.stringify(diagnostics.session, null, 2)}
            </pre>
          </div>

          {diagnostics.error && (
            <div className="bg-red-50 p-4 rounded-lg">
              <h3 className="font-semibold text-red-900 mb-2">Error</h3>
              <p className="text-red-700 text-sm">{diagnostics.error}</p>
            </div>
          )}

          <button
            onClick={runDiagnostics}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Re-ejecutar diagnóstico
          </button>
        </div>
      )}
    </div>
  );
};

export default SupabaseDiagnosticsPage;