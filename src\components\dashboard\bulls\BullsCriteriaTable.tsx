import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Badge } from '../../ui/badge';
import { Button } from '../../ui/button';
import { Input } from '../../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../ui/table';
import { Download, Search, ArrowUpDown } from 'lucide-react';
import { EstudianteRiesgoBulls } from '../../../hooks/dashboard/useBullsCriteria';

interface BullsCriteriaTableProps {
  estudiantes: EstudianteRiesgoBulls[];
  loading?: boolean;
  showOnlyAtRisk?: boolean;
  className?: string;
}

type SortField = 'student_name' | 'curso' | 'rol_riesgo' | 'porcentaje_agresor' | 'porcentaje_victima';
type SortDirection = 'asc' | 'desc';

const BullsCriteriaTable: React.FC<BullsCriteriaTableProps> = ({
  estudiantes,
  loading = false,
  showOnlyAtRisk = true,
  className = ''
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [courseFilter, setCourseFilter] = useState<string>('all');
  const [sortField, setSortField] = useState<SortField>('porcentaje_agresor');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');

  // Obtener cursos únicos para el filtro
  const uniqueCourses = useMemo(() => {
    const courses = [...new Set(estudiantes.map(e => e.curso))];
    return courses.sort();
  }, [estudiantes]);

  // Filtrar y ordenar estudiantes
  const filteredAndSortedStudents = useMemo(() => {
    let filtered = estudiantes;

    // Filtrar solo estudiantes en riesgo si está habilitado
    if (showOnlyAtRisk) {
      filtered = filtered.filter(e => e.en_riesgo);
    }

    // Filtrar por término de búsqueda
    if (searchTerm) {
      filtered = filtered.filter(e => 
        e.student_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        e.curso.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtrar por rol
    if (roleFilter !== 'all') {
      filtered = filtered.filter(e => e.rol_riesgo === roleFilter);
    }

    // Filtrar por curso
    if (courseFilter !== 'all') {
      filtered = filtered.filter(e => e.curso === courseFilter);
    }

    // Ordenar
    filtered.sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;

      switch (sortField) {
        case 'student_name':
          aValue = a.student_name;
          bValue = b.student_name;
          break;
        case 'curso':
          aValue = a.curso;
          bValue = b.curso;
          break;
        case 'rol_riesgo':
          aValue = a.rol_riesgo;
          bValue = b.rol_riesgo;
          break;
        case 'porcentaje_agresor':
          aValue = a.porcentaje_agresor;
          bValue = b.porcentaje_agresor;
          break;
        case 'porcentaje_victima':
          aValue = a.porcentaje_victima;
          bValue = b.porcentaje_victima;
          break;
        default:
          aValue = a.student_name;
          bValue = b.student_name;
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.localeCompare(bValue);
        return sortDirection === 'asc' ? comparison : -comparison;
      } else {
        const comparison = (aValue as number) - (bValue as number);
        return sortDirection === 'asc' ? comparison : -comparison;
      }
    });

    return filtered;
  }, [estudiantes, searchTerm, roleFilter, courseFilter, sortField, sortDirection, showOnlyAtRisk]);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const getRoleBadgeColor = (rol: string) => {
    switch (rol) {
      case 'Agresor':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'Víctima':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Agresor-Víctima':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-green-100 text-green-800 border-green-200';
    }
  };

  const getRoleIcon = (rol: string) => {
    switch (rol) {
      case 'Agresor':
        return <Target className="w-4 h-4" />;
      case 'Víctima':
        return <Shield className="w-4 h-4" />;
      case 'Agresor-Víctima':
        return <AlertTriangle className="w-4 h-4" />;
      default:
        return <Users className="w-4 h-4" />;
    }
  };

  const exportToCSV = () => {
    const headers = [
      'Curso',
      'Estudiante',
      'Rol de Riesgo',
      '% Agresor',
      '% Víctima',
      'Nominaciones Agresor',
      'Nominaciones Víctima',
      'En Riesgo'
    ];

    const csvContent = [
      headers.join(','),
      ...filteredAndSortedStudents.map(e => [
        `"${e.curso}"`,
        `"${e.student_name}"`,
        `"${e.rol_riesgo}"`,
        e.porcentaje_agresor,
        e.porcentaje_victima,
        e.nominaciones_agresor,
        e.nominaciones_victima,
        e.en_riesgo ? 'Sí' : 'No'
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `criterio-bulls-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Cargando estudiantes...
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-12 bg-gray-200 rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Estudiantes según Criterio BULL-S
            <Badge className="bg-blue-100 text-blue-800 border-blue-200">
              {filteredAndSortedStudents.length}
            </Badge>
          </div>
          <Button
            onClick={exportToCSV}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            disabled={filteredAndSortedStudents.length === 0}
          >
            <Download className="w-4 h-4" />
            Exportar CSV
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Filtros */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Buscar estudiante o curso..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <Select value={roleFilter} onValueChange={setRoleFilter}>
            <SelectTrigger className="w-48">
              <Filter className="w-4 h-4 mr-2" />
              <SelectValue placeholder="Filtrar por rol" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos los roles</SelectItem>
              <SelectItem value="Agresor">Agresor</SelectItem>
              <SelectItem value="Víctima">Víctima</SelectItem>
              <SelectItem value="Agresor-Víctima">Agresor-Víctima</SelectItem>
              {!showOnlyAtRisk && <SelectItem value="Sin Riesgo">Sin Riesgo</SelectItem>}
            </SelectContent>
          </Select>
          <Select value={courseFilter} onValueChange={setCourseFilter}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filtrar por curso" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos los cursos</SelectItem>
              {uniqueCourses.map(course => (
                <SelectItem key={course} value={course}>{course}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Tabla */}
        {filteredAndSortedStudents.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Users className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p className="text-lg font-medium mb-2">No se encontraron estudiantes</p>
            <p className="text-sm">
              {showOnlyAtRisk 
                ? 'No hay estudiantes en riesgo según el criterio BULL-S'
                : 'Intenta ajustar los filtros de búsqueda'
              }
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort('curso')}
                  >
                    Curso {getSortIcon('curso')}
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort('student_name')}
                  >
                    Estudiante {getSortIcon('student_name')}
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort('rol_riesgo')}
                  >
                    Rol de Riesgo {getSortIcon('rol_riesgo')}
                  </TableHead>
                  <TableHead 
                    className="text-center cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort('porcentaje_agresor')}
                  >
                    % Agresor {getSortIcon('porcentaje_agresor')}
                  </TableHead>
                  <TableHead 
                    className="text-center cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort('porcentaje_victima')}
                  >
                    % Víctima {getSortIcon('porcentaje_victima')}
                  </TableHead>
                  <TableHead className="text-center">Nominaciones</TableHead>
                  <TableHead className="text-center">Estado</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAndSortedStudents.map((estudiante) => (
                  <TableRow 
                    key={`${estudiante.student_id}-${estudiante.grupo_id}`}
                    className="hover:bg-gray-50"
                  >
                    <TableCell className="font-medium">{estudiante.curso}</TableCell>
                    <TableCell>{estudiante.student_name}</TableCell>
                    <TableCell>
                      <Badge className={`${getRoleBadgeColor(estudiante.rol_riesgo)} flex items-center gap-1 w-fit`}>
                        {getRoleIcon(estudiante.rol_riesgo)}
                        {estudiante.rol_riesgo}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-center">
                      <span className={`font-medium ${
                        estudiante.porcentaje_agresor >= 25 ? 'text-red-600' : 'text-gray-500'
                      }`}>
                        {estudiante.porcentaje_agresor}%
                      </span>
                    </TableCell>
                    <TableCell className="text-center">
                      <span className={`font-medium ${
                        estudiante.porcentaje_victima >= 25 ? 'text-blue-600' : 'text-gray-500'
                      }`}>
                        {estudiante.porcentaje_victima}%
                      </span>
                    </TableCell>
                    <TableCell className="text-center text-sm text-gray-600">
                      <div className="flex flex-col">
                        <span>A: {estudiante.nominaciones_agresor}</span>
                        <span>V: {estudiante.nominaciones_victima}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      {estudiante.en_riesgo ? (
                        <Badge className="bg-red-100 text-red-800 border-red-200">
                          En Riesgo
                        </Badge>
                      ) : (
                        <Badge className="bg-green-100 text-green-800 border-green-200">
                          Normal
                        </Badge>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}

        {/* Información adicional */}
        <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="font-semibold text-blue-900 mb-2">Criterio BULL-S</h4>
          <p className="text-sm text-blue-800">
            Un estudiante está en riesgo si es nombrado por ≥25% de sus compañeros en roles de agresor 
            (items 5, 7, 9) o víctima (items 6, 8, 10). Los porcentajes se calculan sobre el total de 
            estudiantes en el grupo.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default BullsCriteriaTable;