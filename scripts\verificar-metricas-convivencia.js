/**
 * Script para verificar y calcular métricas de convivencia escolar
 * Basado en los datos reales de la tabla respuestas
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Error: Variables de entorno de Supabase no encontradas');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function verificarEstructuraDatos() {
  console.log('🔍 Verificando estructura de datos en Supabase...');
  
  try {
    // Verificar tabla respuestas
    console.log('\n📊 Verificando tabla respuestas:');
    const { data: respuestas, error: errorRespuestas } = await supabase
      .from('respuestas')
      .select('*')
      .limit(5);
    
    if (errorRespuestas) {
      console.error('❌ Error accediendo a respuestas:', errorRespuestas);
    } else {
      console.log(`✅ Tabla respuestas: ${respuestas?.length || 0} registros encontrados`);
      if (respuestas && respuestas.length > 0) {
        console.log('📋 Estructura de respuestas:', Object.keys(respuestas[0]));
        console.log('📝 Ejemplo de respuesta:', respuestas[0]);
      }
    }

    // Verificar tabla estudiantes
    console.log('\n👥 Verificando tabla estudiantes:');
    const { data: estudiantes, error: errorEstudiantes } = await supabase
      .from('estudiantes')
      .select('*')
      .limit(5);
    
    if (errorEstudiantes) {
      console.error('❌ Error accediendo a estudiantes:', errorEstudiantes);
    } else {
      console.log(`✅ Tabla estudiantes: ${estudiantes?.length || 0} registros encontrados`);
      if (estudiantes && estudiantes.length > 0) {
        console.log('📋 Estructura de estudiantes:', Object.keys(estudiantes[0]));
      }
    }

    // Verificar tabla grupos
    console.log('\n🏫 Verificando tabla grupos:');
    const { data: grupos, error: errorGrupos } = await supabase
      .from('grupos')
      .select('*');
    
    if (errorGrupos) {
      console.error('❌ Error accediendo a grupos:', errorGrupos);
    } else {
      console.log(`✅ Tabla grupos: ${grupos?.length || 0} registros encontrados`);
      if (grupos && grupos.length > 0) {
        console.log('📋 Grupos disponibles:');
        grupos.forEach(grupo => {
          console.log(`  - ${grupo.nombre} (${grupo.grado}${grupo.seccion || ''})`);
        });
      }
    }

    // Verificar vistas existentes
    console.log('\n🔍 Verificando vistas del dashboard:');
    const vistas = [
      'view_dashboard_bullying_indicators',
      'view_estudiantes_con_respuestas',
      'view_grupos_con_respuestas'
    ];
    
    for (const vista of vistas) {
      const { data, error } = await supabase
        .from(vista)
        .select('*')
        .limit(3);
      
      if (error) {
        console.log(`❌ ${vista}: ${error.message}`);
      } else {
        console.log(`✅ ${vista}: ${data?.length || 0} registros`);
        if (data && data.length > 0) {
          console.log(`   📋 Columnas: ${Object.keys(data[0]).join(', ')}`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Error general:', error);
  }
}

async function calcularMetricasConvivencia() {
  console.log('\n📈 Calculando métricas de convivencia escolar...');
  
  try {
    // Obtener datos de estudiantes y respuestas
    const { data: estudiantes, error: errorEstudiantes } = await supabase
      .from('estudiantes')
      .select('*');
    
    const { data: respuestas, error: errorRespuestas } = await supabase
      .from('respuestas')
      .select('*');
    
    if (errorEstudiantes || errorRespuestas) {
      console.error('❌ Error obteniendo datos:', errorEstudiantes || errorRespuestas);
      return;
    }
    
    if (!estudiantes || estudiantes.length === 0) {
      console.log('⚠️  No hay estudiantes registrados');
      return;
    }
    
    if (!respuestas || respuestas.length === 0) {
      console.log('⚠️  No hay respuestas registradas');
      console.log('\n📊 Métricas simuladas basadas en estudiantes registrados:');
      
      // Calcular métricas básicas sin respuestas
      const totalEstudiantes = estudiantes.length;
      const estudiantesPorGrupo = {};
      
      estudiantes.forEach(est => {
        if (!estudiantesPorGrupo[est.grupo_id]) {
          estudiantesPorGrupo[est.grupo_id] = [];
        }
        estudiantesPorGrupo[est.grupo_id].push(est);
      });
      
      console.log(`\n📊 Resumen General:`);
      console.log(`   👥 Total de estudiantes: ${totalEstudiantes}`);
      console.log(`   🏫 Grupos activos: ${Object.keys(estudiantesPorGrupo).length}`);
      
      Object.entries(estudiantesPorGrupo).forEach(([grupoId, estudiantesGrupo]) => {
        console.log(`\n🏫 Grupo ${grupoId}:`);
        console.log(`   👥 Estudiantes: ${estudiantesGrupo.length}`);
        console.log(`   📊 Distribución por género:`);
        const porGenero = estudiantesGrupo.reduce((acc, est) => {
          acc[est.genero || 'No especificado'] = (acc[est.genero || 'No especificado'] || 0) + 1;
          return acc;
        }, {});
        Object.entries(porGenero).forEach(([genero, cantidad]) => {
          console.log(`      ${genero}: ${cantidad} (${((cantidad/estudiantesGrupo.length)*100).toFixed(1)}%)`);
        });
      });
      
      console.log('\n⚠️  Para obtener métricas de convivencia reales, es necesario:');
      console.log('   1. Que los estudiantes completen el cuestionario BULL-S');
      console.log('   2. Tener respuestas sociométricas (elecciones y rechazos)');
      console.log('   3. Datos sobre percepciones de bullying');
      
      return;
    }
    
    // Si hay respuestas, calcular métricas reales
    console.log(`\n📊 Datos disponibles:`);
    console.log(`   👥 Estudiantes: ${estudiantes.length}`);
    console.log(`   📝 Respuestas: ${respuestas.length}`);
    
    // Analizar tipos de preguntas en las respuestas
    const tiposPreguntas = {};
    respuestas.forEach(resp => {
      const tipo = resp.pregunta_id || 'sin_tipo';
      tiposPreguntas[tipo] = (tiposPreguntas[tipo] || 0) + 1;
    });
    
    console.log(`\n📋 Tipos de preguntas respondidas:`);
    Object.entries(tiposPreguntas).forEach(([tipo, cantidad]) => {
      console.log(`   ${tipo}: ${cantidad} respuestas`);
    });
    
    // Calcular participación por estudiante
    const participacion = {};
    respuestas.forEach(resp => {
      const estudianteId = resp.estudiante_id;
      participacion[estudianteId] = (participacion[estudianteId] || 0) + 1;
    });
    
    const estudiantesConRespuestas = Object.keys(participacion).length;
    const porcentajeParticipacion = ((estudiantesConRespuestas / estudiantes.length) * 100).toFixed(1);
    
    console.log(`\n📊 Participación:`);
    console.log(`   👥 Estudiantes que respondieron: ${estudiantesConRespuestas}/${estudiantes.length} (${porcentajeParticipacion}%)`);
    
    if (estudiantesConRespuestas > 0) {
      const promedioRespuestasPorEstudiante = (respuestas.length / estudiantesConRespuestas).toFixed(1);
      console.log(`   📝 Promedio de respuestas por estudiante: ${promedioRespuestasPorEstudiante}`);
      
      // Aquí se pueden agregar más cálculos específicos basados en el contenido real de las respuestas
      console.log('\n✅ Datos suficientes para análisis de convivencia escolar');
    }
    
  } catch (error) {
    console.error('❌ Error calculando métricas:', error);
  }
}

async function mostrarRecomendaciones() {
  console.log('\n💡 Recomendaciones para implementar métricas de convivencia:');
  console.log('\n1. 📊 Tasas de Incidencia General:');
  console.log('   - Identificar agresores, víctimas y observadores');
  console.log('   - Basado en respuestas sobre comportamientos de bullying');
  console.log('   - Calcular porcentajes por rol');
  
  console.log('\n2. 🤝 Estatus Sociométrico Promedio:');
  console.log('   - Analizar elecciones y rechazos entre compañeros');
  console.log('   - Clasificar: Popular, Promedio, Aislado, Rechazado, Controvertido');
  console.log('   - Basado en nominaciones sociométricas');
  
  console.log('\n3. 🔗 Cohesión Grupal Promedio:');
  console.log('   - Medir densidad de relaciones positivas');
  console.log('   - Evaluar reciprocidad en las elecciones');
  console.log('   - Identificar subgrupos y estudiantes aislados');
  
  console.log('\n4. ⚠️  Dinámicas de Acoso Predominantes:');
  console.log('   - Patrones de agresión más comunes');
  console.log('   - Lugares y momentos de mayor riesgo');
  console.log('   - Tipos de bullying prevalentes (físico, verbal, social, ciberbullying)');
  
  console.log('\n🔧 Para implementar estas métricas:');
  console.log('   1. Asegurar que las respuestas incluyan datos sociométricos');
  console.log('   2. Crear algoritmos de clasificación de roles');
  console.log('   3. Implementar cálculos de índices sociométricos');
  console.log('   4. Desarrollar visualizaciones interactivas');
}

// Ejecutar verificación completa
async function main() {
  console.log('🔍 VERIFICACIÓN DE MÉTRICAS DE CONVIVENCIA ESCOLAR');
  console.log('=' .repeat(60));
  
  await verificarEstructuraDatos();
  await calcularMetricasConvivencia();
  await mostrarRecomendaciones();
  
  console.log('\n' + '='.repeat(60));
  console.log('✅ Verificación completada');
}

main().catch(console.error);