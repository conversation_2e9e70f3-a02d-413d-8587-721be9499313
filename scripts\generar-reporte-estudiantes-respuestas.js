import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Configurar dotenv
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuración de Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Error: Variables de entorno VITE_SUPABASE_URL y VITE_SUPABASE_ANON_KEY son requeridas');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function obtenerEstudiantesConRespuestas() {
  try {
    console.log('📊 Obteniendo datos de estudiantes...');
    
    // Obtener todos los estudiantes
    const { data: estudiantes, error: errorEstudiantes } = await supabase
      .from('estudiantes')
      .select('*')
      .order('id');
    
    if (errorEstudiantes) {
      throw new Error(`Error al obtener estudiantes: ${errorEstudiantes.message}`);
    }
    
    console.log(`✅ Encontrados ${estudiantes.length} estudiantes`);
    
    console.log('📝 Obteniendo respuestas...');
    
    // Obtener todas las respuestas
    const { data: respuestas, error: errorRespuestas } = await supabase
      .from('respuestas')
      .select('*')
      .order('estudiante_id, pregunta_id');
    
    if (errorRespuestas) {
      throw new Error(`Error al obtener respuestas: ${errorRespuestas.message}`);
    }
    
    console.log(`✅ Encontradas ${respuestas.length} respuestas`);
    
    // Agrupar respuestas por estudiante
    const respuestasPorEstudiante = {};
    respuestas.forEach(respuesta => {
      if (!respuestasPorEstudiante[respuesta.estudiante_id]) {
        respuestasPorEstudiante[respuesta.estudiante_id] = [];
      }
      respuestasPorEstudiante[respuesta.estudiante_id].push(respuesta);
    });
    
    // Combinar datos
    const datosCompletos = estudiantes.map(estudiante => {
      const respuestasEstudiante = respuestasPorEstudiante[estudiante.id] || [];
      return {
        estudiante: {
          id: estudiante.id,
          nombre: estudiante.nombre_estudiante,
          apellido: estudiante.apellido_estudiante,
          genero: estudiante.genero,
          codigo_anonimizado: estudiante.codigo_anonimizado,
          grado: estudiante.grado,
          institucion_id: estudiante.institucion_id,
          grupo_id: estudiante.grupo_id,
          edad: estudiante.edad,
          numero_documento: estudiante.numero_documento,
          fecha_creacion: estudiante.fecha_creacion
        },
        respuestas: respuestasEstudiante.map(resp => ({
          pregunta_id: resp.pregunta_id,
          opcion_respuesta_id: resp.opcion_respuesta_id,
          respuesta_texto: resp.respuesta_texto,
          fecha_respuesta: resp.fecha_respuesta
        })),
        total_respuestas: respuestasEstudiante.length
      };
    });
    
    return datosCompletos;
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    throw error;
  }
}

async function generarReporteJSON(datos) {
  const nombreArchivo = `reporte-estudiantes-respuestas-${new Date().toISOString().split('T')[0]}.json`;
  const rutaArchivo = path.join(__dirname, '..', nombreArchivo);
  
  const reporte = {
    fecha_generacion: new Date().toISOString(),
    total_estudiantes: datos.length,
    total_respuestas: datos.reduce((sum, item) => sum + item.total_respuestas, 0),
    estudiantes: datos
  };
  
  fs.writeFileSync(rutaArchivo, JSON.stringify(reporte, null, 2), 'utf8');
  console.log(`📄 Reporte JSON generado: ${rutaArchivo}`);
  
  return rutaArchivo;
}

async function generarReporteCSV(datos) {
  const nombreArchivo = `reporte-estudiantes-respuestas-${new Date().toISOString().split('T')[0]}.csv`;
  const rutaArchivo = path.join(__dirname, '..', nombreArchivo);
  
  let csvContent = 'ID_Estudiante,Nombre,Apellido,Genero,Codigo_Anonimizado,Grado,Grupo_ID,Edad,Numero_Documento,Pregunta_ID,Opcion_Respuesta_ID,Respuesta_Texto,Fecha_Respuesta\n';
  
  datos.forEach(item => {
    const estudiante = item.estudiante;
    if (item.respuestas.length === 0) {
      // Estudiante sin respuestas
      csvContent += `${estudiante.id},"${estudiante.nombre}","${estudiante.apellido}",${estudiante.genero},${estudiante.codigo_anonimizado},${estudiante.grado},${estudiante.grupo_id},${estudiante.edad},${estudiante.numero_documento || ''},,,,\n`;
    } else {
      // Estudiante con respuestas
      item.respuestas.forEach(respuesta => {
        csvContent += `${estudiante.id},"${estudiante.nombre}","${estudiante.apellido}",${estudiante.genero},${estudiante.codigo_anonimizado},${estudiante.grado},${estudiante.grupo_id},${estudiante.edad},${estudiante.numero_documento || ''},${respuesta.pregunta_id},${respuesta.opcion_respuesta_id || ''},"${respuesta.respuesta_texto || ''}",${respuesta.fecha_respuesta}\n`;
      });
    }
  });
  
  fs.writeFileSync(rutaArchivo, csvContent, 'utf8');
  console.log(`📊 Reporte CSV generado: ${rutaArchivo}`);
  
  return rutaArchivo;
}

async function generarResumenEstadisticas(datos) {
  const nombreArchivo = `resumen-estadisticas-${new Date().toISOString().split('T')[0]}.txt`;
  const rutaArchivo = path.join(__dirname, '..', nombreArchivo);
  
  const totalEstudiantes = datos.length;
  const totalRespuestas = datos.reduce((sum, item) => sum + item.total_respuestas, 0);
  const estudiantesConRespuestas = datos.filter(item => item.total_respuestas > 0).length;
  const estudiantesSinRespuestas = totalEstudiantes - estudiantesConRespuestas;
  
  // Estadísticas por género
  const porGenero = datos.reduce((acc, item) => {
    const genero = item.estudiante.genero || 'No especificado';
    if (!acc[genero]) acc[genero] = 0;
    acc[genero]++;
    return acc;
  }, {});
  
  // Estadísticas por grado
  const porGrado = datos.reduce((acc, item) => {
    const grado = item.estudiante.grado || 'No especificado';
    if (!acc[grado]) acc[grado] = 0;
    acc[grado]++;
    return acc;
  }, {});
  
  const resumen = `REPORTE DE ESTUDIANTES Y RESPUESTAS
` +
    `Fecha de generación: ${new Date().toLocaleString('es-ES')}
` +
    `==========================================

` +
    `RESUMEN GENERAL:
` +
    `- Total de estudiantes: ${totalEstudiantes}
` +
    `- Total de respuestas: ${totalRespuestas}
` +
    `- Estudiantes con respuestas: ${estudiantesConRespuestas}
` +
    `- Estudiantes sin respuestas: ${estudiantesSinRespuestas}
` +
    `- Promedio de respuestas por estudiante: ${(totalRespuestas / totalEstudiantes).toFixed(2)}

` +
    `DISTRIBUCIÓN POR GÉNERO:
` +
    Object.entries(porGenero).map(([genero, cantidad]) => `- ${genero}: ${cantidad}`).join('\n') + '\n\n' +
    `DISTRIBUCIÓN POR GRADO:
` +
    Object.entries(porGrado).map(([grado, cantidad]) => `- ${grado}: ${cantidad}`).join('\n') + '\n\n' +
    `ESTUDIANTES SIN RESPUESTAS:
` +
    datos.filter(item => item.total_respuestas === 0)
      .map(item => `- ${item.estudiante.nombre} ${item.estudiante.apellido} (ID: ${item.estudiante.id})`)
      .join('\n');
  
  fs.writeFileSync(rutaArchivo, resumen, 'utf8');
  console.log(`📈 Resumen estadísticas generado: ${rutaArchivo}`);
  
  return rutaArchivo;
}

async function main() {
  try {
    console.log('🚀 Iniciando generación de reporte de estudiantes y respuestas...');
    
    // Obtener datos
    const datos = await obtenerEstudiantesConRespuestas();
    
    console.log('\n📋 Generando archivos de reporte...');
    
    // Generar reportes en diferentes formatos
    const archivoJSON = await generarReporteJSON(datos);
    const archivoCSV = await generarReporteCSV(datos);
    const archivoResumen = await generarResumenEstadisticas(datos);
    
    console.log('\n✅ Proceso completado exitosamente!');
    console.log('\n📁 Archivos generados:');
    console.log(`   - JSON: ${path.basename(archivoJSON)}`);
    console.log(`   - CSV: ${path.basename(archivoCSV)}`);
    console.log(`   - Resumen: ${path.basename(archivoResumen)}`);
    
  } catch (error) {
    console.error('❌ Error en el proceso:', error.message);
    process.exit(1);
  }
}

// Ejecutar si es llamado directamente
if (import.meta.url === `file://${process.argv[1]}` || process.argv[1].endsWith(path.basename(__filename))) {
  main();
}

// También ejecutar directamente como fallback para Windows
main();

export { obtenerEstudiantesConRespuestas, generarReporteJSON, generarReporteCSV, generarResumenEstadisticas };