import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '../../ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../ui/table';
import { Badge } from '../../ui/badge';

interface TableWidgetProps {
  title: string;
  data: any[];
  columns: {
    key: string;
    label: string;
    type?: 'text' | 'number' | 'date' | 'badge';
    format?: (value: any) => string;
  }[];
  loading?: boolean;
  maxHeight?: number;
}

export const TableWidget: React.FC<TableWidgetProps> = ({
  title,
  data,
  columns,
  loading = false,
  maxHeight = 400
}) => {
  const formatValue = (value: any, type: string = 'text', format?: (value: any) => string) => {
    if (format) return format(value);
    
    switch (type) {
      case 'date':
        return new Date(value).toLocaleDateString('es-ES');
      case 'number':
        return typeof value === 'number' ? value.toLocaleString() : value;
      case 'badge':
        return (
          <Badge variant={value > 80 ? 'success' : value > 60 ? 'warning' : 'destructive'}>
            {value}
          </Badge>
        );
      default:
        return value?.toString() || '-';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">
            <div className="h-6 bg-gray-200 rounded w-32 animate-pulse"></div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-8 bg-gray-100 rounded animate-pulse"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div style={{ maxHeight: `${maxHeight}px`, overflowY: 'auto' }}>
          <Table>
            <TableHeader>
              <TableRow>
                {columns.map((column) => (
                  <TableHead key={column.key}>{column.label}</TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={columns.length} className="text-center text-gray-500">
                    No hay datos disponibles
                  </TableCell>
                </TableRow>
              ) : (
                data.map((row, index) => (
                  <TableRow key={index}>
                    {columns.map((column) => (
                      <TableCell key={column.key}>
                        {formatValue(row[column.key], column.type, column.format)}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};