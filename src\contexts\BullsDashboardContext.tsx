import React, { createContext, useContext } from 'react';
import { useDashboardData } from '../hooks/dashboard/useDashboardData';
import { useDashboardFilters } from '../hooks/dashboard/useDashboardFilters';
import { useBullsMetrics } from '../hooks/dashboard/useBullsMetrics';

// Define la forma del contexto
type BullsDashboardContextType = {
  filters: ReturnType<typeof useDashboardFilters>['filters'];
  updateFilter: ReturnType<typeof useDashboardFilters>['updateFilter'];
  setDateRange: ReturnType<typeof useDashboardFilters>['setDateRange'];
  clearFilters: ReturnType<typeof useDashboardFilters>['clearFilters'];
  data: ReturnType<typeof useDashboardData>['data'];
  loading: ReturnType<typeof useDashboardData>['loading'];
  error: ReturnType<typeof useDashboardData>['error'];
  lastUpdated: ReturnType<typeof useDashboardData>['lastUpdated'];
  refreshData: ReturnType<typeof useDashboardData>['refreshData'];
  kpis: ReturnType<typeof useBullsMetrics>['kpis'];
  sociogramData: ReturnType<typeof useBullsMetrics>['sociogramData'];
  profilesData: ReturnType<typeof useBullsMetrics>['profilesData'];
  contextData: ReturnType<typeof useBullsMetrics>['contextData'];
  comparisonsData: ReturnType<typeof useBullsMetrics>['comparisonsData'];
  courseOverviewData: ReturnType<typeof useBullsMetrics>['courseOverviewData'];
  recommendedActionsData: ReturnType<typeof useBullsMetrics>['recommendedActionsData'];
};

// Crear el contexto
export const BullsDashboardContext = createContext<BullsDashboardContextType | undefined>(undefined);

// Hook personalizado para usar el contexto fácilmente
export const useBullsDashboard = () => {
  const context = useContext(BullsDashboardContext);
  if (!context) {
    throw new Error('useBullsDashboard must be used within a BullsDashboardProvider');
  }
  return context;
};

// Provider component
interface BullsDashboardProviderProps {
  children: React.ReactNode;
}

export const BullsDashboardProvider: React.FC<BullsDashboardProviderProps> = ({ children }) => {
  const { filters, updateFilter, setDateRange, clearFilters } = useDashboardFilters();
  const { data, loading, error, lastUpdated, refreshData } = useDashboardData(filters);
  const { kpis, sociogramData, profilesData, contextData, comparisonsData, courseOverviewData, recommendedActionsData } = useBullsMetrics(data, filters);

  const contextValue = {
    filters, updateFilter, setDateRange, clearFilters,
    data, loading, error, lastUpdated, refreshData,
    kpis, sociogramData, profilesData, contextData, comparisonsData,
    courseOverviewData, recommendedActionsData
  };

  return (
    <BullsDashboardContext.Provider value={contextValue}>
      {children}
    </BullsDashboardContext.Provider>
  );
};