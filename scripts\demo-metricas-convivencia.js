/**
 * Script de demostración para las métricas de convivencia escolar
 * Genera datos de muestra para mostrar cómo funcionan las métricas
 */

import { createClient } from '@supabase/supabase-js';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { readFileSync } from 'fs';

// Obtener el directorio actual
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Cargar variables de entorno desde el archivo .env
try {
  const envPath = join(__dirname, '..', '.env');
  const envContent = readFileSync(envPath, 'utf8');
  const envVars = {};
  
  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key && value) {
      envVars[key.trim()] = value.trim();
    }
  });
  
  // Asignar variables de entorno
  Object.assign(process.env, envVars);
} catch (error) {
  console.log('⚠️ No se pudo cargar el archivo .env, usando variables del sistema');
}

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

// Configurar fetch global para Node.js
if (typeof globalThis.fetch === 'undefined') {
  const { default: fetch } = await import('node-fetch');
  globalThis.fetch = fetch;
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Servicio de demostración de convivencia escolar con datos simulados
 */
class ConvivenciaEscolarDemo {
  
  // Generar datos de muestra para demostración
  generarDatosMuestra(numEstudiantes = 25) {
    const roles = ['Bully', 'Victim', 'Bully-Victim', 'Observer'];
    const estatusSociometrico = ['Popular', 'Average', 'Isolated', 'Rejected', 'Controversial'];
    
    const estudiantes = [];
    
    for (let i = 1; i <= numEstudiantes; i++) {
      // Distribución realista de roles
      let role;
      const rand = Math.random();
      if (rand < 0.08) role = 'Bully';           // 8% agresores
      else if (rand < 0.20) role = 'Victim';     // 12% víctimas
      else if (rand < 0.25) role = 'Bully-Victim'; // 5% agresores-víctimas
      else role = 'Observer';                    // 75% observadores
      
      // Distribución de estatus sociométrico
      let status;
      const statusRand = Math.random();
      if (statusRand < 0.15) status = 'Popular';      // 15%
      else if (statusRand < 0.65) status = 'Average'; // 50%
      else if (statusRand < 0.80) status = 'Isolated'; // 15%
      else if (statusRand < 0.95) status = 'Rejected'; // 15%
      else status = 'Controversial';                  // 5%
      
      // Nominaciones basadas en el estatus
      let positiveNominations, negativeNominations;
      switch (status) {
        case 'Popular':
          positiveNominations = Math.floor(Math.random() * 8) + 5; // 5-12
          negativeNominations = Math.floor(Math.random() * 2);     // 0-1
          break;
        case 'Rejected':
          positiveNominations = Math.floor(Math.random() * 2);     // 0-1
          negativeNominations = Math.floor(Math.random() * 8) + 5; // 5-12
          break;
        case 'Isolated':
          positiveNominations = Math.floor(Math.random() * 2);     // 0-1
          negativeNominations = Math.floor(Math.random() * 2);     // 0-1
          break;
        case 'Controversial':
          positiveNominations = Math.floor(Math.random() * 6) + 3; // 3-8
          negativeNominations = Math.floor(Math.random() * 6) + 3; // 3-8
          break;
        default: // Average
          positiveNominations = Math.floor(Math.random() * 4) + 2; // 2-5
          negativeNominations = Math.floor(Math.random() * 3) + 1; // 1-3
      }
      
      estudiantes.push({
        id: `estudiante_${i}`,
        nombre_estudiante: `Estudiante${i}`,
        apellido_estudiante: `Apellido${i}`,
        bullying_role: role,
        sociometric_status: status,
        positive_nominations: positiveNominations,
        negative_nominations: negativeNominations,
        grupo_id: 'demo_grupo_6b',
        institucion_id: 'demo_institucion'
      });
    }
    
    return estudiantes;
  }
  
  async obtenerMetricasConvivencia(institucionId, grupoId, usarDatosDemo = true) {
    try {
      let indicadores;
      
      if (usarDatosDemo) {
        console.log('🎭 Generando datos de demostración...');
        indicadores = this.generarDatosMuestra(25);
        console.log(`✅ Datos generados: ${indicadores.length} estudiantes`);
      } else {
        console.log('🔍 Obteniendo datos reales de la vista de indicadores de bullying...');
        
        let query = supabase
          .from('view_dashboard_bullying_indicators')
          .select('*');
        
        if (grupoId) {
          query = query.eq('grupo_id', grupoId);
        } else if (institucionId) {
          query = query.eq('institucion_id', institucionId);
        }
        
        const { data, error } = await query;
        
        if (error || !data || data.length === 0) {
          console.log('⚠️ No hay datos reales, usando datos de demostración');
          indicadores = this.generarDatosMuestra(25);
        } else {
          indicadores = data;
        }
      }
      
      // Mostrar muestra de datos
      console.log('\n📋 Muestra de datos:');
      indicadores.slice(0, 5).forEach((ind, index) => {
        console.log(`${index + 1}. ${ind.nombre_estudiante} ${ind.apellido_estudiante}:`);
        console.log(`   - Rol de bullying: ${ind.bullying_role}`);
        console.log(`   - Estatus sociométrico: ${ind.sociometric_status}`);
        console.log(`   - Nominaciones positivas: ${ind.positive_nominations}`);
        console.log(`   - Nominaciones negativas: ${ind.negative_nominations}`);
      });
      
      // Calcular métricas
      const tasasIncidencia = this.calcularTasasIncidencia(indicadores);
      const estatusSociometrico = this.calcularEstatusSociometrico(indicadores);
      const cohesionGrupal = this.calcularCohesionGrupal(indicadores);
      const dinamicasAcoso = this.calcularDinamicasAcoso(indicadores);
      
      return {
        tasasIncidencia,
        estatusSociometrico,
        cohesionGrupal,
        dinamicasAcoso,
        fechaCalculo: new Date().toISOString(),
        grupoId: grupoId || 'demo_grupo',
        institucionId: institucionId || 'demo_institucion',
        tipoAnalisis: usarDatosDemo ? 'Demostración' : 'Datos Reales'
      };
      
    } catch (error) {
      console.error('❌ Error calculando métricas de convivencia:', error);
      return this.obtenerMetricasPorDefecto();
    }
  }
  
  calcularTasasIncidencia(indicadores) {
    const totalEstudiantes = indicadores.length;
    
    const conteos = indicadores.reduce((acc, ind) => {
      const role = ind.bullying_role || 'Observer';
      acc[role] = (acc[role] || 0) + 1;
      return acc;
    }, {});
    
    const agresores = conteos['Bully'] || 0;
    const victimas = conteos['Victim'] || 0;
    const agresoresVictimas = conteos['Bully-Victim'] || 0;
    const observadores = conteos['Observer'] || 0;
    
    console.log('\n📊 Tasas de Incidencia:');
    console.log(`   Total estudiantes: ${totalEstudiantes}`);
    console.log(`   Agresores: ${agresores} (${totalEstudiantes > 0 ? ((agresores / totalEstudiantes) * 100).toFixed(1) : 0}%)`);
    console.log(`   Víctimas: ${victimas} (${totalEstudiantes > 0 ? ((victimas / totalEstudiantes) * 100).toFixed(1) : 0}%)`);
    console.log(`   Agresores-Víctimas: ${agresoresVictimas} (${totalEstudiantes > 0 ? ((agresoresVictimas / totalEstudiantes) * 100).toFixed(1) : 0}%)`);
    console.log(`   Observadores: ${observadores} (${totalEstudiantes > 0 ? ((observadores / totalEstudiantes) * 100).toFixed(1) : 0}%)`);
    
    return {
      agresores,
      victimas,
      agresoresVictimas,
      observadores,
      totalEstudiantes,
      porcentajes: {
        agresores: totalEstudiantes > 0 ? (agresores / totalEstudiantes) * 100 : 0,
        victimas: totalEstudiantes > 0 ? (victimas / totalEstudiantes) * 100 : 0,
        agresoresVictimas: totalEstudiantes > 0 ? (agresoresVictimas / totalEstudiantes) * 100 : 0,
        observadores: totalEstudiantes > 0 ? (observadores / totalEstudiantes) * 100 : 0
      }
    };
  }
  
  calcularEstatusSociometrico(indicadores) {
    const totalEstudiantes = indicadores.length;
    
    const conteos = indicadores.reduce((acc, ind) => {
      const status = ind.sociometric_status || 'Average';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});
    
    const populares = conteos['Popular'] || 0;
    const promedio = conteos['Average'] || 0;
    const aislados = conteos['Isolated'] || 0;
    const rechazados = conteos['Rejected'] || 0;
    const controvertidos = conteos['Controversial'] || 0;
    
    console.log('\n👥 Estatus Sociométrico:');
    console.log(`   Populares: ${populares} (${totalEstudiantes > 0 ? ((populares / totalEstudiantes) * 100).toFixed(1) : 0}%)`);
    console.log(`   Promedio: ${promedio} (${totalEstudiantes > 0 ? ((promedio / totalEstudiantes) * 100).toFixed(1) : 0}%)`);
    console.log(`   Aislados: ${aislados} (${totalEstudiantes > 0 ? ((aislados / totalEstudiantes) * 100).toFixed(1) : 0}%)`);
    console.log(`   Rechazados: ${rechazados} (${totalEstudiantes > 0 ? ((rechazados / totalEstudiantes) * 100).toFixed(1) : 0}%)`);
    console.log(`   Controvertidos: ${controvertidos} (${totalEstudiantes > 0 ? ((controvertidos / totalEstudiantes) * 100).toFixed(1) : 0}%)`);
    
    return {
      populares,
      promedio,
      aislados,
      rechazados,
      controvertidos,
      totalEstudiantes,
      porcentajes: {
        populares: totalEstudiantes > 0 ? (populares / totalEstudiantes) * 100 : 0,
        promedio: totalEstudiantes > 0 ? (promedio / totalEstudiantes) * 100 : 0,
        aislados: totalEstudiantes > 0 ? (aislados / totalEstudiantes) * 100 : 0,
        rechazados: totalEstudiantes > 0 ? (rechazados / totalEstudiantes) * 100 : 0,
        controvertidos: totalEstudiantes > 0 ? (controvertidos / totalEstudiantes) * 100 : 0
      }
    };
  }
  
  calcularCohesionGrupal(indicadores) {
    const nominacionesPositivas = indicadores.map(ind => ind.positive_nominations || 0);
    const promedioNominaciones = nominacionesPositivas.length > 0 
      ? nominacionesPositivas.reduce((sum, val) => sum + val, 0) / nominacionesPositivas.length 
      : 0;
    
    const varianza = nominacionesPositivas.length > 0
      ? nominacionesPositivas.reduce((sum, val) => sum + Math.pow(val - promedioNominaciones, 2), 0) / nominacionesPositivas.length
      : 0;
    const desviacion = Math.sqrt(varianza);
    
    let categoria, valor;
    
    if (promedioNominaciones > 4 && desviacion < 2) {
      categoria = 'Alta';
      valor = 85;
    } else if (promedioNominaciones > 2 && desviacion < 3) {
      categoria = 'Media';
      valor = 65;
    } else {
      categoria = 'Baja';
      valor = 35;
    }
    
    const totalPosiblesRelaciones = indicadores.length * (indicadores.length - 1);
    const relacionesActuales = nominacionesPositivas.reduce((sum, val) => sum + val, 0);
    const densidadRelaciones = totalPosiblesRelaciones > 0 
      ? (relacionesActuales / totalPosiblesRelaciones) * 100 
      : 0;
    
    const reciprocidad = Math.min(densidadRelaciones * 1.2, 100);
    
    console.log('\n❤️ Cohesión Grupal:');
    console.log(`   Promedio nominaciones positivas: ${promedioNominaciones.toFixed(2)}`);
    console.log(`   Desviación estándar: ${desviacion.toFixed(2)}`);
    console.log(`   Categoría: ${categoria}`);
    console.log(`   Valor: ${valor}%`);
    console.log(`   Densidad de relaciones: ${densidadRelaciones.toFixed(1)}%`);
    console.log(`   Reciprocidad estimada: ${reciprocidad.toFixed(1)}%`);
    
    return {
      valor,
      categoria,
      promedioNominacionesPositivas: promedioNominaciones,
      desviacionEstandar: desviacion,
      densidadRelaciones,
      reciprocidad
    };
  }
  
  calcularDinamicasAcoso(indicadores) {
    const conteoRoles = indicadores.reduce((acc, ind) => {
      const role = ind.bullying_role || 'Observer';
      acc[role] = (acc[role] || 0) + 1;
      return acc;
    }, {});
    
    const agresores = conteoRoles['Bully'] || 0;
    const victimas = conteoRoles['Victim'] || 0;
    const agresoresVictimas = conteoRoles['Bully-Victim'] || 0;
    
    let predominante;
    
    if (agresoresVictimas > (agresores + victimas) / 2) {
      predominante = 'Ciclos de Violencia';
    } else if (agresores > victimas) {
      predominante = 'Agresión Dominante';
    } else if (victimas > agresores) {
      predominante = 'Victimización Prevalente';
    } else {
      predominante = 'Ambiente Observacional';
    }
    
    const totalIncidentes = agresores + victimas + agresoresVictimas;
    const patrones = {
      agresionFisica: Math.round(totalIncidentes * 0.3),
      agresionVerbal: Math.round(totalIncidentes * 0.4),
      exclusionSocial: Math.round(totalIncidentes * 0.2),
      ciberacoso: Math.round(totalIncidentes * 0.1)
    };
    
    console.log('\n🎯 Dinámicas de Acoso:');
    console.log(`   Dinámica predominante: ${predominante}`);
    console.log(`   Total incidentes: ${totalIncidentes}`);
    console.log(`   Patrones:`);
    console.log(`     - Agresión física: ${patrones.agresionFisica}`);
    console.log(`     - Agresión verbal: ${patrones.agresionVerbal}`);
    console.log(`     - Exclusión social: ${patrones.exclusionSocial}`);
    console.log(`     - Ciberacoso: ${patrones.ciberacoso}`);
    
    return {
      predominante,
      patrones,
      lugaresComunes: {
        'Aula': 35,
        'Patio': 25,
        'Pasillos': 20,
        'Baños': 10,
        'Comedor': 10
      },
      frecuenciaPercibida: {
        'Diariamente': 15,
        'Varias veces por semana': 25,
        'Una vez por semana': 30,
        'Ocasionalmente': 20,
        'Nunca': 10
      }
    };
  }
  
  obtenerMetricasPorDefecto() {
    return {
      tasasIncidencia: {
        agresores: 0, victimas: 0, agresoresVictimas: 0, observadores: 0,
        totalEstudiantes: 0,
        porcentajes: { agresores: 0, victimas: 0, agresoresVictimas: 0, observadores: 0 }
      },
      estatusSociometrico: {
        populares: 0, promedio: 0, aislados: 0, rechazados: 0, controvertidos: 0,
        totalEstudiantes: 0,
        porcentajes: { populares: 0, promedio: 0, aislados: 0, rechazados: 0, controvertidos: 0 }
      },
      cohesionGrupal: {
        valor: 0, categoria: 'Baja', promedioNominacionesPositivas: 0,
        densidadRelaciones: 0, reciprocidad: 0
      },
      dinamicasAcoso: {
        predominante: 'Ambiente Observacional',
        patrones: { agresionFisica: 0, agresionVerbal: 0, exclusionSocial: 0, ciberacoso: 0 },
        lugaresComunes: {}, frecuenciaPercibida: {}
      },
      fechaCalculo: new Date().toISOString()
    };
  }
  
  async obtenerResumenEjecutivo(institucionId, grupoId, usarDatosDemo = true) {
    const metricas = await this.obtenerMetricasConvivencia(institucionId, grupoId, usarDatosDemo);
    
    const porcentajeAgresion = metricas.tasasIncidencia.porcentajes.agresores + 
                              metricas.tasasIncidencia.porcentajes.agresoresVictimas;
    const porcentajeVictimizacion = metricas.tasasIncidencia.porcentajes.victimas + 
                                   metricas.tasasIncidencia.porcentajes.agresoresVictimas;
    const porcentajeAislamiento = metricas.estatusSociometrico.porcentajes.aislados + 
                                 metricas.estatusSociometrico.porcentajes.rechazados;
    
    let nivelRiesgo, colorRiesgo;
    
    if (porcentajeAgresion > 20 || porcentajeVictimizacion > 25) {
      nivelRiesgo = 'Crítico';
      colorRiesgo = '🔴';
    } else if (porcentajeAgresion > 10 || porcentajeVictimizacion > 15 || porcentajeAislamiento > 30) {
      nivelRiesgo = 'Alto';
      colorRiesgo = '🟠';
    } else if (porcentajeAgresion > 5 || porcentajeVictimizacion > 8 || porcentajeAislamiento > 20) {
      nivelRiesgo = 'Medio';
      colorRiesgo = '🟡';
    } else {
      nivelRiesgo = 'Bajo';
      colorRiesgo = '🟢';
    }
    
    console.log('\n🚨 Resumen Ejecutivo:');
    console.log(`   ${colorRiesgo} Nivel de riesgo: ${nivelRiesgo}`);
    console.log(`   📊 Porcentaje agresión: ${porcentajeAgresion.toFixed(1)}%`);
    console.log(`   🎯 Porcentaje victimización: ${porcentajeVictimizacion.toFixed(1)}%`);
    console.log(`   👥 Porcentaje aislamiento: ${porcentajeAislamiento.toFixed(1)}%`);
    console.log(`   ❤️ Cohesión grupal: ${metricas.cohesionGrupal.categoria} (${metricas.cohesionGrupal.valor}%)`);
    
    // Generar recomendaciones basadas en los resultados
    const recomendaciones = [];
    
    if (porcentajeAgresion > 10) {
      recomendaciones.push('Implementar programa de prevención de bullying');
    }
    if (porcentajeVictimizacion > 15) {
      recomendaciones.push('Establecer protocolo de apoyo a víctimas');
    }
    if (porcentajeAislamiento > 25) {
      recomendaciones.push('Desarrollar actividades de integración social');
    }
    if (metricas.cohesionGrupal.categoria === 'Baja') {
      recomendaciones.push('Fortalecer dinámicas de grupo y trabajo colaborativo');
    }
    
    if (recomendaciones.length > 0) {
      console.log('\n💡 Recomendaciones:');
      recomendaciones.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec}`);
      });
    }
    
    return {
      nivelRiesgo,
      colorRiesgo,
      indicadoresClave: [
        `Cohesión grupal: ${metricas.cohesionGrupal.categoria} (${metricas.cohesionGrupal.valor}%)`,
        `Dinámica predominante: ${metricas.dinamicasAcoso.predominante}`,
        `Estudiantes en riesgo: ${(porcentajeAgresion + porcentajeVictimizacion).toFixed(1)}%`,
        `Aislamiento social: ${porcentajeAislamiento.toFixed(1)}%`
      ],
      recomendaciones,
      alertas: [],
      metricas
    };
  }
}

async function main() {
  console.log('🎭 Demostración de Métricas de Convivencia Escolar\n');
  
  const demo = new ConvivenciaEscolarDemo();
  
  try {
    console.log('🔗 Verificando conexión a Supabase...');
    const { data, error } = await supabase.from('estudiantes').select('count').limit(1);
    
    if (error) {
      console.log('⚠️ Sin conexión a Supabase, usando solo datos de demostración');
    } else {
      console.log('✅ Conexión a Supabase exitosa');
    }
    
    console.log('\n🎯 Ejecutando análisis de convivencia escolar...');
    
    // Ejecutar análisis con datos de demostración
    const resumen = await demo.obtenerResumenEjecutivo('demo_institucion', 'demo_grupo_6b', true);
    
    console.log('\n✅ Análisis completado exitosamente!');
    console.log('\n📈 Resumen Final:');
    console.log(`   🏫 Institución: Demo Institución`);
    console.log(`   📚 Grupo: 6B (Demostración)`);
    console.log(`   📅 Fecha: ${new Date().toLocaleString('es-ES')}`);
    console.log(`   🎭 Tipo: ${resumen.metricas.tipoAnalisis}`);
    console.log(`   👥 Total estudiantes: ${resumen.metricas.tasasIncidencia.totalEstudiantes}`);
    console.log(`   ${resumen.colorRiesgo} Nivel de riesgo: ${resumen.nivelRiesgo}`);
    
    console.log('\n🎉 La demostración muestra cómo funcionarían las métricas con datos reales.');
    console.log('📝 Para usar con datos reales, asegúrate de que la vista view_dashboard_bullying_indicators contenga datos.');
    
  } catch (error) {
    console.error('❌ Error durante la demostración:', error);
  }
}

// Ejecutar la demostración
main().catch(console.error);