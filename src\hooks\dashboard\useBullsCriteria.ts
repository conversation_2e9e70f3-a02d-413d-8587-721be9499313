import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../../lib/supabase';

export interface EstudianteRiesgoBulls {
  grupo_id: string;
  curso: string;
  student_id: string;
  student_name: string;
  total_estudiantes: number;
  nominaciones_agresor: number;
  nominaciones_victima: number;
  porcentaje_agresor: number;
  porcentaje_victima: number;
  rol_riesgo: 'Agresor' | 'Víctima' | 'Agresor-Víctima' | 'Sin Riesgo';
  es_agresor: boolean;
  es_victima: boolean;
  en_riesgo: boolean;
  fecha_calculo: string;
}

export interface ResumenCursoBulls {
  curso: string;
  grupo_id: string;
  total_estudiantes: number;
  total_agresores: number;
  total_victimas: number;
  total_agresor_victima: number;
  total_sin_riesgo: number;
  total_en_riesgo: number;
  porcentaje_riesgo: number;
  nivel_alerta: 'CRÍTICO' | 'NORMAL';
  fecha_calculo: string;
}

export interface BullsCriteriaStats {
  totalEstudiantes: number;
  totalEnRiesgo: number;
  porcentajeRiesgo: number;
  totalAgresores: number;
  totalVictimas: number;
  totalAgresorVictima: number;
  gruposCriticos: number;
}

export interface UseBullsCriteriaReturn {
  estudiantes: EstudianteRiesgoBulls[];
  resumen: ResumenCursoBulls[];
  stats: BullsCriteriaStats;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  getEstudiantesPorGrupo: (grupoId: string) => EstudianteRiesgoBulls[];
  getResumenPorGrupo: (grupoId: string) => ResumenCursoBulls | null;
  getEstudiantesPorRol: (rol: string) => EstudianteRiesgoBulls[];
}

/**
 * Hook personalizado para manejar el criterio oficial BULL-S
 * 
 * @param grupoId - ID del grupo a filtrar (opcional)
 * @param autoRefresh - Si debe refrescar automáticamente los datos
 * @param refreshInterval - Intervalo de refresco en milisegundos
 */
export const useBullsCriteria = (
  grupoId?: string,
  autoRefresh: boolean = false,
  refreshInterval: number = 30000
): UseBullsCriteriaReturn => {
  const [estudiantes, setEstudiantes] = useState<EstudianteRiesgoBulls[]>([]);
  const [resumen, setResumen] = useState<ResumenCursoBulls[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Función para cargar los datos
  const fetchData = useCallback(async () => {
    try {
      setError(null);

      // Cargar estudiantes en riesgo
      let estudiantesQuery = supabase
        .from('vw_estudiantes_en_riesgo_bulls')
        .select('*')
        .order('curso')
        .order('rol_riesgo')
        .order('porcentaje_agresor', { ascending: false })
        .order('porcentaje_victima', { ascending: false });

      if (grupoId) {
        estudiantesQuery = estudiantesQuery.eq('grupo_id', grupoId);
      }

      const { data: estudiantesData, error: estudiantesError } = await estudiantesQuery;
      if (estudiantesError) throw estudiantesError;

      // Cargar resumen por curso
      let resumenQuery = supabase
        .from('vw_resumen_bulls_por_curso')
        .select('*')
        .order('porcentaje_riesgo', { ascending: false });

      if (grupoId) {
        resumenQuery = resumenQuery.eq('grupo_id', grupoId);
      }

      const { data: resumenData, error: resumenError } = await resumenQuery;
      if (resumenError) throw resumenError;

      setEstudiantes(estudiantesData || []);
      setResumen(resumenData || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError(errorMessage);
      console.error('Error en useBullsCriteria:', err);
    }
  }, [grupoId]);

  // Función pública para refrescar datos
  const refetch = useCallback(async () => {
    setLoading(true);
    await fetchData();
    setLoading(false);
  }, [fetchData]);

  // Efecto para cargar datos iniciales
  useEffect(() => {
    refetch();
  }, [refetch]);

  // Efecto para auto-refresh
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      fetchData();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchData]);

  // Calcular estadísticas generales
  const stats: BullsCriteriaStats = {
    totalEstudiantes: resumen.reduce((sum, r) => sum + r.total_estudiantes, 0),
    totalEnRiesgo: resumen.reduce((sum, r) => sum + r.total_en_riesgo, 0),
    porcentajeRiesgo: resumen.length > 0 
      ? Math.round((resumen.reduce((sum, r) => sum + r.total_en_riesgo, 0) / 
          resumen.reduce((sum, r) => sum + r.total_estudiantes, 0)) * 100 * 10) / 10
      : 0,
    totalAgresores: resumen.reduce((sum, r) => sum + r.total_agresores, 0),
    totalVictimas: resumen.reduce((sum, r) => sum + r.total_victimas, 0),
    totalAgresorVictima: resumen.reduce((sum, r) => sum + r.total_agresor_victima, 0),
    gruposCriticos: resumen.filter(r => r.nivel_alerta === 'CRÍTICO').length
  };

  // Funciones de utilidad
  const getEstudiantesPorGrupo = useCallback((targetGrupoId: string): EstudianteRiesgoBulls[] => {
    return estudiantes.filter(e => e.grupo_id === targetGrupoId);
  }, [estudiantes]);

  const getResumenPorGrupo = useCallback((targetGrupoId: string): ResumenCursoBulls | null => {
    return resumen.find(r => r.grupo_id === targetGrupoId) || null;
  }, [resumen]);

  const getEstudiantesPorRol = useCallback((rol: string): EstudianteRiesgoBulls[] => {
    return estudiantes.filter(e => e.rol_riesgo === rol);
  }, [estudiantes]);

  return {
    estudiantes,
    resumen,
    stats,
    loading,
    error,
    refetch,
    getEstudiantesPorGrupo,
    getResumenPorGrupo,
    getEstudiantesPorRol
  };
};

/**
 * Hook simplificado para obtener solo las estadísticas del criterio BULL-S
 */
export const useBullsCriteriaStats = (grupoId?: string) => {
  const { stats, loading, error, refetch } = useBullsCriteria(grupoId);
  
  return {
    stats,
    loading,
    error,
    refetch
  };
};

/**
 * Hook para obtener estudiantes en riesgo crítico (≥25% nominaciones)
 */
export const useEstudiantesEnRiesgo = (grupoId?: string) => {
  const { estudiantes, loading, error, refetch } = useBullsCriteria(grupoId);
  
  const estudiantesEnRiesgo = estudiantes.filter(e => e.en_riesgo);
  
  return {
    estudiantes: estudiantesEnRiesgo,
    loading,
    error,
    refetch
  };
};

/**
 * Hook para obtener alertas críticas por grupo
 */
export const useAlertasCriticas = () => {
  const { resumen, loading, error, refetch } = useBullsCriteria();
  
  const alertasCriticas = resumen.filter(r => r.nivel_alerta === 'CRÍTICO');
  
  return {
    alertas: alertasCriticas,
    tieneAlertas: alertasCriticas.length > 0,
    loading,
    error,
    refetch
  };
};

// Constantes del criterio BULL-S
export const BULLS_CRITERIA = {
  THRESHOLD: 25, // Porcentaje mínimo para estar en riesgo
  AGRESOR_QUESTIONS: {
    Q5: '0489df06-c6e7-48ec-8fb0-49469ec541ae', // "¿Quiénes son los/as más fuertes?"
    Q7: '8e0be6b5-fa0b-4215-bc60-0c91065bbaa9', // "¿Quiénes maltratan o pegan?"
    Q9: '8074fef6-4952-4857-b97c-08a1a8805522'  // "¿Quiénes empezar las peleas?"
  },
  VICTIMA_QUESTIONS: {
    Q6: '775af389-a84d-4f40-8fb9-7b94cbea5498', // "¿Quiénes actúan como cobardes?"
    Q8: 'eec6513e-f5b7-45b1-b21d-4e4551b3e504', // "¿Quiénes suelen ser víctimas?"
    Q10: '00000000-0000-0000-0000-000000000010' // "¿A quiénes se les tiene manía?"
  },
  ROLES: {
    AGRESOR: 'Agresor',
    VICTIMA: 'Víctima',
    AGRESOR_VICTIMA: 'Agresor-Víctima',
    SIN_RIESGO: 'Sin Riesgo'
  } as const
};

export default useBullsCriteria;