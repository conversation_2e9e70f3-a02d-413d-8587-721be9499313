import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';

const SupabaseTestPage: React.FC = () => {
  const [testResults, setTestResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testSupabaseConnection = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('Testing Supabase connection...');
      console.log('VITE_SUPABASE_URL:', import.meta.env.VITE_SUPABASE_URL);
      console.log('VITE_SUPABASE_ANON_KEY exists:', !!import.meta.env.VITE_SUPABASE_ANON_KEY);
      
      // Test 1: Simple count query
      const { data: countData, error: countError } = await supabase
        .from('respuestas')
        .select('*', { count: 'exact', head: true });
      
      if (countError) {
        throw new Error(`Count query failed: ${countError.message}`);
      }
      
      // Test 2: Simple select query
      const { data: selectData, error: selectError } = await supabase
        .from('respuestas')
        .select('id, estudiante_id')
        .limit(5);
      
      if (selectError) {
        throw new Error(`Select query failed: ${selectError.message}`);
      }
      
      // Test 3: Join query
      const { data: joinData, error: joinError } = await supabase
        .from('respuestas')
        .select(`
          id,
          estudiantes!inner(
            nombre_estudiante,
            apellido_estudiante
          )
        `)
        .limit(3);
      
      if (joinError) {
        throw new Error(`Join query failed: ${joinError.message}`);
      }
      
      setTestResults({
        countResult: countData,
        selectResult: selectData,
        joinResult: joinData,
        envVars: {
          url: import.meta.env.VITE_SUPABASE_URL,
          hasKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY
        }
      });
      
    } catch (err) {
      console.error('Supabase test error:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    testSupabaseConnection();
  }, []);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Supabase Connection Test</h1>
      
      <div className="mb-4">
        <button 
          onClick={testSupabaseConnection}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test Connection'}
        </button>
      </div>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong>Error:</strong> {error}
        </div>
      )}
      
      {testResults && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          <h2 className="font-bold mb-2">Test Results:</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(testResults, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default SupabaseTestPage;