/**
 * Script para generar SQL completo que se puede copiar y pegar en Supabase Dashboard
 * Combina todas las migraciones en un solo archivo ejecutable
 */

import fs from 'fs';
import path from 'path';

function generarSQLCompleto() {
  console.log('📝 GENERANDO SQL COMPLETO PARA SUPABASE DASHBOARD');
  console.log('============================================================');

  try {
    // Lista de archivos de migración en orden
    const migraciones = [
      'supabase/migrations/20241220000002_create_sociometric_views.sql',
      'supabase/migrations/20241225000001_create_bulls_dashboard_views.sql', 
      'supabase/migrations/20241225000002_create_bulls_metrics_functions.sql'
    ];

    let sqlCompleto = `-- ============================================================
-- SQL COMPLETO PARA APLICAR EN SUPABASE DASHBOARD
-- Generado automáticamente - Copiar y pegar en SQL Editor
-- ============================================================

`;

    for (const archivo of migraciones) {
      console.log(`📄 Procesando: ${path.basename(archivo)}`);
      
      try {
        const contenido = fs.readFileSync(archivo, 'utf8');
        
        sqlCompleto += `-- ============================================================
`;
        sqlCompleto += `-- ${path.basename(archivo).toUpperCase()}
`;
        sqlCompleto += `-- ============================================================

`;
        sqlCompleto += contenido;
        sqlCompleto += `

`;
        
        console.log(`✅ ${path.basename(archivo)} agregado`);
        
      } catch (error) {
        console.log(`❌ Error leyendo ${archivo}: ${error.message}`);
      }
    }

    // Agregar verificaciones al final
    sqlCompleto += `-- ============================================================
`;
    sqlCompleto += `-- VERIFICACIONES FINALES
`;
    sqlCompleto += `-- ============================================================

`;
    
    sqlCompleto += `-- Verificar vistas creadas
`;
    sqlCompleto += `SELECT 'vw_roles' as vista, count(*) as registros FROM vw_roles
`;
    sqlCompleto += `UNION ALL
`;
    sqlCompleto += `SELECT 'vw_cohesion' as vista, count(*) as registros FROM vw_cohesion
`;
    sqlCompleto += `UNION ALL
`;
    sqlCompleto += `SELECT 'vw_victims' as vista, count(*) as registros FROM vw_victims
`;
    sqlCompleto += `UNION ALL
`;
    sqlCompleto += `SELECT 'vw_aggressors' as vista, count(*) as registros FROM vw_aggressors;

`;
    
    sqlCompleto += `-- Verificar funciones creadas
`;
    sqlCompleto += `SELECT routine_name, routine_type 
`;
    sqlCompleto += `FROM information_schema.routines 
`;
    sqlCompleto += `WHERE routine_schema = 'public' 
`;
    sqlCompleto += `AND routine_name IN ('calcular_comparativas', 'calcular_panorama_curso', 'calcular_sociograma', 'calcular_contexto')
`;
    sqlCompleto += `ORDER BY routine_name;

`;

    // Guardar el archivo
    const archivoSalida = 'scripts/sql-completo-para-supabase.sql';
    fs.writeFileSync(archivoSalida, sqlCompleto);
    
    console.log(`\n✅ SQL completo generado en: ${archivoSalida}`);
    console.log(`\n📋 INSTRUCCIONES:`);
    console.log(`1. Abrir Supabase Dashboard`);
    console.log(`2. Ir a SQL Editor`);
    console.log(`3. Copiar todo el contenido de ${archivoSalida}`);
    console.log(`4. Pegar en el editor y ejecutar`);
    console.log(`5. Verificar que no hay errores`);
    
    // Mostrar estadísticas
    const lineas = sqlCompleto.split('\n').length;
    const caracteres = sqlCompleto.length;
    console.log(`\n📊 Estadísticas:`);
    console.log(`- Líneas: ${lineas}`);
    console.log(`- Caracteres: ${caracteres}`);
    console.log(`- Archivos procesados: ${migraciones.length}`);

  } catch (error) {
    console.error('❌ Error general:', error.message);
  }

  console.log('\n✅ GENERACIÓN COMPLETADA');
}

generarSQLCompleto();