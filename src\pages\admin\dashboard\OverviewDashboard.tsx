import React from 'react';
import { DashboardLayout } from '../../../components/dashboard/layouts/DashboardLayout';
import { DashboardGrid, GridItem } from '../../../components/dashboard/layouts/DashboardGrid';
import { MetricsCard } from '../../../components/dashboard/widgets/MetricsCard';
import { ChartWidget } from '../../../components/dashboard/widgets/ChartWidget';
import { TableWidget } from '../../../components/dashboard/widgets/TableWidget';
import { useDashboardData } from '../../../hooks/dashboard/useDashboardData';
import { useDashboardFilters } from '../../../hooks/dashboard/useDashboardFilters';
import { useDashboardMetrics } from '../../../hooks/dashboard/useDashboardMetrics';
import { DateRangeFilter } from '../../../components/dashboard/filters/DateRangeFilter';
import { InstitutionFilter } from '../../../components/dashboard/filters/InstitutionFilter';
import { GroupFilter } from '../../../components/dashboard/filters/GroupFilter';
import { Button } from '../../../components/ui/button';

export const OverviewDashboard: React.FC = () => {
  const { filters, updateFilter, setDateRange, clearFilters } = useDashboardFilters();
  const { data, loading, error, lastUpdated, refreshData } = useDashboardData(filters);
  const { metrics, chartData, topPerformers, recentActivity } = useDashboardMetrics(data);

  const handleDateRangeChange = (startDate: string, endDate: string) => {
    setDateRange(startDate, endDate);
  };

  const handleDateClear = () => {
    clearFilters();
  };

  if (error) {
    return (
      <DashboardLayout
        title="Resumen General"
        subtitle="Vista general del estado actual"
        onRefresh={refreshData}
        loading={loading}
      >
        <div className="text-center py-8">
          <div className="text-red-500">Error: {error}</div>
          <Button onClick={refreshData} className="mt-4">
            Reintentar
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Resumen General"
      subtitle="Vista general del estado actual"
      onRefresh={refreshData}
      lastUpdated={lastUpdated}
      loading={loading}
      actions={
        <div className="flex items-center space-x-4">
          <DateRangeFilter
            startDate={filters.startDate}
            endDate={filters.endDate}
            onDateRangeChange={handleDateRangeChange}
            onClear={handleDateClear}
          />
          <InstitutionFilter
            value={filters.institutionId}
            onChange={(value) => updateFilter('institutionId', value)}
          />
          <GroupFilter
            value={filters.groupId}
            onChange={(value) => updateFilter('groupId', value)}
          />
        </div>
      }
    >
      {/* Metrics Cards */}
      <DashboardGrid columns={4}>
        {metrics.map((metric) => (
          <MetricsCard
            key={metric.id}
            title={metric.label}
            value={metric.value}
            icon={metric.icon}
            color={metric.color}
            loading={loading}
          />
        ))}
      </DashboardGrid>

      {/* Charts Section */}
      <DashboardGrid columns={2}>
        <GridItem span={1}>
          <ChartWidget
            title="Distribución por Institución"
            data={chartData?.institutionChart}
            type="pie"
            loading={loading}
            height={300}
          />
        </GridItem>
        
        <GridItem span={1}>
          <ChartWidget
            title="Resultados por Mes"
            data={chartData?.resultsChart}
            type="line"
            loading={loading}
            height={300}
          />
        </GridItem>
      </DashboardGrid>

      {/* Tables Section */}
      <DashboardGrid columns={2}>
        <GridItem span={1}>
          <TableWidget
            title="Top 5 Mejores Pacientes"
            data={topPerformers}
            columns={[
              { key: 'name', label: 'Nombre' },
              { key: 'resultCount', label: 'Evaluaciones', type: 'number' },
              { key: 'averageScore', label: 'Promedio', type: 'badge' },
              { key: 'institutionName', label: 'Institución' }
            ]}
            loading={loading}
            maxHeight={300}
          />
        </GridItem>
        
        <GridItem span={1}>
          <TableWidget
            title="Actividad Reciente"
            data={recentActivity}
            columns={[
              { key: 'patientName', label: 'Paciente' },
              { key: 'score', label: 'Puntaje', type: 'badge' },
              { key: 'institutionName', label: 'Institución' },
              { key: 'createdAt', label: 'Fecha', type: 'date' }
            ]}
            loading={loading}
            maxHeight={300}
          />
        </GridItem>
      </DashboardGrid>

      {/* Additional Charts */}
      <DashboardGrid columns={1}>
        <GridItem span={1}>
          <ChartWidget
            title="Distribución de Puntajes"
            data={chartData?.scoreDistribution}
            type="bar"
            loading={loading}
            height={250}
          />
        </GridItem>
      </DashboardGrid>
    </DashboardLayout>
  );
};

export default OverviewDashboard;