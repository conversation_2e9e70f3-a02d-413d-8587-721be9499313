import React, { useState } from 'react';
import { ContextData } from '../../../../types/bulls-dashboard';
import { Card, CardContent, CardHeader, CardTitle } from '../../../ui/card';
import { Badge } from '../../../ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../../../ui/tabs';
import { MapPin, MessageSquare, Clock, Shield, TrendingUp, AlertCircle } from 'lucide-react';
import { 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  PieChart, 
  Pie, 
  Cell,
  ScatterChart,
  Scatter,
  Legend,
  RadialBarChart,
  RadialBar
} from 'recharts';
import AggressionDistributionChart from '../../charts/AggressionDistributionChart';

interface ContextPanelProps {
  data: ContextData;
}

export const ContextPanel: React.FC<ContextPanelProps> = ({ data }) => {
  const [selectedTimeframe, setSelectedTimeframe] = useState<'day' | 'week' | 'month'>('week');

  // Datos simulados para la dimensión temporal
  const temporalData = {
    day: [
      { time: 'Entrada (7-8am)', count: 2, severity: 1.2 },
      { time: 'Recreo 1 (10-10:30am)', count: 8, severity: 2.1 },
      { time: 'Almuerzo (12-1pm)', count: 12, severity: 2.8 },
      { time: 'Recreo 2 (3-3:30pm)', count: 6, severity: 1.9 },
      { time: 'Salida (4-5pm)', count: 4, severity: 1.5 }
    ],
    week: [
      { time: 'Lunes', count: 15, severity: 2.2 },
      { time: 'Martes', count: 12, severity: 1.8 },
      { time: 'Miércoles', count: 18, severity: 2.5 },
      { time: 'Jueves', count: 14, severity: 2.1 },
      { time: 'Viernes', count: 22, severity: 3.1 }
    ],
    month: [
      { time: 'Semana 1', count: 45, severity: 2.1 },
      { time: 'Semana 2', count: 52, severity: 2.4 },
      { time: 'Semana 3', count: 38, severity: 1.9 },
      { time: 'Semana 4', count: 61, severity: 2.8 }
    ]
  };

  const COLORS = {
    primary: '#3b82f6',
    secondary: '#ef4444',
    tertiary: '#f59e0b',
    quaternary: '#10b981',
    quinary: '#8b5cf6'
  };

  const getSafetyLevel = (percentage: number): { label: string; color: string; bgColor: string } => {
    if (percentage >= 80) return { label: 'Alto', color: 'text-green-600', bgColor: 'bg-green-100' };
    if (percentage >= 60) return { label: 'Medio', color: 'text-yellow-600', bgColor: 'bg-yellow-100' };
    if (percentage >= 40) return { label: 'Bajo', color: 'text-orange-600', bgColor: 'bg-orange-100' };
    return { label: 'Crítico', color: 'text-red-600', bgColor: 'bg-red-100' };
  };

  const safetyLevel = getSafetyLevel(data.perceivedSafety);

  // Datos para el gráfico radial de seguridad
  const safetyRadialData = [
    {
      name: 'Seguridad',
      value: data.perceivedSafety,
      fill: data.perceivedSafety >= 60 ? '#10b981' : data.perceivedSafety >= 40 ? '#f59e0b' : '#ef4444'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Métricas principales */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Forma Principal</p>
                <p className="font-semibold">{data.aggressionTypes[0]?.type || 'N/A'}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <MapPin className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Lugar Principal</p>
                <p className="font-semibold">{data.locations[0]?.location || 'N/A'}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600">Incidentes Totales</p>
                <p className="font-semibold">
                  {data.aggressionTypes.reduce((sum, type) => sum + type.count, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Shield className={`h-5 w-5 ${safetyLevel.color}`} />
              <div>
                <p className="text-sm text-gray-600">Seguridad</p>
                <div className="flex items-center space-x-2">
                  <p className="font-semibold">{data.perceivedSafety}%</p>
                  <Badge className={`${safetyLevel.bgColor} ${safetyLevel.color} text-xs`}>
                    {safetyLevel.label}
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="forms" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="forms">Formas de Agresión</TabsTrigger>
          <TabsTrigger value="locations">Lugares</TabsTrigger>
          <TabsTrigger value="temporal">Dimensión Temporal</TabsTrigger>
          <TabsTrigger value="analysis">Análisis Cruzado</TabsTrigger>
        </TabsList>

        <TabsContent value="forms" className="space-y-4">
          {/* Nuevo componente de distribución por curso */}
          <AggressionDistributionChart className="mb-6" />
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageSquare className="h-5 w-5 mr-2" />
                  Formas de Agresión (General)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={data.aggressionTypes}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="type" 
                      angle={-45}
                      textAnchor="end"
                      height={80}
                      fontSize={12}
                    />
                    <YAxis />
                    <Tooltip 
                      formatter={(value, name) => [value, 'Incidentes']}
                      labelFormatter={(label) => `Tipo: ${label}`}
                    />
                    <Bar dataKey="count" fill={COLORS.primary} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Distribución Porcentual (General)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {data.aggressionTypes.map((type, index) => (
                    <div key={index} className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span className="font-medium">{type.type}</span>
                        <span>{type.percentage}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${type.percentage}%` }}
                        ></div>
                      </div>
                      <div className="text-xs text-gray-500">
                        {type.count} incidentes reportados
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="locations" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MapPin className="h-5 w-5 mr-2" />
                  Distribución por Lugares
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={data.locations}
                      cx="50%"
                      cy="50%"
                      outerRadius={100}
                      dataKey="count"
                      label={({ location, percentage }) => `${location} (${percentage}%)`}
                    >
                      {data.locations.map((entry, index) => (
                        <Cell 
                          key={`cell-${index}`} 
                          fill={Object.values(COLORS)[index % Object.values(COLORS).length]} 
                        />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [value, 'Incidentes']} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Análisis de Riesgo por Ubicación</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.locations.map((location, index) => {
                    const riskLevel = location.percentage > 30 ? 'Alto' : location.percentage > 15 ? 'Medio' : 'Bajo';
                    const riskColor = location.percentage > 30 ? 'text-red-600' : location.percentage > 15 ? 'text-yellow-600' : 'text-green-600';
                    const riskBg = location.percentage > 30 ? 'bg-red-100' : location.percentage > 15 ? 'bg-yellow-100' : 'bg-green-100';
                    
                    return (
                      <div key={index} className="border rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{location.location}</h4>
                          <Badge className={`${riskBg} ${riskColor}`}>
                            {riskLevel}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Incidentes:</span>
                            <span className="font-medium ml-2">{location.count}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Porcentaje:</span>
                            <span className="font-medium ml-2">{location.percentage}%</span>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="temporal" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Patrones Temporales
                </div>
                <div className="flex space-x-2">
                  {(['day', 'week', 'month'] as const).map((timeframe) => (
                    <button
                      key={timeframe}
                      onClick={() => setSelectedTimeframe(timeframe)}
                      className={`px-3 py-1 rounded text-sm ${
                        selectedTimeframe === timeframe
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                    >
                      {timeframe === 'day' ? 'Día' : timeframe === 'week' ? 'Semana' : 'Mes'}
                    </button>
                  ))}
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={temporalData[selectedTimeframe]}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis yAxisId="left" orientation="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Legend />
                  <Bar yAxisId="left" dataKey="count" fill={COLORS.primary} name="Incidentes" />
                  <Bar yAxisId="right" dataKey="severity" fill={COLORS.secondary} name="Severidad Promedio" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analysis" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Frecuencia vs Gravedad</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <ScatterChart data={data.frequencyVsGravity}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="frequency" 
                      name="Frecuencia"
                      label={{ value: 'Frecuencia', position: 'insideBottom', offset: -5 }}
                    />
                    <YAxis 
                      dataKey="gravity" 
                      name="Gravedad"
                      label={{ value: 'Gravedad', angle: -90, position: 'insideLeft' }}
                    />
                    <Tooltip 
                      formatter={(value, name) => [
                        name === 'frequency' ? `${value} veces` : `${value}/10`,
                        name === 'frequency' ? 'Frecuencia' : 'Gravedad'
                      ]}
                      labelFormatter={() => ''}
                    />
                    <Scatter 
                      dataKey="students" 
                      fill={COLORS.tertiary}
                      name="Estudiantes afectados"
                    />
                  </ScatterChart>
                </ResponsiveContainer>
                <div className="mt-2 text-xs text-gray-500">
                  El tamaño de las burbujas representa el número de estudiantes afectados
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="h-5 w-5 mr-2" />
                  Termómetro de Seguridad
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col items-center space-y-4">
                  <ResponsiveContainer width="100%" height={200}>
                    <RadialBarChart 
                      cx="50%" 
                      cy="50%" 
                      innerRadius="60%" 
                      outerRadius="90%" 
                      data={safetyRadialData}
                      startAngle={180}
                      endAngle={0}
                    >
                      <RadialBar 
                        dataKey="value" 
                        cornerRadius={10} 
                        fill={safetyRadialData[0].fill}
                      />
                      <text 
                        x="50%" 
                        y="50%" 
                        textAnchor="middle" 
                        dominantBaseline="middle" 
                        className="text-2xl font-bold"
                      >
                        {data.perceivedSafety}%
                      </text>
                    </RadialBarChart>
                  </ResponsiveContainer>
                  
                  <div className="text-center">
                    <p className="text-lg font-semibold">"Me siento seguro/a"</p>
                    <Badge className={`${safetyLevel.bgColor} ${safetyLevel.color} mt-2`}>
                      Nivel {safetyLevel.label}
                    </Badge>
                  </div>
                  
                  <div className="w-full space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Muy inseguro</span>
                      <span>Muy seguro</span>
                    </div>
                    <div className="w-full bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 h-2 rounded-full"></div>
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>0%</span>
                      <span>50%</span>
                      <span>100%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};