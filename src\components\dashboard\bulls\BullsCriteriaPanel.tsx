import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Badge } from '../../ui/badge';
import { Alert, AlertDescription } from '../../ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../ui/select';
import { AlertTriangle, Users, Shield, Target } from 'lucide-react';
import { useBullsCriteria } from '../../../hooks/dashboard/useBullsCriteria';
import BullsCriteriaKPICards from './BullsCriteriaKPICards';
import BullsCriteriaTable from './BullsCriteriaTable';
import { supabase } from '../../../lib/supabase';

const BullsCriteriaPanel: React.FC = () => {
  const {
    estudiantes,
    resumen: resumenCursos,
    stats,
    loading,
    error,
    refetch: refreshData
  } = useBullsCriteria();
  
  // Estado para grupos y filtros
  const [grupos, setGrupos] = useState<{id: string, nombre: string}[]>([]);
  const [grupoSeleccionado, setGrupoSeleccionado] = useState<string>('todos');
  
  // Cargar grupos
  useEffect(() => {
    const cargarGrupos = async () => {
      try {
        const { data, error } = await supabase
          .from('grupos')
          .select('id, nombre')
          .order('nombre');
        
        if (error) throw error;
        setGrupos(data || []);
      } catch (err) {
        console.error('Error cargando grupos:', err);
      }
    };
    
    cargarGrupos();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-gray-600">Cargando criterios BULL-S...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert className="m-4">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Shield className="h-6 w-6 text-blue-600" />
            Criterio BULL-S Oficial
          </h2>
          <p className="text-gray-600 mt-1">
            Identificación de estudiantes en riesgo según criterios oficiales BULL-S
          </p>
        </div>
        
        {/* Filtro por grupo */}
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium text-gray-700">Grupo:</label>
          <Select value={grupoSeleccionado} onValueChange={setGrupoSeleccionado}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Seleccionar grupo" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="todos">Todos los grupos</SelectItem>
              {grupos.map((grupo) => (
                <SelectItem key={grupo.id} value={grupo.id}>
                  {grupo.nombre}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Alertas críticas */}
      {stats.gruposCriticos > 0 && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            <strong>¡Alerta Crítica!</strong> {stats.gruposCriticos} grupo(s) tienen ≥25% de estudiantes en riesgo.
          </AlertDescription>
        </Alert>
      )}

      {/* KPIs */}
      <BullsCriteriaKPICards grupoId={grupoSeleccionado === 'todos' ? undefined : grupoSeleccionado} />

      {/* Tabla de estudiantes */}
      <BullsCriteriaTable 
        estudiantes={estudiantes}
      />

      {/* Información sobre criterios BULL-S */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Criterios BULL-S
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Preguntas para Agresores:</h4>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• Q5: ¿Quiénes son los/as más fuertes de la clase?</li>
                <li>• Q7: ¿Quiénes maltratan o pegan a otros/as compañeros/as?</li>
                <li>• Q9: ¿Quiénes suelen empezar las peleas?</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Preguntas para Víctimas:</h4>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• Q6: ¿Quiénes actúan como un/a cobarde o un bebé?</li>
                <li>• Q8: ¿Quiénes suelen ser las víctimas?</li>
                <li>• Q10: ¿A quiénes se les tiene manía?</li>
              </ul>
            </div>
          </div>
          <div className="mt-4 p-3 bg-blue-100 rounded-lg">
            <p className="text-sm font-medium">
              Un estudiante está en riesgo si es nombrado por ≥25% de sus compañeros en cualquier rol.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BullsCriteriaPanel;