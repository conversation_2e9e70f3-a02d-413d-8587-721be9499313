// Tipos específicos para el Dashboard BULL-S

export interface BullsKPIs {
  studentsAtRisk: {
    value: number;
    percentage: number;
    trend: number;
  };
  groupCohesion: {
    value: number;
    level: 'bajo' | 'medio' | 'alto';
    color: 'red' | 'yellow' | 'green';
  };
  perceivedSafety: {
    value: number;
    level: 'bajo' | 'medio' | 'alto';
    color: 'red' | 'yellow' | 'green';
  };
  topAggressionTypes: {
    type: string;
    count: number;
    percentage: number;
  }[];
}

export interface SociogramNode {
  id: string;
  name: string;
  x: number;
  y: number;
  size: number;
  color: string;
  status: 'popular' | 'promedio' | 'aislado' | 'rechazado' | 'controvertido';
  role: 'agresor' | 'victima' | 'observador' | 'no_implicado';
  popularity: number;
  rejection: number;
}

export interface SociogramEdge {
  source: string;
  target: string;
  type: 'eleccion' | 'rechazo' | 'agresion';
  intensity: number;
  reciprocal: boolean;
  color: string;
  width: number;
}

export interface SociogramCluster {
  id: string;
  name: string;
  members: string[];
  density: number;
  cohesion: number;
}

export interface SociogramMetrics {
  density: number;
  reciprocity: number;
  transitivity: number;
  modularity: number;
}

export interface SociogramData {
  nodes: SociogramNode[];
  edges: SociogramEdge[];
  clusters: SociogramCluster[];
  metrics: SociogramMetrics;
}

export interface AggressorProfile {
  id: string;
  name: string;
  institution: string;
  aggressionLevel: number;
  traits: {
    strong: number;
    provocative: number;
    aggressive: number;
  };
}

export interface VictimProfile {
  id: string;
  name: string;
  institution: string;
  victimizationLevel: number;
  traits: {
    coward: number;
    victim: number;
    mania: number;
  };
}

export interface AggressionMatrix {
  aggressorId: string;
  victimId: string;
  intensity: number;
}

export interface ProfilesData {
  aggressors: AggressorProfile[];
  victims: VictimProfile[];
  aggressorTraits: {
    strong: number;
    provocative: number;
    aggressive: number;
  };
  victimTraits: {
    coward: number;
    victim: number;
    mania: number;
  };
  matrix: AggressionMatrix[];
}

export interface ContextData {
  aggressionTypes: {
    type: string;
    count: number;
    percentage: number;
  }[];
  locations: {
    location: string;
    count: number;
    percentage: number;
  }[];
  frequencyVsGravity: {
    frequency: number;
    gravity: number;
    students: number;
  }[];
  perceivedSafety: number;
}

export interface ComparisonsData {
  courseComparisons: {
    course: string;
    studentsAtRisk: number;
    cohesion: number;
    safety: number;
    trend?: 'up' | 'down' | 'stable';
    evolution?: {
      period: string;
      value: number;
    }[];
  }[];
  genderDistribution: {
    gender: string;
    aggressors: number;
    victims: number;
    observers: number;
    totalStudents: number;
    bullyingLevel: number;
    cohesion: number;
    safety: number;
  }[];
  roleDistribution: {
    role: string;
    count: number;
    percentage: number;
    riskLevel?: string;
  }[];
  // Alias para compatibilidad con ComparisonsPanel
  genders?: {
    gender: string;
    aggressors: number;
    victims: number;
    observers: number;
    totalStudents: number;
    bullyingLevel: number;
    cohesion: number;
    safety: number;
  }[];
  roles?: {
    role: string;
    count: number;
    percentage: number;
    riskLevel?: string;
  }[];
  // Datos de evolución temporal
  evolutionData?: {
    course: string;
    data: {
      period: string;
      value: number;
    }[];
  }[];
}

export interface BullsGlobalFilter {
  course?: string;
  gender?: 'M' | 'F' | 'all';
  role?: 'agresor' | 'victima' | 'observador' | 'all';
  period?: string;
  institutionId?: string;
  startDate?: string;
  endDate?: string;
}

export interface StudentProfile {
  id: string;
  name: string;
  course: string;
  institution: string;
  riskLevel: 'bajo' | 'medio' | 'alto' | 'critico';
  popularityIndex: number;
  rejectionIndex: number;
  roles: string[];
  interventions: {
    date: string;
    type: string;
    description: string;
    responsible: string;
  }[];
  kpis: {
    socialStatus: string;
    riskScore: number;
    popularityScore: number;
    rejectionScore: number;
  };
}

export interface Alert {
  id: string;
  type: 'aislamiento' | 'rechazo_masivo' | 'agresor' | 'victima' | 'cluster_problematico';
  severity: 'bajo' | 'medio' | 'alto' | 'critico';
  studentId: string;
  studentName: string;
  description: string;
  recommendations: string[];
  date: string;
  status: 'nueva' | 'en_proceso' | 'resuelta';
}

export interface PreConfiguredView {
  id: string;
  name: string;
  description: string;
  filters: BullsGlobalFilter;
  focusStudents?: string[];
  narrative: string[];
}

export interface CourseOverviewData {
  courseName: string;
  totalStudents: number;
  responseRate: number;
  lastUpdate: string;
  academicPeriod: string;
  riskLevel: 'bajo' | 'medio' | 'alto' | 'critico';
  cohesionLevel: number;
  safetyPerception: number;
  keyMetrics: {
    aggressors: number;
    victims: number;
    observers: number;
    notInvolved: number;
  };
  trends: {
    metric: string;
    value: number;
    change: number;
    trend: 'up' | 'down' | 'stable';
  }[];
  alerts: {
    id: string;
    type: string;
    severity: 'bajo' | 'medio' | 'alto' | 'critico';
    description: string;
  }[];
}

export interface Recommendation {
  id: string;
  type: 'individual' | 'grupal' | 'institucional';
  priority: 'baja' | 'media' | 'alta' | 'critica';
  title: string;
  description: string;
  specificActions: string[];
  requiredResources: string[];
  estimatedTime: string;
  responsible: string[];
  successIndicators: string[];
  status: 'pendiente' | 'en_proceso' | 'completada';
  dueDate: string;
  targetStudents?: string[];
}

export interface RecommendedActionsData {
  recommendations: Recommendation[];
  summary: {
    total: number;
    byPriority: {
      critica: number;
      alta: number;
      media: number;
      baja: number;
    };
    byType: {
      individual: number;
      grupal: number;
      institucional: number;
    };
    byStatus: {
      pendiente: number;
      en_proceso: number;
      completada: number;
    };
  };
}