// src/layouts/menuConfig.ts
import {
  Users,
  FileText,
  Settings,
  HelpCircle,
  LogOut,
  Home,
  School,
  UserCheck,
  User,
  BarChart3,
  <PERSON>rendingU<PERSON>,
  <PERSON><PERSON><PERSON>
} from 'lucide-react';

export interface MenuItem {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  path?: string;
  subItems?: MenuItem[];
  isDivider?: boolean;
  isBottom?: boolean;
}

export const menuItems: MenuItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: BarChart3,
    path: '/admin/dashboard',
    subItems: [
      { id: 'overview', label: 'Resumen General', icon: Home, path: '/admin/dashboard/overview' },
      { id: 'analytics', label: 'Análisis Detallado', icon: TrendingUp, path: '/admin/dashboard/analytics' },
      { id: 'reports-dashboard', label: 'Reportes Visuales', icon: <PERSON><PERSON><PERSON>, path: '/admin/dashboard/reports' },
      { id: 'bulls', label: 'BULL-S Dashboard', icon: BarChart3, path: '/admin/dashboard/bulls' },
    ],
  },
  {
    id: 'management',
    label: 'Gestión',
    icon: Settings,
    subItems: [
      { id: 'institutions', label: 'Instituciones', icon: School, path: '/admin/instituciones' },
      { id: 'groups', label: 'Grupos', icon: Users, path: '/admin/grupos' },
      { id: 'students', label: 'Estudiantes', icon: User, path: '/admin/estudiantes' },
      { id: 'users', label: 'Usuarios del Sistema', icon: UserCheck, path: '/admin/usuarios' },
    ],
  },
  {
    id: 'reports',
    label: 'Informes',
    icon: FileText,
    path: '/admin/reports',
  },
  {
    id: 'divider-1',
    isDivider: true,
    isBottom: true,
  } as MenuItem,
  { id: 'help', label: 'Ayuda y Soporte', icon: HelpCircle, isBottom: true },
  { id: 'logout', label: 'Cerrar Sesión', icon: LogOut, isBottom: true },
];

export const favoriteableItems: string[] = [
  'students',
  'reports',
];