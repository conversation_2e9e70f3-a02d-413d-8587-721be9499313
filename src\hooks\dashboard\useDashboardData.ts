import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../../lib/supabase';
import { DashboardData, DashboardFilter } from '../../types/dashboard';

export const useDashboardData = (filters: DashboardFilter) => {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<string | null>(null);

  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch students first with filters applied
      let studentsQuery = supabase
        .from('estudiantes')
        .select('*, instituciones_educativas(*)');

      // Apply filters to students
      if (filters.institutionId) {
        studentsQuery = studentsQuery.eq('institucion_id', filters.institutionId);
      }

      if (filters.groupId) {
        studentsQuery = studentsQuery.eq('grado', filters.groupId);
      }

      if (filters.patientType) {
        studentsQuery = studentsQuery.eq('genero', filters.patientType);
      }

      const { data: students, error: studentsError } = await studentsQuery;

      if (studentsError) throw studentsError;

      // Get student IDs for filtering responses
      const studentIds = students?.map(s => s.id) || [];

      // Fetch responses for filtered students
      let respuestasData = [];

      if (studentIds.length > 0) {
        let responsesQuery = supabase
          .from('respuestas')
          .select('*')
          .in('estudiante_id', studentIds);

        if (filters.startDate) {
          responsesQuery = responsesQuery.gte('fecha_respuesta', filters.startDate);
        }

        if (filters.endDate) {
          responsesQuery = responsesQuery.lte('fecha_respuesta', filters.endDate);
        }

        const { data: responses, error: respuestasError } = await responsesQuery;

        if (respuestasError) {
          console.warn('Warning: Could not fetch responses:', respuestasError.message);
          // Continue with empty responses instead of throwing
          respuestasData = [];
        } else {
          respuestasData = responses || [];
        }
      }

      // Fetch institutions
      const { data: institutions, error: institutionsError } = await supabase
        .from('instituciones_educativas')
        .select('*');

      if (institutionsError) throw institutionsError;

      // Enrich responses with student data
      const enrichedResponses = respuestasData?.map(response => {
        const student = students?.find(s => s.id === response.estudiante_id);
        return {
          ...response,
          estudiantes: student
        };
      }) || [];

      // Process data
      const processedData: DashboardData = {
        totalPatients: students?.length || 0,
        totalInstitutions: institutions?.length || 0,
        totalResults: respuestasData?.length || 0,
        averageScore: respuestasData?.length > 0
          ? respuestasData.reduce((sum, r) => sum + (r.id ? 1 : 0), 0) / respuestasData.length
          : 0,
        recentResults: enrichedResponses.slice(-10).reverse(),
        rawResponses: respuestasData || [],
        institutionMetrics: processInstitutionMetrics(institutions || [], enrichedResponses),
        patientMetrics: processPatientMetrics(students || [], respuestasData || []),
        resultMetrics: processResultMetrics(enrichedResponses)
      };

      setData(processedData);
      setLastUpdated(new Date().toISOString());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  const processInstitutionMetrics = (institutions: any[], results: any[]) => {
    return institutions.map(inst => {
      const institutionResults = results.filter(r => r.estudiantes?.institucion_id === inst.id);
      const studentsInInstitution = [...new Set(institutionResults.map(r => r.estudiante_id))];
      
      return {
        id: inst.id,
        name: inst.nombre,
        patientCount: studentsInInstitution.length,
        resultCount: institutionResults.length,
        averageScore: institutionResults.length > 0 
          ? institutionResults.length 
          : 0,
        lastActivity: institutionResults.length > 0
          ? institutionResults[institutionResults.length - 1].fecha_respuesta
          : inst.fecha_creacion
      };
    });
  };

  const processPatientMetrics = (students: any[], results: any[]) => {
    return students.map(student => {
      const studentResults = results.filter(r => r.estudiante_id === student.id);

      return {
        id: student.id,
        name: `${student.nombre_estudiante} ${student.apellido_estudiante}`,
        institutionName: student.instituciones_educativas?.nombre || 'Sin institución',
        resultCount: studentResults.length,
        averageScore: studentResults.length > 0
          ? studentResults.length
          : 0,
        lastResultDate: studentResults.length > 0
          ? studentResults[studentResults.length - 1].fecha_respuesta
          : student.fecha_creacion
      };
    });
  };

  const processResultMetrics = (results: any[]) => {
    return results.map(result => ({
      id: result.id,
      patientName: result.estudiantes ? `${result.estudiantes.nombre_estudiante} ${result.estudiantes.apellido_estudiante}` : 'Estudiante desconocido',
      institutionName: result.estudiantes?.instituciones_educativas?.nombre || 'Sin institución',
      score: 1,
      createdAt: result.fecha_respuesta,
      testType: 'Cuestionario BULL-S'
    }));
  };

  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  const refreshData = useCallback(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  return {
    data,
    loading,
    error,
    lastUpdated,
    refreshData
  };
};