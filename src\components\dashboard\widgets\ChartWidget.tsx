import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../../ui/card';
import { <PERSON>, <PERSON><PERSON><PERSON>, Line, <PERSON><PERSON>hart, <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, XAxis, YAxis, CartesianGrid, Tooltip, Legend, Cell } from 'recharts';
import { ChartData } from '../../../types/dashboard';

interface ChartWidgetProps {
  title: string;
  data: ChartData;
  type: 'bar' | 'line' | 'pie';
  loading?: boolean;
  height?: number;
}

export const ChartWidget: React.FC<ChartWidgetProps> = ({
  title,
  data,
  type,
  loading = false,
  height = 300
}) => {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">
            <div className="h-6 bg-gray-200 rounded w-32 animate-pulse"></div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 bg-gray-100 rounded animate-pulse"></div>
        </CardContent>
      </Card>
    );
  }

  const renderChart = () => {
    switch (type) {
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <BarChart data={data.datasets[0].data.map((value, index) => ({
              label: data.labels[index],
              value
            }))}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="label" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="value" fill={data.datasets[0].backgroundColor?.[0] || '#3B82F6'} />
            </BarChart>
          </ResponsiveContainer>
        );

      case 'line':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <LineChart data={data.datasets[0].data.map((value, index) => ({
              label: data.labels[index],
              value
            }))}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="label" />
              <YAxis />
              <Tooltip />
              <Line 
                type="monotone" 
                dataKey="value" 
                stroke={data.datasets[0].borderColor || '#3B82F6'} 
                strokeWidth={2}
                dot={{ fill: data.datasets[0].borderColor || '#3B82F6' }}
              />
            </LineChart>
          </ResponsiveContainer>
        );

      case 'pie':
        const pieData = data.labels.map((label, index) => ({
          name: label,
          value: data.datasets[0].data[index],
          color: data.datasets[0].backgroundColor?.[index] || '#3B82F6'
        }));

        return (
          <ResponsiveContainer width="100%" height={height}>
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        );

      default:
        return null;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        {renderChart()}
      </CardContent>
    </Card>
  );
};