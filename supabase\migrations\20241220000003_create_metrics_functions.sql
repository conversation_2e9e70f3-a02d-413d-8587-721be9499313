-- Migración para crear funciones RPC de métricas de convivencia
-- Estas funciones centralizan los cálculos complejos en la base de datos

-- Función para calcular métricas agregadas de un grupo
CREATE OR REPLACE FUNCTION public.calcular_metricas_grupo(
    p_grupo_id uuid DEFAULT NULL,
    p_institucion_id uuid DEFAULT NULL
)
RETURNS TABLE (
    grupo_id uuid,
    nombre_grupo text,
    total_estudiantes bigint,
    estudiantes_con_respuestas bigint,
    porcentaje_participacion numeric,
    -- Métricas de bullying
    promedio_agresion numeric,
    promedio_victimizacion numeric,
    promedio_observacion numeric,
    estudiantes_alto_riesgo_bullying bigint,
    porcentaje_alto_riesgo_bullying numeric,
    -- Métricas sociométricas
    estudiantes_populares bigint,
    estudiantes_rechazados bigint,
    estudiantes_aislados bigint,
    estudiantes_controvertidos bigint,
    porcentaje_aislamiento numeric,
    -- Cohesión grupal
    indice_cohesion numeric,
    nivel_cohesion text,
    -- Evaluación general
    nivel_riesgo_general text,
    recomendaciones text[]
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    WITH grupos_filtrados AS (
        SELECT g.id, g.nombre, g.institucion_id
        FROM public.grupos g
        WHERE 
            (p_grupo_id IS NULL OR g.id = p_grupo_id)
            AND (p_institucion_id IS NULL OR g.institucion_id = p_institucion_id)
    ),
    metricas_base AS (
        SELECT 
            gf.id AS grupo_id,
            gf.nombre AS nombre_grupo,
            COUNT(DISTINCT e.id) AS total_estudiantes,
            COUNT(DISTINCT CASE WHEN vcm.estudiante_id IS NOT NULL THEN e.id END) AS estudiantes_con_respuestas,
            -- Métricas de bullying
            AVG(vcm.nivel_agresion) AS promedio_agresion,
            AVG(vcm.nivel_victimizacion) AS promedio_victimizacion,
            AVG(vcm.nivel_observacion) AS promedio_observacion,
            COUNT(CASE WHEN vcm.nivel_riesgo_bullying = 'alto' THEN 1 END) AS estudiantes_alto_riesgo_bullying,
            -- Métricas sociométricas
            COUNT(CASE WHEN vcm.estatus_sociometrico = 'popular' THEN 1 END) AS estudiantes_populares,
            COUNT(CASE WHEN vcm.estatus_sociometrico = 'rechazado' THEN 1 END) AS estudiantes_rechazados,
            COUNT(CASE WHEN vcm.estatus_sociometrico = 'aislado' THEN 1 END) AS estudiantes_aislados,
            COUNT(CASE WHEN vcm.estatus_sociometrico = 'controvertido' THEN 1 END) AS estudiantes_controvertidos,
            -- Cohesión (basada en reciprocidad de elecciones)
            AVG(vcm.elecciones_recibidas) AS promedio_elecciones
        FROM grupos_filtrados gf
        LEFT JOIN public.estudiantes e ON gf.id = e.grupo_id
        LEFT JOIN public.view_convivencia_metrics vcm ON e.id = vcm.estudiante_id
        GROUP BY gf.id, gf.nombre
    )
    SELECT 
        mb.grupo_id,
        mb.nombre_grupo,
        mb.total_estudiantes,
        mb.estudiantes_con_respuestas,
        CASE 
            WHEN mb.total_estudiantes > 0 
            THEN ROUND((mb.estudiantes_con_respuestas::numeric / mb.total_estudiantes::numeric) * 100, 2)
            ELSE 0
        END AS porcentaje_participacion,
        
        -- Métricas de bullying
        ROUND(COALESCE(mb.promedio_agresion, 0), 2) AS promedio_agresion,
        ROUND(COALESCE(mb.promedio_victimizacion, 0), 2) AS promedio_victimizacion,
        ROUND(COALESCE(mb.promedio_observacion, 0), 2) AS promedio_observacion,
        mb.estudiantes_alto_riesgo_bullying,
        CASE 
            WHEN mb.total_estudiantes > 0 
            THEN ROUND((mb.estudiantes_alto_riesgo_bullying::numeric / mb.total_estudiantes::numeric) * 100, 2)
            ELSE 0
        END AS porcentaje_alto_riesgo_bullying,
        
        -- Métricas sociométricas
        mb.estudiantes_populares,
        mb.estudiantes_rechazados,
        mb.estudiantes_aislados,
        mb.estudiantes_controvertidos,
        CASE 
            WHEN mb.total_estudiantes > 0 
            THEN ROUND((mb.estudiantes_aislados::numeric / mb.total_estudiantes::numeric) * 100, 2)
            ELSE 0
        END AS porcentaje_aislamiento,
        
        -- Cohesión grupal (basada en distribución de elecciones)
        CASE 
            WHEN mb.promedio_elecciones IS NULL THEN 0
            WHEN mb.promedio_elecciones >= 3 THEN ROUND(85 + (mb.promedio_elecciones - 3) * 5, 2)
            WHEN mb.promedio_elecciones >= 2 THEN ROUND(65 + (mb.promedio_elecciones - 2) * 20, 2)
            WHEN mb.promedio_elecciones >= 1 THEN ROUND(35 + (mb.promedio_elecciones - 1) * 30, 2)
            ELSE ROUND(mb.promedio_elecciones * 35, 2)
        END AS indice_cohesion,
        
        CASE 
            WHEN mb.promedio_elecciones >= 3 THEN 'Alta'
            WHEN mb.promedio_elecciones >= 2 THEN 'Media'
            WHEN mb.promedio_elecciones >= 1 THEN 'Baja'
            ELSE 'Muy Baja'
        END AS nivel_cohesion,
        
        -- Evaluación general de riesgo
        CASE 
            WHEN (mb.estudiantes_alto_riesgo_bullying::numeric / GREATEST(mb.total_estudiantes, 1)) > 0.2 
                 OR (mb.estudiantes_aislados::numeric / GREATEST(mb.total_estudiantes, 1)) > 0.3
            THEN 'Alto'
            WHEN (mb.estudiantes_alto_riesgo_bullying::numeric / GREATEST(mb.total_estudiantes, 1)) > 0.1 
                 OR (mb.estudiantes_aislados::numeric / GREATEST(mb.total_estudiantes, 1)) > 0.2
            THEN 'Medio'
            ELSE 'Bajo'
        END AS nivel_riesgo_general,
        
        -- Recomendaciones basadas en métricas
        ARRAY(
            SELECT recomendacion FROM (
                SELECT 'Implementar programas de integración social' AS recomendacion, 1 AS orden
                WHERE (mb.estudiantes_aislados::numeric / GREATEST(mb.total_estudiantes, 1)) > 0.2
                
                UNION ALL
                
                SELECT 'Fortalecer protocolos anti-bullying' AS recomendacion, 2 AS orden
                WHERE (mb.estudiantes_alto_riesgo_bullying::numeric / GREATEST(mb.total_estudiantes, 1)) > 0.15
                
                UNION ALL
                
                SELECT 'Desarrollar actividades de cohesión grupal' AS recomendacion, 3 AS orden
                WHERE mb.promedio_elecciones < 2
                
                UNION ALL
                
                SELECT 'Capacitar docentes en detección temprana' AS recomendacion, 4 AS orden
                WHERE mb.promedio_observacion > 2.5
                
                UNION ALL
                
                SELECT 'Implementar mediación entre pares' AS recomendacion, 5 AS orden
                WHERE mb.estudiantes_controvertidos > 0
                
                UNION ALL
                
                SELECT 'Mantener monitoreo regular' AS recomendacion, 6 AS orden
                WHERE (mb.estudiantes_alto_riesgo_bullying::numeric / GREATEST(mb.total_estudiantes, 1)) <= 0.1
            ) AS r ORDER BY r.orden
        ) AS recomendaciones
        
    FROM metricas_base mb;
END;
$$;

-- Función para obtener el resumen ejecutivo de convivencia
CREATE OR REPLACE FUNCTION public.generar_resumen_convivencia(
    p_grupo_id uuid DEFAULT NULL,
    p_institucion_id uuid DEFAULT NULL
)
RETURNS TABLE (
    resumen_general text,
    nivel_riesgo text,
    indicadores_clave jsonb,
    tendencias text[],
    acciones_prioritarias text[],
    fecha_generacion timestamp with time zone
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_metricas RECORD;
    v_total_grupos INTEGER;
    v_grupos_alto_riesgo INTEGER;
BEGIN
    -- Obtener métricas agregadas
    SELECT 
        COUNT(*) AS total_grupos,
        SUM(CASE WHEN nivel_riesgo_general = 'Alto' THEN 1 ELSE 0 END) AS grupos_alto_riesgo,
        AVG(porcentaje_alto_riesgo_bullying) AS promedio_riesgo_bullying,
        AVG(porcentaje_aislamiento) AS promedio_aislamiento,
        AVG(indice_cohesion) AS promedio_cohesion
    INTO v_total_grupos, v_grupos_alto_riesgo, v_metricas
    FROM public.calcular_metricas_grupo(p_grupo_id, p_institucion_id);
    
    RETURN QUERY
    SELECT 
        -- Resumen general
        CASE 
            WHEN v_total_grupos = 0 THEN 'No hay datos suficientes para generar un análisis.'
            WHEN v_grupos_alto_riesgo::numeric / v_total_grupos > 0.5 THEN 
                'La institución presenta indicadores preocupantes de convivencia escolar que requieren intervención inmediata.'
            WHEN v_grupos_alto_riesgo::numeric / v_total_grupos > 0.2 THEN 
                'Se observan algunos grupos con riesgo elevado que necesitan atención especializada.'
            ELSE 
                'La convivencia escolar se encuentra en niveles aceptables con oportunidades de mejora.'
        END AS resumen_general,
        
        -- Nivel de riesgo general
        CASE 
            WHEN v_grupos_alto_riesgo::numeric / GREATEST(v_total_grupos, 1) > 0.5 THEN 'Alto'
            WHEN v_grupos_alto_riesgo::numeric / GREATEST(v_total_grupos, 1) > 0.2 THEN 'Medio'
            ELSE 'Bajo'
        END AS nivel_riesgo,
        
        -- Indicadores clave en formato JSON
        jsonb_build_object(
            'total_grupos', v_total_grupos,
            'grupos_alto_riesgo', v_grupos_alto_riesgo,
            'porcentaje_riesgo_bullying', ROUND(v_metricas.promedio_riesgo_bullying, 2),
            'porcentaje_aislamiento', ROUND(v_metricas.promedio_aislamiento, 2),
            'indice_cohesion_promedio', ROUND(v_metricas.promedio_cohesion, 2)
        ) AS indicadores_clave,
        
        -- Tendencias identificadas
        ARRAY(
            SELECT tendencia FROM (
                SELECT 'Aislamiento social elevado' AS tendencia, 1 AS orden
                WHERE v_metricas.promedio_aislamiento > 20
                
                UNION ALL
                
                SELECT 'Indicadores de bullying preocupantes' AS tendencia, 2 AS orden
                WHERE v_metricas.promedio_riesgo_bullying > 15
                
                UNION ALL
                
                SELECT 'Baja cohesión grupal' AS tendencia, 3 AS orden
                WHERE v_metricas.promedio_cohesion < 50
                
                UNION ALL
                
                SELECT 'Convivencia estable' AS tendencia, 4 AS orden
                WHERE v_metricas.promedio_riesgo_bullying <= 10 AND v_metricas.promedio_aislamiento <= 15
            ) AS t ORDER BY t.orden
        ) AS tendencias,
        
        -- Acciones prioritarias
        ARRAY(
            SELECT accion FROM (
                SELECT 'Implementar programa integral anti-bullying' AS accion, 1 AS orden
                WHERE v_metricas.promedio_riesgo_bullying > 15
                
                UNION ALL
                
                SELECT 'Desarrollar estrategias de inclusión social' AS accion, 2 AS orden
                WHERE v_metricas.promedio_aislamiento > 20
                
                UNION ALL
                
                SELECT 'Fortalecer dinámicas de grupo' AS accion, 3 AS orden
                WHERE v_metricas.promedio_cohesion < 50
                
                UNION ALL
                
                SELECT 'Capacitar equipo docente en convivencia' AS accion, 4 AS orden
                WHERE v_grupos_alto_riesgo > 0
                
                UNION ALL
                
                SELECT 'Mantener monitoreo preventivo' AS accion, 5 AS orden
                WHERE v_grupos_alto_riesgo = 0
            ) AS a ORDER BY a.orden
        ) AS acciones_prioritarias,
        
        NOW() AS fecha_generacion;
END;
$$;

-- Otorgar permisos de ejecución
GRANT EXECUTE ON FUNCTION public.calcular_metricas_grupo(uuid, uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.generar_resumen_convivencia(uuid, uuid) TO authenticated;

-- Crear índices adicionales para optimizar las consultas
CREATE INDEX IF NOT EXISTS idx_respuestas_estudiante_pregunta ON public.respuestas(estudiante_id, pregunta_id);
CREATE INDEX IF NOT EXISTS idx_preguntas_categoria ON public.preguntas(categoria, tipo_pregunta);
CREATE INDEX IF NOT EXISTS idx_estudiantes_grupo ON public.estudiantes(grupo_id);