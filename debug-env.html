<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Environment Variables</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; border: 1px solid #f44336; }
        .success { background: #e8f5e8; border: 1px solid #4caf50; }
    </style>
</head>
<body>
    <h1>Debug Environment Variables</h1>
    <div id="results"></div>
    
    <script type="module">
        const results = document.getElementById('results');
        
        function addResult(title, content, isError = false) {
            const div = document.createElement('div');
            div.className = `result ${isError ? 'error' : 'success'}`;
            div.innerHTML = `<h3>${title}</h3><pre>${content}</pre>`;
            results.appendChild(div);
        }
        
        // Test 1: Check if import.meta.env is available
        try {
            addResult('import.meta.env availability', JSON.stringify(import.meta.env, null, 2));
        } catch (error) {
            addResult('import.meta.env error', error.message, true);
        }
        
        // Test 2: Check specific environment variables
        const envVars = {
            'VITE_SUPABASE_URL': import.meta.env.VITE_SUPABASE_URL,
            'VITE_SUPABASE_ANON_KEY': import.meta.env.VITE_SUPABASE_ANON_KEY
        };
        
        addResult('Environment Variables', JSON.stringify(envVars, null, 2));
        
        // Test 3: Try to import Supabase client
        try {
            const { createClient } = await import('@supabase/supabase-js');
            
            if (!envVars.VITE_SUPABASE_URL || !envVars.VITE_SUPABASE_ANON_KEY) {
                throw new Error('Missing environment variables');
            }
            
            const supabase = createClient(envVars.VITE_SUPABASE_URL, envVars.VITE_SUPABASE_ANON_KEY);
            
            // Test simple query
            const { data, error } = await supabase
                .from('respuestas')
                .select('id')
                .limit(1);
                
            if (error) {
                addResult('Supabase Query Error', error.message, true);
            } else {
                addResult('Supabase Query Success', JSON.stringify(data, null, 2));
            }
            
        } catch (error) {
            addResult('Supabase Import/Query Error', error.message, true);
        }
    </script>
</body>
</html>