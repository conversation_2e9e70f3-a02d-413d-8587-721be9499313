import React from 'react';
import { useBullsRealData } from '../../../hooks/dashboard/useBullsRealData';

/**
 * Componente de prueba para verificar que las métricas BULL-S funcionen correctamente
 */
export const TestBullsMetrics: React.FC = () => {
  const { kpis, loading, error } = useBullsRealData();

  if (loading) {
    return <div className="p-4">Cargando métricas BULL-S...</div>;
  }

  if (error) {
    return <div className="p-4 text-red-600">Error: {error}</div>;
  }

  return (
    <div className="p-4 space-y-4">
      <h2 className="text-xl font-bold">Prueba de Métricas BULL-S</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="font-semibold text-red-600">Estudiantes en Riesgo</h3>
          <p className="text-2xl font-bold">{kpis.studentsAtRisk.value}</p>
          <p className="text-sm text-gray-600">({kpis.studentsAtRisk.percentage}%)</p>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="font-semibold text-red-600">Agresores</h3>
          <p className="text-2xl font-bold">{kpis.aggressors.value}</p>
          <p className="text-sm text-gray-600">({kpis.aggressors.percentage}%)</p>
          {kpis.aggressors.topAggressors.length > 0 && (
            <div className="mt-2">
              <p className="text-xs font-medium">Top Agresores:</p>
              {kpis.aggressors.topAggressors.map((aggressor, index) => (
                <p key={aggressor.id} className="text-xs">
                  {index + 1}. {aggressor.name} ({aggressor.nominations} nom.)
                </p>
              ))}
            </div>
          )}
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="font-semibold text-blue-600">Víctimas</h3>
          <p className="text-2xl font-bold">{kpis.victims.value}</p>
          <p className="text-sm text-gray-600">({kpis.victims.percentage}%)</p>
          {kpis.victims.topVictims.length > 0 && (
            <div className="mt-2">
              <p className="text-xs font-medium">Top Víctimas:</p>
              {kpis.victims.topVictims.map((victim, index) => (
                <p key={victim.id} className="text-xs">
                  {index + 1}. {victim.name} ({victim.nominations} nom.)
                </p>
              ))}
            </div>
          )}
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="font-semibold text-green-600">Cohesión Grupal</h3>
          <p className="text-2xl font-bold">{kpis.groupCohesion.value}%</p>
          <p className="text-sm text-gray-600">Nivel: {kpis.groupCohesion.level}</p>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="font-semibold text-purple-600">Seguridad Percibida</h3>
          <p className="text-2xl font-bold">{kpis.perceivedSafety.value}%</p>
          <p className="text-sm text-gray-600">Nivel: {kpis.perceivedSafety.level}</p>
        </div>
      </div>
    </div>
  );
};

export default TestBullsMetrics;
