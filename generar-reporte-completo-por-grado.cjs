const fs = require('fs');
const path = require('path');

console.log('Iniciando generación de reporte completo por grado con respuestas...');

// Leer archivos CSV
function leerCSV(rutaArchivo) {
  const contenido = fs.readFileSync(rutaArchivo, 'utf8');
  const lineas = contenido.trim().split('\n');
  const encabezados = lineas[0].split(',');
  
  return lineas.slice(1).map(linea => {
    const valores = [];
    let valorActual = '';
    let dentroComillas = false;
    
    for (let i = 0; i < linea.length; i++) {
      const char = linea[i];
      if (char === '"') {
        dentroComillas = !dentroComillas;
      } else if (char === ',' && !dentroComillas) {
        valores.push(valorActual.trim());
        valorActual = '';
      } else {
        valorActual += char;
      }
    }
    valores.push(valorActual.trim());
    
    const objeto = {};
    encabezados.forEach((encabezado, index) => {
      objeto[encabezado.trim()] = valores[index] || '';
    });
    return objeto;
  });
}

// Mapeo de preguntas
const mapeoPreguntas = {
  'd90ddd09-3878-4efc-9059-7279570157bc': { numero: 'P1', titulo: '¿Con qué compañeros/as te gusta estar más?', tipo: 'sociometrica' },
  '47b56067-0c8c-4565-b645-80348852907f': { numero: 'P2', titulo: '¿Con qué compañeros/as te gusta estar menos?', tipo: 'sociometrica' },
  'dae67e87-db3e-4637-ace1-f1148f1d7d69': { numero: 'P3', titulo: '¿Quiénes suelen intimidar o maltratar a otros compañeros/as?', tipo: 'sociometrica' },
  'd2888d67-9878-4cdf-8a58-592c251c1cb6': { numero: 'P4', titulo: '¿Quiénes suelen ser víctimas de intimidación o maltrato?', tipo: 'sociometrica' },
  '0489df06-c6e7-48ec-8fb0-49469ec541ae': { numero: 'P5', titulo: '¿Qué tipo de maltrato o intimidación es más frecuente?', tipo: 'sociometrica' },
  '775af389-a84d-4f40-8fb9-7b94cbea5498': { numero: 'P6', titulo: '¿Dónde suelen ocurrir estas situaciones?', tipo: 'sociometrica' },
  '8e0be6b5-fa0b-4215-bc60-0c91065bbaa9': { numero: 'P7', titulo: '¿Con qué frecuencia ocurren las agresiones?', tipo: 'sociometrica' },
  'eec6513e-f5b7-45b1-b21d-4e4551b3e504': { numero: 'P8', titulo: '¿Crees que estas situaciones encierran gravedad?', tipo: 'sociometrica' },
  '8074fef6-4952-4857-b97c-08a1a8805522': { numero: 'P9', titulo: '¿Te encuentras seguro/a en el centro escolar?', tipo: 'sociometrica' },
  '00000000-0000-0000-0000-000000000010': { numero: 'P10', titulo: 'Pregunta sociométrica 10', tipo: 'sociometrica' },
  '00000000-0000-0000-0000-000000000011': { numero: 'P11', titulo: 'Pregunta abierta 11', tipo: 'abierta' },
  '00000000-0000-0000-0000-000000000012': { numero: 'P12', titulo: 'Pregunta abierta 12', tipo: 'abierta' },
  '00000000-0000-0000-0000-000000000013': { numero: 'P13', titulo: 'Pregunta abierta 13', tipo: 'abierta' },
  '00000000-0000-0000-0000-000000000014': { numero: 'P14', titulo: 'Pregunta abierta 14', tipo: 'abierta' },
  '00000000-0000-0000-0000-000000000015': { numero: 'P15', titulo: 'Pregunta abierta 15', tipo: 'abierta' }
};

// Función para convertir UUIDs a nombres
function convertirUuidsANombres(respuestaTexto, mapeoEstudiantes) {
  if (!respuestaTexto || respuestaTexto === 'Sin respuesta') {
    return 'Sin respuesta';
  }
  
  // Verificar si es un array JSON de UUIDs
  if (respuestaTexto.startsWith('[') && respuestaTexto.endsWith(']')) {
    try {
      const uuids = JSON.parse(respuestaTexto);
      if (Array.isArray(uuids)) {
        return uuids.map(uuid => {
          const estudiante = mapeoEstudiantes[uuid];
          return estudiante ? `${estudiante.nombre_completo} (${estudiante.codigo})` : uuid;
        });
      }
    } catch (e) {
      // Si no se puede parsear, devolver como texto
      return respuestaTexto;
    }
  }
  
  // Verificar si es un objeto JSON con selected/other
  if (respuestaTexto.startsWith('{') && respuestaTexto.includes('selected')) {
    try {
      const respuestaObj = JSON.parse(respuestaTexto);
      if (respuestaObj.selected) {
        let resultado = respuestaObj.selected.join(', ');
        if (respuestaObj.other && respuestaObj.other.trim()) {
          resultado += ` - Otro: ${respuestaObj.other}`;
        }
        return resultado;
      }
    } catch (e) {
      return respuestaTexto;
    }
  }
  
  // Si no es ninguno de los casos anteriores, devolver como texto
  return respuestaTexto;
}

try {
  // Leer datos de estudiantes
  const estudiantes = leerCSV(path.join(__dirname, 'respuestas bulls-s', 'estudiantes_rows.csv'));
  console.log(`Estudiantes cargados: ${estudiantes.length}`);
  
  // Leer respuestas
  const respuestas = leerCSV(path.join(__dirname, 'respuestas bulls-s', 'respuestas_rows.csv'));
  console.log(`Respuestas cargadas: ${respuestas.length}`);
  
  // Crear mapeo de estudiantes UUID -> datos
  const mapeoEstudiantes = {};
  estudiantes.forEach(est => {
    mapeoEstudiantes[est.id] = {
      nombre_completo: `${est.nombre_estudiante} ${est.apellido_estudiante}`,
      codigo: est.codigo_anonimizado,
      grado: est.grado,
      genero: est.genero,
      edad: parseInt(est.edad) || 0
    };
  });
  
  // Organizar respuestas por estudiante y pregunta
  const respuestasPorEstudiante = {};
  
  respuestas.forEach(resp => {
    const estudianteId = resp.estudiante_id;
    const preguntaId = resp.pregunta_id;
    

    if (!respuestasPorEstudiante[estudianteId]) {
      respuestasPorEstudiante[estudianteId] = {};
    }
    
    // Convertir respuesta usando la función especializada
    let respuestaProcesada;
    const pregunta = mapeoPreguntas[preguntaId];
    if (pregunta && pregunta.tipo === 'sociometrica') {
      // Verificar si respuesta_texto es un string que contiene un array JSON
      if (resp.respuesta_texto && resp.respuesta_texto.startsWith('[') && resp.respuesta_texto.endsWith(']')) {
        try {
          let arrayText = resp.respuesta_texto;
          // Si los UUIDs no tienen comillas, agregarlas
          if (!arrayText.includes('"')) {
            arrayText = arrayText.replace(/([a-f0-9-]{36})/g, '"$1"');
          }
          const uuidsArray = JSON.parse(arrayText);
          if (Array.isArray(uuidsArray)) {
            respuestaProcesada = uuidsArray.map(uuid => {
              const estudiante = mapeoEstudiantes[uuid];
              return estudiante ? `${estudiante.nombre_completo} (${estudiante.codigo})` : uuid;
            });
          } else {
            respuestaProcesada = 'Sin respuesta';
          }
        } catch (e) {
          // Error parsing sociometric response
          respuestaProcesada = 'Sin respuesta';
        }
      } else {
        respuestaProcesada = convertirUuidsANombres(resp.respuesta_texto, mapeoEstudiantes);
      }
    } else {
      respuestaProcesada = resp.respuesta_texto || 'Sin respuesta';
    }
    

    respuestasPorEstudiante[estudianteId][preguntaId] = respuestaProcesada;
  });
  
  // Organizar por grados
  const grados = {
    '6B': [],
    '8A': [],
    '8B': []
  };
  
  // Clasificar estudiantes por grado
  Object.keys(mapeoEstudiantes).forEach(uuid => {
    const estudiante = mapeoEstudiantes[uuid];
    const grado = estudiante.grado;
    
    if (grados[grado]) {
      const respuestasEstudiante = respuestasPorEstudiante[uuid] || {};
      
      // Crear objeto de preguntas con respuestas
      const preguntas = {};
      Object.keys(mapeoPreguntas).forEach(preguntaId => {
        const pregunta = mapeoPreguntas[preguntaId];
        let respuesta = respuestasEstudiante[preguntaId] || 'Sin respuesta';
        
        // Si es una respuesta sociométrica y es un array, convertir a string para JSON
        if (pregunta.tipo === 'sociometrica' && Array.isArray(respuesta)) {
          respuesta = respuesta.join('; ');
        }
        
        preguntas[pregunta.numero] = {
          titulo: pregunta.titulo,
          tipo: pregunta.tipo,
          respuesta: respuesta
        };
      });
      
      grados[grado].push({
        uuid: uuid,
        nombre_completo: estudiante.nombre_completo,
        codigo: estudiante.codigo,
        genero: estudiante.genero,
        edad: estudiante.edad,
        preguntas: preguntas
      });
    }
  });
  
  // Ordenar estudiantes por código dentro de cada grado
  Object.keys(grados).forEach(grado => {
    grados[grado].sort((a, b) => a.codigo.localeCompare(b.codigo));
  });
  
  // Crear reporte final
  const reporte = {
    metadata: {
      fecha_generacion: new Date().toISOString(),
      titulo: 'Reporte Completo por Grado con Respuestas',
      descripcion: 'Estudiantes organizados por grado con mapeo UUID->EST### y todas sus respuestas',
      total_estudiantes: estudiantes.length,
      total_respuestas: respuestas.length
    },
    grados: []
  };
  
  ['6B', '8A', '8B'].forEach(gradoNombre => {
    const estudiantesGrado = grados[gradoNombre];
    const estudiantesConRespuestas = estudiantesGrado.filter(est => 
      Object.values(est.preguntas).some(p => p.respuesta !== 'Sin respuesta')
    );
    const estudiantesSinRespuestas = estudiantesGrado.filter(est => 
      Object.values(est.preguntas).every(p => p.respuesta === 'Sin respuesta')
    );
    
    reporte.grados.push({
      grado: gradoNombre,
      total_estudiantes: estudiantesGrado.length,
      estudiantes_con_respuestas: estudiantesConRespuestas.length,
      estudiantes_sin_respuestas: estudiantesSinRespuestas.length,
      estudiantes: estudiantesGrado
    });
  });
  
  // Generar archivos
  const fechaHoy = new Date().toISOString().split('T')[0];
  const nombreBase = `reporte-completo-grado-${fechaHoy}`;
  
  // JSON
  const archivoJson = path.join(__dirname, `${nombreBase}.json`);
  fs.writeFileSync(archivoJson, JSON.stringify(reporte, null, 2), 'utf8');
  
  // CSV
  const archivoCSV = path.join(__dirname, `${nombreBase}.csv`);
  let csvContent = 'Grado,UUID,Codigo,Nombre_Completo,Genero,Edad';
  
  // Agregar columnas para cada pregunta
  Object.keys(mapeoPreguntas).forEach(preguntaId => {
    const pregunta = mapeoPreguntas[preguntaId];
    if (pregunta.tipo === 'sociometrica') {
      csvContent += `,${pregunta.numero}_R1,${pregunta.numero}_R2,${pregunta.numero}_R3`;
    } else {
      csvContent += `,${pregunta.numero}`;
    }
  });
  csvContent += '\n';
  
  reporte.grados.forEach(grado => {
    grado.estudiantes.forEach(estudiante => {
      let fila = `${grado.grado},${estudiante.uuid},${estudiante.codigo},"${estudiante.nombre_completo}",${estudiante.genero},${estudiante.edad}`;
      
      Object.keys(mapeoPreguntas).forEach(preguntaId => {
        const pregunta = mapeoPreguntas[preguntaId];
        // Obtener la respuesta original (puede ser array) desde respuestasPorEstudiante
        const respuestaOriginal = respuestasPorEstudiante[estudiante.uuid] ? respuestasPorEstudiante[estudiante.uuid][preguntaId] : 'Sin respuesta';
        
        if (pregunta.tipo === 'sociometrica' && Array.isArray(respuestaOriginal)) {
          // Respuesta sociométrica con 3 selecciones
          fila += `,"${respuestaOriginal[0] || 'Sin respuesta'}","${respuestaOriginal[1] || 'Sin respuesta'}","${respuestaOriginal[2] || 'Sin respuesta'}"`;
        } else if (pregunta.tipo === 'sociometrica') {
          // Respuesta sociométrica pero no es array
          const respuesta = respuestaOriginal || 'Sin respuesta';
          fila += `,"${respuesta}","Sin respuesta","Sin respuesta"`;
        } else {
          // Respuesta abierta
          const respuesta = respuestaOriginal || 'Sin respuesta';
          fila += `,"${respuesta}"`;
        }
      });
      
      csvContent += fila + '\n';
    });
  });
  
  fs.writeFileSync(archivoCSV, csvContent, 'utf8');
  
  // TXT resumen
  const archivoTxt = path.join(__dirname, `resumen-completo-grado-${fechaHoy}.txt`);
  let txtContent = `REPORTE COMPLETO POR GRADO CON RESPUESTAS\n`;
  txtContent += `Fecha de generación: ${new Date().toLocaleString('es-ES')}\n\n`;
  
  txtContent += `RESUMEN GENERAL:\n`;
  txtContent += `- Total de estudiantes: ${reporte.metadata.total_estudiantes}\n`;
  txtContent += `- Total de respuestas: ${reporte.metadata.total_respuestas}\n\n`;
  
  txtContent += `MAPEO UUID -> CÓDIGO EST###:\n`;
  reporte.grados.forEach(grado => {
    txtContent += `\nGRADO ${grado.grado}:\n`;
    txtContent += `- Total estudiantes: ${grado.total_estudiantes}\n`;
    txtContent += `- Con respuestas: ${grado.estudiantes_con_respuestas}\n`;
    txtContent += `- Sin respuestas: ${grado.estudiantes_sin_respuestas}\n\n`;
    
    grado.estudiantes.forEach(estudiante => {
      txtContent += `  ${estudiante.uuid} -> ${estudiante.codigo} - ${estudiante.nombre_completo}\n`;
    });
  });
  
  txtContent += `\n\nPREGUNTAS INCLUIDAS:\n`;
  Object.keys(mapeoPreguntas).forEach(preguntaId => {
    const pregunta = mapeoPreguntas[preguntaId];
    txtContent += `${pregunta.numero}: ${pregunta.titulo} (${pregunta.tipo})\n`;
  });
  
  fs.writeFileSync(archivoTxt, txtContent, 'utf8');
  
  console.log('\n=== REPORTE GENERADO EXITOSAMENTE ===');
  console.log('Archivos generados:');
  console.log(`- JSON: ${archivoJson}`);
  console.log(`- CSV: ${archivoCSV}`);
  console.log(`- TXT: ${archivoTxt}`);
  
  console.log('\n=== RESUMEN POR GRADOS ===');
  reporte.grados.forEach(grado => {
    console.log(`${grado.grado}: ${grado.total_estudiantes} estudiantes (${grado.estudiantes_con_respuestas} con respuestas, ${grado.estudiantes_sin_respuestas} sin respuestas)`);
  });
  
  console.log(`\nTotal general: ${reporte.metadata.total_estudiantes} estudiantes`);
  console.log(`Total respuestas procesadas: ${reporte.metadata.total_respuestas}`);
  
} catch (error) {
  console.error('Error al generar el reporte:', error);
  process.exit(1);
}