/**
 * Script para probar las métricas de convivencia escolar
 * Verifica que el servicio funcione correctamente con datos reales
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { readFileSync } from 'fs';

// Obtener el directorio actual
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Cargar variables de entorno desde el archivo .env
try {
  const envPath = join(__dirname, '..', '.env');
  const envContent = readFileSync(envPath, 'utf8');
  const envVars = {};
  
  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key && value) {
      envVars[key.trim()] = value.trim();
    }
  });
  
  // Asignar variables de entorno
  Object.assign(process.env, envVars);
} catch (error) {
  console.log('⚠️ No se pudo cargar el archivo .env, usando variables del sistema');
}

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔧 Configuración:');
console.log(`   URL: ${supabaseUrl ? supabaseUrl.substring(0, 30) + '...' : 'No configurada'}`);
console.log(`   Key: ${supabaseKey ? supabaseKey.substring(0, 20) + '...' : 'No configurada'}`);

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Error: Variables de entorno de Supabase no encontradas');
  console.log('Asegúrate de que VITE_SUPABASE_URL y VITE_SUPABASE_ANON_KEY estén configuradas');
  process.exit(1);
}

// Configurar fetch global para Node.js
if (typeof globalThis.fetch === 'undefined') {
  const { default: fetch } = await import('node-fetch');
  globalThis.fetch = fetch;
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Simular el servicio de convivencia escolar
 */
class ConvivenciaEscolarServiceTest {
  async obtenerMetricasConvivencia(institucionId, grupoId) {
    try {
      console.log('🔍 Obteniendo datos de la vista de indicadores de bullying...');
      
      // Obtener datos de la vista de indicadores de bullying
      let query = supabase
        .from('view_dashboard_bullying_indicators')
        .select('*');
      
      if (grupoId) {
        query = query.eq('grupo_id', grupoId);
        console.log(`📊 Filtrando por grupo: ${grupoId}`);
      } else if (institucionId) {
        query = query.eq('institucion_id', institucionId);
        console.log(`🏫 Filtrando por institución: ${institucionId}`);
      }
      
      const { data: indicadores, error } = await query;
      
      if (error) {
        console.error('❌ Error obteniendo indicadores:', error);
        return this.obtenerMetricasPorDefecto();
      }
      
      if (!indicadores || indicadores.length === 0) {
        console.log('⚠️ No hay datos de indicadores disponibles');
        return this.obtenerMetricasPorDefecto();
      }
      
      console.log(`✅ Datos obtenidos: ${indicadores.length} registros`);
      
      // Mostrar muestra de datos
      console.log('\n📋 Muestra de datos obtenidos:');
      indicadores.slice(0, 3).forEach((ind, index) => {
        console.log(`${index + 1}. ${ind.nombre_estudiante} ${ind.apellido_estudiante}:`);
        console.log(`   - Rol de bullying: ${ind.bullying_role || 'No definido'}`);
        console.log(`   - Estatus sociométrico: ${ind.sociometric_status || 'No definido'}`);
        console.log(`   - Nominaciones positivas: ${ind.positive_nominations || 0}`);
        console.log(`   - Nominaciones negativas: ${ind.negative_nominations || 0}`);
      });
      
      // Calcular métricas
      const tasasIncidencia = this.calcularTasasIncidencia(indicadores);
      const estatusSociometrico = this.calcularEstatusSociometrico(indicadores);
      const cohesionGrupal = await this.calcularCohesionGrupal(indicadores, grupoId);
      const dinamicasAcoso = this.calcularDinamicasAcoso(indicadores);
      
      return {
        tasasIncidencia,
        estatusSociometrico,
        cohesionGrupal,
        dinamicasAcoso,
        fechaCalculo: new Date().toISOString(),
        grupoId,
        institucionId
      };
      
    } catch (error) {
      console.error('❌ Error calculando métricas de convivencia:', error);
      return this.obtenerMetricasPorDefecto();
    }
  }
  
  calcularTasasIncidencia(indicadores) {
    const totalEstudiantes = indicadores.length;
    
    const conteos = indicadores.reduce((acc, ind) => {
      const role = ind.bullying_role || 'Observer';
      acc[role] = (acc[role] || 0) + 1;
      return acc;
    }, {});
    
    const agresores = conteos['Bully'] || 0;
    const victimas = conteos['Victim'] || 0;
    const agresoresVictimas = conteos['Bully-Victim'] || 0;
    const observadores = conteos['Observer'] || 0;
    
    console.log('\n📊 Tasas de Incidencia:');
    console.log(`   Total estudiantes: ${totalEstudiantes}`);
    console.log(`   Agresores: ${agresores} (${totalEstudiantes > 0 ? ((agresores / totalEstudiantes) * 100).toFixed(1) : 0}%)`);
    console.log(`   Víctimas: ${victimas} (${totalEstudiantes > 0 ? ((victimas / totalEstudiantes) * 100).toFixed(1) : 0}%)`);
    console.log(`   Agresores-Víctimas: ${agresoresVictimas} (${totalEstudiantes > 0 ? ((agresoresVictimas / totalEstudiantes) * 100).toFixed(1) : 0}%)`);
    console.log(`   Observadores: ${observadores} (${totalEstudiantes > 0 ? ((observadores / totalEstudiantes) * 100).toFixed(1) : 0}%)`);
    
    return {
      agresores,
      victimas,
      agresoresVictimas,
      observadores,
      totalEstudiantes,
      porcentajes: {
        agresores: totalEstudiantes > 0 ? (agresores / totalEstudiantes) * 100 : 0,
        victimas: totalEstudiantes > 0 ? (victimas / totalEstudiantes) * 100 : 0,
        agresoresVictimas: totalEstudiantes > 0 ? (agresoresVictimas / totalEstudiantes) * 100 : 0,
        observadores: totalEstudiantes > 0 ? (observadores / totalEstudiantes) * 100 : 0
      }
    };
  }
  
  calcularEstatusSociometrico(indicadores) {
    const totalEstudiantes = indicadores.length;
    
    const conteos = indicadores.reduce((acc, ind) => {
      const status = ind.sociometric_status || 'Average';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});
    
    const populares = conteos['Popular'] || 0;
    const promedio = conteos['Average'] || 0;
    const aislados = conteos['Isolated'] || 0;
    const rechazados = conteos['Rejected'] || 0;
    const controvertidos = conteos['Controversial'] || 0;
    
    console.log('\n👥 Estatus Sociométrico:');
    console.log(`   Populares: ${populares} (${totalEstudiantes > 0 ? ((populares / totalEstudiantes) * 100).toFixed(1) : 0}%)`);
    console.log(`   Promedio: ${promedio} (${totalEstudiantes > 0 ? ((promedio / totalEstudiantes) * 100).toFixed(1) : 0}%)`);
    console.log(`   Aislados: ${aislados} (${totalEstudiantes > 0 ? ((aislados / totalEstudiantes) * 100).toFixed(1) : 0}%)`);
    console.log(`   Rechazados: ${rechazados} (${totalEstudiantes > 0 ? ((rechazados / totalEstudiantes) * 100).toFixed(1) : 0}%)`);
    console.log(`   Controvertidos: ${controvertidos} (${totalEstudiantes > 0 ? ((controvertidos / totalEstudiantes) * 100).toFixed(1) : 0}%)`);
    
    return {
      populares,
      promedio,
      aislados,
      rechazados,
      controvertidos,
      totalEstudiantes,
      porcentajes: {
        populares: totalEstudiantes > 0 ? (populares / totalEstudiantes) * 100 : 0,
        promedio: totalEstudiantes > 0 ? (promedio / totalEstudiantes) * 100 : 0,
        aislados: totalEstudiantes > 0 ? (aislados / totalEstudiantes) * 100 : 0,
        rechazados: totalEstudiantes > 0 ? (rechazados / totalEstudiantes) * 100 : 0,
        controvertidos: totalEstudiantes > 0 ? (controvertidos / totalEstudiantes) * 100 : 0
      }
    };
  }
  
  async calcularCohesionGrupal(indicadores, grupoId) {
    const nominacionesPositivas = indicadores.map(ind => ind.positive_nominations || 0);
    const promedioNominaciones = nominacionesPositivas.length > 0 
      ? nominacionesPositivas.reduce((sum, val) => sum + val, 0) / nominacionesPositivas.length 
      : 0;
    
    const varianza = nominacionesPositivas.length > 0
      ? nominacionesPositivas.reduce((sum, val) => sum + Math.pow(val - promedioNominaciones, 2), 0) / nominacionesPositivas.length
      : 0;
    const desviacion = Math.sqrt(varianza);
    
    let categoria, valor;
    
    if (promedioNominaciones > 4 && desviacion < 2) {
      categoria = 'Alta';
      valor = 85;
    } else if (promedioNominaciones > 2 && desviacion < 3) {
      categoria = 'Media';
      valor = 65;
    } else {
      categoria = 'Baja';
      valor = 35;
    }
    
    const totalPosiblesRelaciones = indicadores.length * (indicadores.length - 1);
    const relacionesActuales = nominacionesPositivas.reduce((sum, val) => sum + val, 0);
    const densidadRelaciones = totalPosiblesRelaciones > 0 
      ? (relacionesActuales / totalPosiblesRelaciones) * 100 
      : 0;
    
    const reciprocidad = Math.min(densidadRelaciones * 1.2, 100);
    
    console.log('\n❤️ Cohesión Grupal:');
    console.log(`   Promedio nominaciones positivas: ${promedioNominaciones.toFixed(2)}`);
    console.log(`   Desviación estándar: ${desviacion.toFixed(2)}`);
    console.log(`   Categoría: ${categoria}`);
    console.log(`   Valor: ${valor}%`);
    console.log(`   Densidad de relaciones: ${densidadRelaciones.toFixed(2)}%`);
    console.log(`   Reciprocidad: ${reciprocidad.toFixed(2)}%`);
    
    return {
      valor,
      categoria,
      promedioNominacionesPositivas: promedioNominaciones,
      densidadRelaciones,
      reciprocidad
    };
  }
  
  calcularDinamicasAcoso(indicadores) {
    const conteoRoles = indicadores.reduce((acc, ind) => {
      const role = ind.bullying_role || 'Observer';
      acc[role] = (acc[role] || 0) + 1;
      return acc;
    }, {});
    
    const agresores = conteoRoles['Bully'] || 0;
    const victimas = conteoRoles['Victim'] || 0;
    const agresoresVictimas = conteoRoles['Bully-Victim'] || 0;
    
    let predominante;
    
    if (agresoresVictimas > (agresores + victimas) / 2) {
      predominante = 'Ciclos de Violencia';
    } else if (agresores > victimas) {
      predominante = 'Agresión Dominante';
    } else if (victimas > agresores) {
      predominante = 'Victimización Prevalente';
    } else {
      predominante = 'Ambiente Observacional';
    }
    
    const totalIncidentes = agresores + victimas + agresoresVictimas;
    const patrones = {
      agresionFisica: Math.round(totalIncidentes * 0.3),
      agresionVerbal: Math.round(totalIncidentes * 0.4),
      exclusionSocial: Math.round(totalIncidentes * 0.2),
      ciberacoso: Math.round(totalIncidentes * 0.1)
    };
    
    console.log('\n🎯 Dinámicas de Acoso:');
    console.log(`   Dinámica predominante: ${predominante}`);
    console.log(`   Total incidentes: ${totalIncidentes}`);
    console.log(`   Patrones:`);
    console.log(`     - Agresión física: ${patrones.agresionFisica}`);
    console.log(`     - Agresión verbal: ${patrones.agresionVerbal}`);
    console.log(`     - Exclusión social: ${patrones.exclusionSocial}`);
    console.log(`     - Ciberacoso: ${patrones.ciberacoso}`);
    
    return {
      predominante,
      patrones,
      lugaresComunes: {
        'Aula': 35,
        'Patio': 25,
        'Pasillos': 20,
        'Baños': 10,
        'Comedor': 10
      },
      frecuenciaPercibida: {
        'Diariamente': 15,
        'Varias veces por semana': 25,
        'Una vez por semana': 30,
        'Ocasionalmente': 20,
        'Nunca': 10
      }
    };
  }
  
  obtenerMetricasPorDefecto() {
    console.log('⚠️ Usando métricas por defecto (sin datos)');
    return {
      tasasIncidencia: {
        agresores: 0,
        victimas: 0,
        agresoresVictimas: 0,
        observadores: 0,
        totalEstudiantes: 0,
        porcentajes: { agresores: 0, victimas: 0, agresoresVictimas: 0, observadores: 0 }
      },
      estatusSociometrico: {
        populares: 0, promedio: 0, aislados: 0, rechazados: 0, controvertidos: 0,
        totalEstudiantes: 0,
        porcentajes: { populares: 0, promedio: 0, aislados: 0, rechazados: 0, controvertidos: 0 }
      },
      cohesionGrupal: {
        valor: 0, categoria: 'Baja', promedioNominacionesPositivas: 0,
        densidadRelaciones: 0, reciprocidad: 0
      },
      dinamicasAcoso: {
        predominante: 'Ambiente Observacional',
        patrones: { agresionFisica: 0, agresionVerbal: 0, exclusionSocial: 0, ciberacoso: 0 },
        lugaresComunes: {}, frecuenciaPercibida: {}
      },
      fechaCalculo: new Date().toISOString()
    };
  }
  
  async obtenerResumenEjecutivo(institucionId, grupoId) {
    const metricas = await this.obtenerMetricasConvivencia(institucionId, grupoId);
    
    const porcentajeAgresion = metricas.tasasIncidencia.porcentajes.agresores + 
                              metricas.tasasIncidencia.porcentajes.agresoresVictimas;
    const porcentajeVictimizacion = metricas.tasasIncidencia.porcentajes.victimas + 
                                   metricas.tasasIncidencia.porcentajes.agresoresVictimas;
    const porcentajeAislamiento = metricas.estatusSociometrico.porcentajes.aislados + 
                                 metricas.estatusSociometrico.porcentajes.rechazados;
    
    let nivelRiesgo;
    
    if (porcentajeAgresion > 20 || porcentajeVictimizacion > 25) {
      nivelRiesgo = 'Crítico';
    } else if (porcentajeAgresion > 10 || porcentajeVictimizacion > 15 || porcentajeAislamiento > 30) {
      nivelRiesgo = 'Alto';
    } else if (porcentajeAgresion > 5 || porcentajeVictimizacion > 8 || porcentajeAislamiento > 20) {
      nivelRiesgo = 'Medio';
    } else {
      nivelRiesgo = 'Bajo';
    }
    
    console.log('\n🚨 Resumen Ejecutivo:');
    console.log(`   Nivel de riesgo: ${nivelRiesgo}`);
    console.log(`   Porcentaje agresión: ${porcentajeAgresion.toFixed(1)}%`);
    console.log(`   Porcentaje victimización: ${porcentajeVictimizacion.toFixed(1)}%`);
    console.log(`   Porcentaje aislamiento: ${porcentajeAislamiento.toFixed(1)}%`);
    
    return {
      nivelRiesgo,
      indicadoresClave: [
        `Cohesión grupal: ${metricas.cohesionGrupal.categoria} (${metricas.cohesionGrupal.valor}%)`,
        `Dinámica predominante: ${metricas.dinamicasAcoso.predominante}`,
        `Estudiantes en riesgo: ${(porcentajeAgresion + porcentajeVictimizacion).toFixed(1)}%`,
        `Aislamiento social: ${porcentajeAislamiento.toFixed(1)}%`
      ],
      recomendaciones: [],
      alertas: []
    };
  }
}

async function main() {
  console.log('🚀 Iniciando prueba de métricas de convivencia escolar\n');
  
  const service = new ConvivenciaEscolarServiceTest();
  
  try {
    // Verificar conexión a Supabase
    console.log('🔗 Verificando conexión a Supabase...');
    const { data, error } = await supabase.from('estudiantes').select('count').limit(1);
    
    if (error) {
      console.error('❌ Error de conexión a Supabase:', error.message);
      return;
    }
    
    console.log('✅ Conexión a Supabase exitosa\n');
    
    // Obtener lista de grupos disponibles
    console.log('📋 Obteniendo grupos disponibles...');
    const { data: grupos, error: gruposError } = await supabase
      .from('grupos')
      .select('id, nombre, grado')
      .limit(5);
    
    if (gruposError) {
      console.error('❌ Error obteniendo grupos:', gruposError.message);
      return;
    }
    
    if (grupos && grupos.length > 0) {
      console.log('✅ Grupos encontrados:');
      grupos.forEach((grupo, index) => {
        console.log(`   ${index + 1}. ${grupo.nombre} (${grupo.grado}) - ID: ${grupo.id}`);
      });
      
      // Probar con el primer grupo
      const grupoTest = grupos[0];
      console.log(`\n🧪 Probando métricas para el grupo: ${grupoTest.nombre}\n`);
      
      const metricas = await service.obtenerMetricasConvivencia(null, grupoTest.id);
      const resumen = await service.obtenerResumenEjecutivo(null, grupoTest.id);
      
      console.log('\n✅ Prueba completada exitosamente!');
      console.log('\n📊 Resumen de métricas calculadas:');
      console.log(`   - Fecha de cálculo: ${new Date(metricas.fechaCalculo).toLocaleString('es-ES')}`);
      console.log(`   - Grupo ID: ${metricas.grupoId}`);
      console.log(`   - Total estudiantes analizados: ${metricas.tasasIncidencia.totalEstudiantes}`);
      
    } else {
      console.log('⚠️ No se encontraron grupos. Probando métricas generales...');
      
      const metricas = await service.obtenerMetricasConvivencia();
      const resumen = await service.obtenerResumenEjecutivo();
      
      console.log('\n✅ Prueba completada con métricas generales!');
    }
    
  } catch (error) {
    console.error('❌ Error durante la prueba:', error);
  }
}

// Ejecutar la prueba
main().catch(console.error);