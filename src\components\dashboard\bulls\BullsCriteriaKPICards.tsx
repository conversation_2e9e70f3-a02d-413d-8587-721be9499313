import React from 'react';
import { Card, CardContent, Card<PERSON>eader, CardTitle } from '../../ui/card';
import { Badge } from '../../ui/badge';
import { AlertTriangle, Users, Shield, Target, TrendingUp, AlertCircle } from 'lucide-react';
import { useBullsCriteriaStats } from '../../../hooks/dashboard/useBullsCriteria';

interface BullsCriteriaKPICardsProps {
  grupoId?: string;
  className?: string;
}

const BullsCriteriaKPICards: React.FC<BullsCriteriaKPICardsProps> = ({ 
  grupoId, 
  className = '' 
}) => {
  const { stats, loading, error } = useBullsCriteriaStats(grupoId);

  if (loading) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 ${className}`}>
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card className={`border-red-200 bg-red-50 ${className}`}>
        <CardContent className="p-6">
          <div className="flex items-center gap-2 text-red-600">
            <AlertCircle className="w-5 h-5" />
            <span>Error al cargar métricas BULL-S: {error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const kpiCards = [
    {
      title: 'Total Estudiantes',
      value: stats.totalEstudiantes.toLocaleString(),
      icon: <Users className="w-5 h-5" />,
      description: 'Estudiantes evaluados',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      title: 'En Riesgo',
      value: stats.totalEnRiesgo.toLocaleString(),
      icon: <AlertTriangle className="w-5 h-5" />,
      description: `${stats.porcentajeRiesgo}% del total`,
      color: stats.porcentajeRiesgo >= 25 ? 'text-red-600' : 'text-orange-600',
      bgColor: stats.porcentajeRiesgo >= 25 ? 'bg-red-50' : 'bg-orange-50',
      borderColor: stats.porcentajeRiesgo >= 25 ? 'border-red-200' : 'border-orange-200',
      badge: stats.porcentajeRiesgo >= 25 ? (
        <Badge className="bg-red-100 text-red-800 border-red-200 text-xs">
          CRÍTICO
        </Badge>
      ) : null
    },
    {
      title: 'Agresores',
      value: stats.totalAgresores.toLocaleString(),
      icon: <Target className="w-5 h-5" />,
      description: 'Rol agresor identificado',
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200'
    },
    {
      title: 'Víctimas',
      value: stats.totalVictimas.toLocaleString(),
      icon: <Shield className="w-5 h-5" />,
      description: 'Rol víctima identificado',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      title: 'Agresor-Víctima',
      value: stats.totalAgresorVictima.toLocaleString(),
      icon: <AlertTriangle className="w-5 h-5" />,
      description: 'Doble rol identificado',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200'
    },
    {
      title: 'Grupos Críticos',
      value: stats.gruposCriticos.toLocaleString(),
      icon: <TrendingUp className="w-5 h-5" />,
      description: '≥25% estudiantes en riesgo',
      color: stats.gruposCriticos > 0 ? 'text-red-600' : 'text-green-600',
      bgColor: stats.gruposCriticos > 0 ? 'bg-red-50' : 'bg-green-50',
      borderColor: stats.gruposCriticos > 0 ? 'border-red-200' : 'border-green-200',
      badge: stats.gruposCriticos > 0 ? (
        <Badge className="bg-red-100 text-red-800 border-red-200 text-xs">
          ALERTA
        </Badge>
      ) : (
        <Badge className="bg-green-100 text-green-800 border-green-200 text-xs">
          NORMAL
        </Badge>
      )
    }
  ];

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 ${className}`}>
      {kpiCards.map((kpi, index) => (
        <Card 
          key={index} 
          className={`${kpi.borderColor} ${kpi.bgColor} transition-all duration-200 hover:shadow-md`}
        >
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-700 flex items-center justify-between">
              <span>{kpi.title}</span>
              {kpi.badge}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className={`text-2xl font-bold ${kpi.color}`}>
                  {kpi.value}
                </div>
                <p className="text-xs text-gray-600 mt-1">
                  {kpi.description}
                </p>
              </div>
              <div className={`${kpi.color} opacity-80`}>
                {kpi.icon}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default BullsCriteriaKPICards;