import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Cargar variables de entorno
dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

console.log('🔍 VERIFICANDO ESTRUCTURA DE BASE DE DATOS');
console.log('=' .repeat(60));

async function verificarTablas() {
  try {
    // Intentar obtener información del esquema
    console.log('\n📊 Verificando tablas disponibles...');
    
    const tablas = [
      'answers',
      'nominations', 
      'students',
      'sociomatrix',
      'respuestas',
      'preguntas',
      'estudiantes',
      'grupos'
    ];
    
    for (const tabla of tablas) {
      try {
        const { data, error, count } = await supabase
          .from(tabla)
          .select('*', { count: 'exact', head: true });
        
        if (error) {
          console.log(`❌ ${tabla}: ${error.message}`);
        } else {
          console.log(`✅ ${tabla}: tabla encontrada (${count || 0} registros)`);
        }
      } catch (err) {
        console.log(`❌ ${tabla}: error de conexión`);
      }
    }
    
    // Verificar si hay alguna tabla con datos de contexto
    console.log('\n🔍 Buscando datos de contexto en tablas existentes...');
    
    // Intentar con tabla respuestas (esquema anterior)
    try {
      const { data: respuestasData, error: respuestasError } = await supabase
        .from('respuestas')
        .select('*')
        .limit(5);
      
      if (!respuestasError && respuestasData) {
        console.log('✅ Tabla respuestas encontrada con datos:');
        console.table(respuestasData);
      }
    } catch (err) {
      console.log('❌ No se pudo acceder a tabla respuestas');
    }
    
    // Intentar con tabla preguntas
    try {
      const { data: preguntasData, error: preguntasError } = await supabase
        .from('preguntas')
        .select('*')
        .limit(5);
      
      if (!preguntasError && preguntasData) {
        console.log('\n✅ Tabla preguntas encontrada con datos:');
        console.table(preguntasData);
      }
    } catch (err) {
      console.log('❌ No se pudo acceder a tabla preguntas');
    }
    
  } catch (error) {
    console.error('❌ Error general:', error);
  }
}

verificarTablas();