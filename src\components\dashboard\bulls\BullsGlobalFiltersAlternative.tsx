import React, { useState, useEffect, useRef } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../ui/select';
import { Button } from '../../ui/button';
import { Badge } from '../../ui/badge';
import { X } from 'lucide-react';

interface FilterState {
  groupId: string;
  patientType: string;
  role: string;
  startDate: string;
  endDate: string;
}

interface BullsGlobalFiltersAlternativeProps {
  onFiltersChange: (filters: FilterState) => void;
}

const filterConfigs = [
  {
    key: 'groupId' as const,
    label: 'Curso',
    placeholder: 'Seleccionar curso',
    options: [
      { value: '6B', label: '6ºB' },
      { value: '8A', label: '8ºA' },
      { value: '8B', label: '8ºB' },
    ],
  },
  {
    key: 'patientType' as const,
    label: '<PERSON><PERSON><PERSON>',
    placeholder: 'Seleccionar género',
    options: [
      { value: 'F', label: '<PERSON><PERSON><PERSON>' },
      { value: 'M', label: 'Ma<PERSON><PERSON><PERSON>' },
    ],
  },
  {
    key: 'role' as const,
    label: 'Rol',
    placeholder: 'Se<PERSON>ccionar rol',
    options: [
      { value: 'agresor', label: 'Agresor' },
      { value: 'victima', label: 'Víctima' },
      { value: 'observador', label: 'Observador' },
      { value: 'no_rol', label: 'Sin rol específico' },
    ],
  },
];

export const BullsGlobalFiltersAlternative: React.FC<BullsGlobalFiltersAlternativeProps> = ({ onFiltersChange }) => {
  const [filters, setFilters] = useState<FilterState>({
    groupId: '6B', // Valor por defecto
    patientType: '',
    role: '',
    startDate: '',
    endDate: ''
  });

  // Referencia para evitar llamadas innecesarias
  const prevFiltersRef = useRef<FilterState>(filters);
  const isInitialMount = useRef(true);
  
  // Notificar cambios de filtros al componente padre solo cuando realmente cambien
  useEffect(() => {
    // En el primer render, siempre notificar
    if (isInitialMount.current) {
      isInitialMount.current = false;
      prevFiltersRef.current = filters;
      onFiltersChange(filters);
      return;
    }
    
    // Para renders posteriores, solo notificar si hay cambios reales
    const hasChanged = Object.keys(filters).some(
      key => prevFiltersRef.current[key as keyof FilterState] !== filters[key as keyof FilterState]
    );
    
    if (hasChanged) {
      prevFiltersRef.current = { ...filters };
      onFiltersChange(filters);
    }
  }, [filters]); // Removemos onFiltersChange de las dependencias

  const handleFilterChange = (key: keyof FilterState, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === 'all' ? '' : value
    }));
  };

  const clearFilters = () => {
    setFilters({
      groupId: '',
      patientType: '',
      role: '',
      startDate: '',
      endDate: ''
    });
  };

  const clearSingleFilter = (key: keyof FilterState) => {
    setFilters(prev => ({
      ...prev,
      [key]: ''
    }));
  };

  const getLabelForValue = (filterKey: string, value: string) => {
    const config = filterConfigs.find(f => f.key === filterKey);
    const option = config?.options.find(o => o.value === value);
    return option?.label || value;
  };

  const hasActiveFilters = Object.values(filters).some(value => value !== '');

  return (
    <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <h3 className="text-lg font-semibold text-gray-900">Filtros Globales</h3>
          {hasActiveFilters && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              Filtros activos
            </span>
          )}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={clearFilters}
          className="text-gray-600 hover:text-gray-900"
          disabled={!hasActiveFilters}
        >
          Limpiar Filtros
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {filterConfigs.map(filter => (
          <div className="space-y-2" key={filter.key}>
            <label className="text-sm font-medium text-gray-700">{filter.label}</label>
            <Select
              value={filters[filter.key] || 'all'}
              onValueChange={(value: string) => handleFilterChange(filter.key, value)}
            >
              <SelectTrigger>
                <SelectValue placeholder={filter.placeholder} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                {filter.options.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        ))}

        {/* Filtro de fechas simplificado */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Período</label>
          <div className="flex space-x-2">
            <input
              type="date"
              value={filters.startDate}
              onChange={(e) => handleFilterChange('startDate', e.target.value)}
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            />
            <input
              type="date"
              value={filters.endDate}
              onChange={(e) => handleFilterChange('endDate', e.target.value)}
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            />
          </div>
        </div>
      </div>

      {/* Filtros activos */}
      {hasActiveFilters && (
        <div className="mt-4 flex flex-wrap gap-2">
          {filterConfigs.map(filter => {
            const filterValue = filters[filter.key];
            if (!filterValue) return null;
            
            return (
              <Badge key={filter.key} variant="secondary" className="flex items-center gap-1">
                {filter.label}: {getLabelForValue(filter.key, filterValue)}
                <X
                  className="h-3 w-3 cursor-pointer hover:text-red-500"
                  onClick={() => clearSingleFilter(filter.key)}
                />
              </Badge>
            );
          })}
          {(filters.startDate || filters.endDate) && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Período: {filters.startDate} - {filters.endDate}
              <X 
                className="h-3 w-3 cursor-pointer hover:text-red-500" 
                onClick={() => {
                  clearSingleFilter('startDate');
                  clearSingleFilter('endDate');
                }}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  );
};