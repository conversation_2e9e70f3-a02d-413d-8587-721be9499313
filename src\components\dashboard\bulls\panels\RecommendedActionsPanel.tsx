import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '../../../ui/card';
import { Badge } from '../../../ui/badge';
import { Button } from '../../../ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '../../../ui/tabs';
import { 
  Target, 
  Clock, 
  Users, 
  AlertTriangle, 
  CheckCircle, 
  User, 
  Building, 
  Calendar,
  ArrowRight,
  FileText,
  Download
} from 'lucide-react';

interface Recommendation {
  id: string;
  type: 'individual' | 'grupal' | 'institucional';
  priority: 'baja' | 'media' | 'alta' | 'critica';
  title: string;
  description: string;
  specificActions: string[];
  requiredResources: string[];
  estimatedTime: string;
  responsible: string[];
  successIndicators: string[];
  status: 'pendiente' | 'en_proceso' | 'completada';
  dueDate?: string;
  targetStudents?: string[];
}

interface RecommendedActionsPanelProps {
  data?: {
    recommendations: Recommendation[];
    summary: {
      total: number;
      byPriority: Record<string, number>;
      byType: Record<string, number>;
      byStatus: Record<string, number>;
    };
  };
  onUpdateRecommendationStatus?: (id: string, newStatus: string) => void;
  onNavigateToProfiles?: (studentId?: string) => void;
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'critica': return 'bg-red-100 text-red-800 border-red-200';
    case 'alta': return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'media': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'baja': return 'bg-blue-100 text-blue-800 border-blue-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'completada': return 'bg-green-100 text-green-800';
    case 'en_proceso': return 'bg-blue-100 text-blue-800';
    case 'pendiente': return 'bg-gray-100 text-gray-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getTypeIcon = (type: string) => {
  switch (type) {
    case 'individual': return <User className="h-4 w-4" />;
    case 'grupal': return <Users className="h-4 w-4" />;
    case 'institucional': return <Building className="h-4 w-4" />;
    default: return <Target className="h-4 w-4" />;
  }
};

const RecommendationCard: React.FC<{ 
  recommendation: Recommendation;
  onUpdateStatus?: (id: string, newStatus: string) => void;
  onExportPlan?: (recommendation: Recommendation) => void;
}> = ({ recommendation, onUpdateStatus, onExportPlan }) => {
  const [expanded, setExpanded] = useState(false);

  return (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className="p-2 bg-blue-50 rounded-lg">
              {getTypeIcon(recommendation.type)}
            </div>
            <div className="flex-1">
              <CardTitle className="text-lg mb-2">{recommendation.title}</CardTitle>
              <div className="flex items-center space-x-2 mb-2">
                <Badge className={getPriorityColor(recommendation.priority)}>
                  Prioridad {recommendation.priority.charAt(0).toUpperCase() + recommendation.priority.slice(1)}
                </Badge>
                <Badge className={getStatusColor(recommendation.status)}>
                  {recommendation.status.replace('_', ' ').charAt(0).toUpperCase() + recommendation.status.replace('_', ' ').slice(1)}
                </Badge>
                <Badge variant="outline">
                  {recommendation.type.charAt(0).toUpperCase() + recommendation.type.slice(1)}
                </Badge>
              </div>
              <p className="text-gray-600 text-sm">{recommendation.description}</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setExpanded(!expanded)}
          >
            <ArrowRight className={`h-4 w-4 transition-transform ${expanded ? 'rotate-90' : ''}`} />
          </Button>
        </div>
      </CardHeader>
      
      {expanded && (
        <CardContent className="pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2 flex items-center space-x-2">
                <CheckCircle className="h-4 w-4" />
                <span>Acciones Específicas</span>
              </h4>
              <ul className="space-y-1 text-sm">
                {recommendation.specificActions.map((action, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="text-blue-600 mt-1">•</span>
                    <span>{action}</span>
                  </li>
                ))}
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2 flex items-center space-x-2">
                <Target className="h-4 w-4" />
                <span>Indicadores de Éxito</span>
              </h4>
              <ul className="space-y-1 text-sm">
                {recommendation.successIndicators.map((indicator, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="text-green-600 mt-1">•</span>
                    <span>{indicator}</span>
                  </li>
                ))}
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2 flex items-center space-x-2">
                <Users className="h-4 w-4" />
                <span>Recursos Necesarios</span>
              </h4>
              <ul className="space-y-1 text-sm">
                {recommendation.requiredResources.map((resource, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="text-purple-600 mt-1">•</span>
                    <span>{resource}</span>
                  </li>
                ))}
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2 flex items-center space-x-2">
                <Clock className="h-4 w-4" />
                <span>Información Adicional</span>
              </h4>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">Tiempo estimado:</span> {recommendation.estimatedTime}
                </div>
                <div>
                  <span className="font-medium">Responsables:</span> {recommendation.responsible.join(', ')}
                </div>
                {recommendation.dueDate && (
                  <div>
                    <span className="font-medium">Fecha límite:</span> {new Date(recommendation.dueDate).toLocaleDateString()}
                  </div>
                )}
                {recommendation.targetStudents && (
                  <div>
                    <span className="font-medium">Estudiantes objetivo:</span> {recommendation.targetStudents.join(', ')}
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <div className="mt-4 pt-4 border-t flex space-x-2">
            <Button size="sm" variant="outline" onClick={() => setExpanded(!expanded)}>
              <FileText className="h-4 w-4 mr-2" />
              {expanded ? 'Ocultar Detalles' : 'Ver Detalles'}
            </Button>
            <Button 
              size="sm" 
              variant="outline"
              onClick={() => onExportPlan?.(recommendation)}
            >
              <Download className="h-4 w-4 mr-2" />
              Exportar Plan
            </Button>
            {recommendation.status === 'pendiente' && (
              <Button 
                size="sm"
                onClick={() => onUpdateStatus?.(recommendation.id, 'en_proceso')}
              >
                Marcar como En Proceso
              </Button>
            )}
            {recommendation.status === 'en_proceso' && (
              <Button 
                size="sm"
                onClick={() => onUpdateStatus?.(recommendation.id, 'completada')}
              >
                Marcar como Completada
              </Button>
            )}
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export const RecommendedActionsPanel: React.FC<RecommendedActionsPanelProps> = ({ 
  data, 
  onUpdateRecommendationStatus,
  onNavigateToProfiles 
}) => {
  const [activeTab, setActiveTab] = useState('all');

  // Función para exportar plan individual
  const handleExportPlan = (recommendation: Recommendation) => {
    const csvContent = [
      'Título,Descripción,Tipo,Prioridad,Estado,Tiempo Estimado,Responsables',
      `"${recommendation.title}","${recommendation.description}","${recommendation.type}","${recommendation.priority}","${recommendation.status}","${recommendation.estimatedTime}","${recommendation.responsible.join('; ')}"`
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `plan_${recommendation.id}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Función para exportar plan completo
  const handleExportFullPlan = () => {
    if (!data) return;
    
    const csvContent = [
      'Título,Descripción,Tipo,Prioridad,Estado,Tiempo Estimado,Responsables,Acciones Específicas',
      ...data.recommendations.map(rec => 
        `"${rec.title}","${rec.description}","${rec.type}","${rec.priority}","${rec.status}","${rec.estimatedTime}","${rec.responsible.join('; ')}","${rec.specificActions.join('; ')}"`
      )
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'plan_completo_recomendaciones.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Función para generar reporte
  const handleGenerateReport = () => {
    if (!data) return;
    
    const reportContent = `
REPORTE DE RECOMENDACIONES\n
Fecha: ${new Date().toLocaleDateString()}\n
RESUMEN:\n- Total de recomendaciones: ${data.summary.total}\n- Pendientes: ${data.summary.byStatus.pendiente || 0}\n- En proceso: ${data.summary.byStatus.en_proceso || 0}\n- Completadas: ${data.summary.byStatus.completada || 0}\n
DETALLE POR PRIORIDAD:\n- Crítica: ${data.summary.byPriority.critica || 0}\n- Alta: ${data.summary.byPriority.alta || 0}\n- Media: ${data.summary.byPriority.media || 0}\n- Baja: ${data.summary.byPriority.baja || 0}\n
RECOMENDACIONES:\n${data.recommendations.map((rec, index) => `\n${index + 1}. ${rec.title}\n   Tipo: ${rec.type}\n   Prioridad: ${rec.priority}\n   Estado: ${rec.status}\n   Descripción: ${rec.description}\n`).join('')}
    `;
    
    const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'reporte_recomendaciones.txt');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (!data) {
    return <div>Cargando recomendaciones...</div>; // O un skeleton loader
  }
  
  const filteredRecommendations = data.recommendations.filter(rec => {
    if (activeTab === 'all') return true;
    if (activeTab === 'pending') return rec.status === 'pendiente';
    if (activeTab === 'in_progress') return rec.status === 'en_proceso';
    if (activeTab === 'completed') return rec.status === 'completada';
    return rec.type === activeTab;
  });

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-blue-600" />
              <div>
                <div className="text-2xl font-bold">{data.summary.total}</div>
                <div className="text-sm text-gray-600">Total Recomendaciones</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <div>
                <div className="text-2xl font-bold text-red-600">{data.summary.byPriority.critica + data.summary.byPriority.alta}</div>
                <div className="text-sm text-gray-600">Alta Prioridad</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-orange-600" />
              <div>
                <div className="text-2xl font-bold text-orange-600">{data.summary.byStatus.pendiente}</div>
                <div className="text-sm text-gray-600">Pendientes</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <div className="text-2xl font-bold text-green-600">{data.summary.byStatus.completada}</div>
                <div className="text-sm text-gray-600">Completadas</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Actions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Target className="h-5 w-5" />
              <span>Plan de Acciones Recomendadas</span>
            </CardTitle>
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleExportFullPlan}
              >
                <Download className="h-4 w-4 mr-2" />
                Exportar Plan Completo
              </Button>
              <Button 
                size="sm"
                onClick={handleGenerateReport}
              >
                <FileText className="h-4 w-4 mr-2" />
                Generar Reporte
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-7">
              <TabsTrigger value="all">Todas</TabsTrigger>
              <TabsTrigger value="pending">Pendientes</TabsTrigger>
              <TabsTrigger value="in_progress">En Proceso</TabsTrigger>
              <TabsTrigger value="completed">Completadas</TabsTrigger>
              <TabsTrigger value="individual">Individual</TabsTrigger>
              <TabsTrigger value="grupal">Grupal</TabsTrigger>
              <TabsTrigger value="institucional">Institucional</TabsTrigger>
            </TabsList>
            
            <TabsContent value={activeTab} className="mt-6">
              {filteredRecommendations.length > 0 ? (
                <div>
                  {filteredRecommendations.map((recommendation) => (
                    <RecommendationCard 
                      key={recommendation.id} 
                      recommendation={recommendation}
                      onUpdateStatus={onUpdateRecommendationStatus}
                      onExportPlan={handleExportPlan}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No hay recomendaciones que coincidan con el filtro seleccionado.
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};