-- ============================================================
-- SQL COMPLETO PARA APLICAR EN SUPABASE DASHBOARD
-- Generado automáticamente - Copiar y pegar en SQL Editor
-- ============================================================

-- ============================================================
-- 20241220000002_CREATE_SOCIOMETRIC_VIEWS.SQL
-- ============================================================

-- Migración para crear vistas de análisis sociométrico
-- Estas vistas centralizan los cálculos de métricas en la base de datos

-- Vista para estadísticas sociométricas básicas
CREATE OR REPLACE VIEW public.view_sociometric_stats AS
WITH nominaciones AS (
    -- Extraer nominaciones de la tabla de respuestas (datos existentes)
    SELECT 
        r.grupo_id,
        r.estudiante_id AS elector_id,
        p.tipo_pregunta,
        -- Convertir JSON array a filas individuales
        jsonb_array_elements_text(r.respuesta_texto::jsonb) AS elegido_id
    FROM 
        public.respuestas r
    JOIN 
        public.preguntas p ON r.pregunta_id = p.id
    WHERE 
        p.categoria = 'sociometrica' 
        AND r.respuesta_texto IS NOT NULL 
        AND r.respuesta_texto <> '[]'
        AND r.respuesta_texto <> 'null'
    
    UNION ALL
    
    -- Incluir nominaciones de la nueva tabla optimizada
    SELECT 
        ns.grupo_id,
        ns.evaluador_id AS elector_id,
        ns.tipo_nominacion AS tipo_pregunta,
        ns.evaluado_id::text AS elegido_id
    FROM 
        public.nominaciones_sociometricas ns
),
elecciones_recibidas AS (
    SELECT 
        n.elegido_id::uuid,
        n.grupo_id,
        COUNT(*) AS total_elecciones
    FROM nominaciones n
    WHERE n.tipo_pregunta = 'eleccion'
    GROUP BY n.elegido_id, n.grupo_id
),
rechazos_recibidos AS (
    SELECT 
        n.elegido_id::uuid,
        n.grupo_id,
        COUNT(*) AS total_rechazos
    FROM nominaciones n
    WHERE n.tipo_pregunta = 'rechazo'
    GROUP BY n.elegido_id, n.grupo_id
),
estadisticas_grupo AS (
    SELECT 
        e.grupo_id,
        COUNT(*) AS total_estudiantes,
        AVG(COALESCE(er.total_elecciones, 0)) AS promedio_elecciones,
        AVG(COALESCE(rr.total_rechazos, 0)) AS promedio_rechazos
    FROM public.estudiantes e
    LEFT JOIN elecciones_recibidas er ON e.id = er.elegido_id AND e.grupo_id = er.grupo_id
    LEFT JOIN rechazos_recibidos rr ON e.id = rr.elegido_id AND e.grupo_id = rr.grupo_id
    GROUP BY e.grupo_id
)
SELECT 
    e.id AS estudiante_id,
    e.grupo_id,
    g.nombre AS nombre_grupo,
    e.nombre_estudiante,
    e.apellido_estudiante,
    COALESCE(er.total_elecciones, 0) AS elecciones_recibidas,
    COALESCE(rr.total_rechazos, 0) AS rechazos_recibidos,
    eg.total_estudiantes,
    eg.promedio_elecciones,
    eg.promedio_rechazos,
    -- Calcular índice sociométrico (elecciones - rechazos)
    (COALESCE(er.total_elecciones, 0) - COALESCE(rr.total_rechazos, 0)) AS indice_sociometrico,
    -- Clasificación sociométrica estándar
    CASE 
        WHEN COALESCE(er.total_elecciones, 0) >= eg.promedio_elecciones + (0.5 * eg.promedio_elecciones) 
             AND COALESCE(rr.total_rechazos, 0) <= eg.promedio_rechazos 
        THEN 'popular'
        WHEN COALESCE(er.total_elecciones, 0) <= eg.promedio_elecciones * 0.5 
             AND COALESCE(rr.total_rechazos, 0) >= eg.promedio_rechazos + (0.5 * eg.promedio_rechazos)
        THEN 'rechazado'
        WHEN COALESCE(er.total_elecciones, 0) <= eg.promedio_elecciones * 0.5 
             AND COALESCE(rr.total_rechazos, 0) <= eg.promedio_rechazos * 0.5
        THEN 'aislado'
        WHEN COALESCE(er.total_elecciones, 0) >= eg.promedio_elecciones 
             AND COALESCE(rr.total_rechazos, 0) >= eg.promedio_rechazos
        THEN 'controvertido'
        ELSE 'promedio'
    END AS estatus_sociometrico
FROM 
    public.estudiantes e
LEFT JOIN 
    elecciones_recibidas er ON e.id = er.elegido_id
LEFT JOIN 
    rechazos_recibidos rr ON e.id = rr.elegido_id
JOIN 
    public.grupos g ON e.grupo_id = g.id
JOIN 
    estadisticas_grupo eg ON e.grupo_id = eg.grupo_id;

-- Vista para métricas de convivencia mejoradas
CREATE OR REPLACE VIEW public.view_convivencia_metrics AS
WITH bullying_responses AS (
    SELECT 
        r.estudiante_id,
        r.grupo_id,
        p.pregunta,
        r.respuesta_numerica,
        p.id as pregunta_id,
        -- Clasificar tipos de bullying según la pregunta
        CASE 
            WHEN p.pregunta ILIKE '%físic%' OR p.pregunta ILIKE '%golpe%' OR p.pregunta ILIKE '%empuj%' THEN 'fisico'
            WHEN p.pregunta ILIKE '%verbal%' OR p.pregunta ILIKE '%insult%' OR p.pregunta ILIKE '%burla%' THEN 'verbal'
            WHEN p.pregunta ILIKE '%social%' OR p.pregunta ILIKE '%exclu%' OR p.pregunta ILIKE '%aislam%' THEN 'social'
            WHEN p.pregunta ILIKE '%cyber%' OR p.pregunta ILIKE '%internet%' OR p.pregunta ILIKE '%redes%' THEN 'cibernetico'
            ELSE 'general'
        END AS tipo_bullying,
        -- Clasificar rol según la pregunta
        CASE 
            WHEN p.pregunta ILIKE '%haces%' OR p.pregunta ILIKE '%realizas%' THEN 'agresor'
            WHEN p.pregunta ILIKE '%sufres%' OR p.pregunta ILIKE '%recibes%' THEN 'victima'
            WHEN p.pregunta ILIKE '%observas%' OR p.pregunta ILIKE '%ves%' THEN 'observador'
            ELSE 'general'
        END AS rol_bullying
    FROM 
        public.respuestas r
    JOIN 
        public.preguntas p ON r.pregunta_id = p.id
    WHERE 
        p.categoria = 'bullying'
        AND r.respuesta_numerica IS NOT NULL
),
metricas_estudiante AS (
    SELECT 
        estudiante_id,
        grupo_id,
        -- Métricas por rol
        AVG(CASE WHEN rol_bullying = 'agresor' THEN respuesta_numerica END) AS nivel_agresion,
        AVG(CASE WHEN rol_bullying = 'victima' THEN respuesta_numerica END) AS nivel_victimizacion,
        AVG(CASE WHEN rol_bullying = 'observador' THEN respuesta_numerica END) AS nivel_observacion,
        -- Métricas por tipo
        AVG(CASE WHEN tipo_bullying = 'fisico' THEN respuesta_numerica END) AS bullying_fisico,
        AVG(CASE WHEN tipo_bullying = 'verbal' THEN respuesta_numerica END) AS bullying_verbal,
        AVG(CASE WHEN tipo_bullying = 'social' THEN respuesta_numerica END) AS bullying_social,
        AVG(CASE WHEN tipo_bullying = 'cibernetico' THEN respuesta_numerica END) AS bullying_cibernetico,
        -- Indicadores de riesgo
        MAX(respuesta_numerica) AS max_respuesta,
        COUNT(*) AS total_respuestas
    FROM bullying_responses
    GROUP BY estudiante_id, grupo_id
)
SELECT 
    e.id AS estudiante_id,
    e.grupo_id,
    g.nombre AS nombre_grupo,
    e.nombre_estudiante,
    e.apellido_estudiante,
    -- Métricas de bullying
    COALESCE(me.nivel_agresion, 0) AS nivel_agresion,
    COALESCE(me.nivel_victimizacion, 0) AS nivel_victimizacion,
    COALESCE(me.nivel_observacion, 0) AS nivel_observacion,
    COALESCE(me.bullying_fisico, 0) AS bullying_fisico,
    COALESCE(me.bullying_verbal, 0) AS bullying_verbal,
    COALESCE(me.bullying_social, 0) AS bullying_social,
    COALESCE(me.bullying_cibernetico, 0) AS bullying_cibernetico,
    -- Métricas sociométricas
    COALESCE(vs.elecciones_recibidas, 0) AS elecciones_recibidas,
    COALESCE(vs.rechazos_recibidos, 0) AS rechazos_recibidos,
    COALESCE(vs.estatus_sociometrico, 'sin_datos') AS estatus_sociometrico,
    -- Indicadores de riesgo
    CASE 
        WHEN me.nivel_victimizacion >= 3 OR me.nivel_agresion >= 3 THEN 'alto'
        WHEN me.nivel_victimizacion >= 2 OR me.nivel_agresion >= 2 THEN 'medio'
        WHEN me.nivel_victimizacion >= 1 OR me.nivel_agresion >= 1 THEN 'bajo'
        ELSE 'sin_riesgo'
    END AS nivel_riesgo_bullying,
    CASE 
        WHEN vs.estatus_sociometrico IN ('rechazado', 'aislado') THEN 'alto'
        WHEN vs.estatus_sociometrico = 'controvertido' THEN 'medio'
        ELSE 'bajo'
    END AS nivel_riesgo_social
FROM 
    public.estudiantes e
JOIN 
    public.grupos g ON e.grupo_id = g.id
LEFT JOIN 
    metricas_estudiante me ON e.id = me.estudiante_id
LEFT JOIN 
    public.view_sociometric_stats vs ON e.id = vs.estudiante_id;

-- Habilitar RLS en las vistas
ALTER VIEW public.view_sociometric_stats SET (security_invoker = true);
ALTER VIEW public.view_convivencia_metrics SET (security_invoker = true);

-- ============================================================
-- 20241225000001_CREATE_BULLS_DASHBOARD_VIEWS.SQL
-- ============================================================

-- Migración para crear las vistas específicas del dashboard BULL-S
-- Estas vistas implementan las métricas exactas requeridas por cada módulo

-- Vista para roles de estudiantes (vw_roles)
CREATE OR REPLACE VIEW public.vw_roles AS
WITH nominaciones_agresion AS (
    SELECT 
        r.estudiante_id,
        r.grupo_id,
        COUNT(*) as nom_agr
    FROM public.respuestas r
    JOIN public.preguntas p ON r.pregunta_id = p.id
    WHERE (p.pregunta ILIKE '%intimidar%' OR p.pregunta ILIKE '%maltratar%' OR p.pregunta ILIKE '%agresi%')
        AND r.respuesta_texto IS NOT NULL
        AND r.respuesta_texto != '[]'
    GROUP BY r.estudiante_id, r.grupo_id
),
nominaciones_victima AS (
    SELECT 
        r.estudiante_id,
        r.grupo_id,
        COUNT(*) as nom_vic
    FROM public.respuestas r
    JOIN public.preguntas p ON r.pregunta_id = p.id
    WHERE (p.pregunta ILIKE '%víctima%' OR p.pregunta ILIKE '%victima%' OR p.pregunta ILIKE '%maltrato%')
        AND r.respuesta_texto IS NOT NULL
        AND r.respuesta_texto != '[]'
    GROUP BY r.estudiante_id, r.grupo_id
),
nominaciones_observador AS (
    SELECT 
        r.estudiante_id,
        r.grupo_id,
        COUNT(*) as nom_obs
    FROM public.respuestas r
    JOIN public.preguntas p ON r.pregunta_id = p.id
    WHERE (p.pregunta ILIKE '%observ%' OR p.pregunta ILIKE '%ve%')
        AND r.respuesta_texto IS NOT NULL
        AND r.respuesta_texto != '[]'
    GROUP BY r.estudiante_id, r.grupo_id
),
total_estudiantes AS (
    SELECT 
        grupo_id,
        COUNT(*) as total
    FROM public.estudiantes
    GROUP BY grupo_id
)
SELECT 
    e.id AS student_id,
    e.nombre_estudiante AS name,
    g.nombre AS course,
    e.genero AS gender,
    CASE 
        WHEN COALESCE(na.nom_agr, 0) >= 0.25 * te.total AND COALESCE(nv.nom_vic, 0) >= 0.25 * te.total THEN 'agresor-victima'
        WHEN COALESCE(na.nom_agr, 0) >= 0.25 * te.total THEN 'agresor'
        WHEN COALESCE(nv.nom_vic, 0) >= 0.25 * te.total THEN 'victima'
        WHEN COALESCE(no.nom_obs, 0) >= 0.25 * te.total THEN 'observador'
        ELSE 'sin rol específico'
    END AS role
FROM public.estudiantes e
JOIN public.grupos g ON e.grupo_id = g.id
LEFT JOIN nominaciones_agresion na ON e.id = na.estudiante_id
LEFT JOIN nominaciones_victima nv ON e.id = nv.estudiante_id
LEFT JOIN nominaciones_observador no ON e.id = no.estudiante_id
JOIN total_estudiantes te ON e.grupo_id = te.grupo_id;

-- Vista para popularidad (vw_popularity)
CREATE OR REPLACE VIEW public.vw_popularity AS
WITH elecciones_positivas AS (
    SELECT 
        jsonb_array_elements_text(r.respuesta_texto::jsonb)::uuid AS elegido_id,
        r.grupo_id,
        COUNT(*) as elecciones
    FROM public.respuestas r
    JOIN public.preguntas p ON r.pregunta_id = p.id
    WHERE p.categoria = 'sociometrica' 
        AND (p.pregunta ILIKE '%gusta%' OR p.pregunta ILIKE '%amigo%' OR p.pregunta ILIKE '%jugar%')
        AND r.respuesta_texto IS NOT NULL
        AND r.respuesta_texto != '[]'
    GROUP BY elegido_id, r.grupo_id
),
total_grupo AS (
    SELECT 
        grupo_id,
        COUNT(*) as total_estudiantes
    FROM public.estudiantes
    GROUP BY grupo_id
)
SELECT 
    e.id AS student_id,
    e.nombre_estudiante AS name,
    g.nombre AS course,
    COALESCE(ep.elecciones, 0) AS elecciones_recibidas,
    ROUND((COALESCE(ep.elecciones, 0)::numeric / tg.total_estudiantes::numeric) * 100, 2) AS Pe
FROM public.estudiantes e
JOIN public.grupos g ON e.grupo_id = g.id
LEFT JOIN elecciones_positivas ep ON e.id = ep.elegido_id AND e.grupo_id = ep.grupo_id
JOIN total_grupo tg ON e.grupo_id = tg.grupo_id;

-- Vista para rechazo (vw_rejection)
CREATE OR REPLACE VIEW public.vw_rejection AS
WITH rechazos AS (
    SELECT 
        jsonb_array_elements_text(r.respuesta_texto::jsonb)::uuid AS rechazado_id,
        r.grupo_id,
        COUNT(*) as rechazos
    FROM public.respuestas r
    JOIN public.preguntas p ON r.pregunta_id = p.id
    WHERE p.categoria = 'sociometrica' 
        AND (p.pregunta ILIKE '%no gusta%' OR p.pregunta ILIKE '%rechazo%' OR p.pregunta ILIKE '%manía%')
        AND r.respuesta_texto IS NOT NULL
        AND r.respuesta_texto != '[]'
    GROUP BY rechazado_id, r.grupo_id
),
total_grupo AS (
    SELECT 
        grupo_id,
        COUNT(*) as total_estudiantes
    FROM public.estudiantes
    GROUP BY grupo_id
)
SELECT 
    e.id AS student_id,
    e.nombre_estudiante AS name,
    g.nombre AS course,
    COALESCE(r.rechazos, 0) AS rechazos_recibidos,
    ROUND((COALESCE(r.rechazos, 0)::numeric / tg.total_estudiantes::numeric) * 100, 2) AS Sp
FROM public.estudiantes e
JOIN public.grupos g ON e.grupo_id = g.id
LEFT JOIN rechazos r ON e.id = r.rechazado_id AND e.grupo_id = r.grupo_id
JOIN total_grupo tg ON e.grupo_id = tg.grupo_id;

-- Vista para cohesión grupal (vw_cohesion)
-- Implementa la fórmula especificada: reciprocal * 100.0 / possible
-- Usa sociomatrix con items 1,3 (elecciones positivas)
CREATE OR REPLACE VIEW public.vw_cohesion AS
WITH reciprocidades AS (
    SELECT 
        s1.course,
        SUM(CASE WHEN s1.chooser_id = s2.chosen_id AND s2.chooser_id = s1.chosen_id THEN 1 ELSE 0 END) AS reciprocal
    FROM public.sociomatrix s1
    JOIN public.sociomatrix s2 ON 
        s1.course = s2.course
        AND s1.chooser_id = s2.chosen_id
        AND s1.chosen_id = s2.chooser_id
        AND s1.item = s2.item
    WHERE s1.item IN (1, 3) -- elecciones positivas
    GROUP BY s1.course
),
total_posibles AS (
    SELECT 
        course,
        (COUNT(DISTINCT student_id) * (COUNT(DISTINCT student_id) - 1) / 2) AS possible
    FROM public.students
    GROUP BY course
)
SELECT 
    COALESCE(g.nombre, tp.course) AS course,
    tp.course AS grupo_id,
    COALESCE(r.reciprocal, 0) as reciprocidades,
    tp.possible as total_posibles_relaciones,
    ROUND(COALESCE(r.reciprocal, 0) * 100.0 / NULLIF(tp.possible, 0), 1) AS cohesion_pct
FROM total_posibles tp
LEFT JOIN reciprocidades r ON tp.course = r.course
LEFT JOIN public.grupos g ON g.nombre = tp.course;

-- Vista para agresores identificados (vw_aggressors)
CREATE OR REPLACE VIEW public.vw_aggressors AS
WITH nominaciones_agresor AS (
    SELECT 
        jsonb_array_elements_text(r.respuesta_texto::jsonb)::uuid AS agresor_id,
        r.grupo_id,
        COUNT(*) as nominaciones_agr
    FROM public.respuestas r
    JOIN public.preguntas p ON r.pregunta_id = p.id
    WHERE (p.pregunta ILIKE '%intimidar%' OR p.pregunta ILIKE '%maltratar%' OR p.pregunta ILIKE '%agresi%')
        AND r.respuesta_texto IS NOT NULL
        AND r.respuesta_texto != '[]'
    GROUP BY agresor_id, r.grupo_id
),
total_grupo AS (
    SELECT 
        grupo_id,
        COUNT(*) as total_students
    FROM public.estudiantes
    GROUP BY grupo_id
)
SELECT 
    e.id AS student_id,
    e.nombre_estudiante AS name,
    g.nombre AS course,
    COALESCE(na.nominaciones_agr, 0) AS nominations_agr,
    tg.total_students
FROM public.estudiantes e
JOIN public.grupos g ON e.grupo_id = g.id
LEFT JOIN nominaciones_agresor na ON e.id = na.agresor_id AND e.grupo_id = na.grupo_id
JOIN total_grupo tg ON e.grupo_id = tg.grupo_id
WHERE COALESCE(na.nominaciones_agr, 0) >= 0.25 * tg.total_students;

-- Vista para víctimas identificadas (vw_victims)
CREATE OR REPLACE VIEW public.vw_victims AS
WITH nominaciones_victima AS (
    SELECT 
        jsonb_array_elements_text(r.respuesta_texto::jsonb)::uuid AS victima_id,
        r.grupo_id,
        COUNT(*) as nominaciones_vic
    FROM public.respuestas r
    JOIN public.preguntas p ON r.pregunta_id = p.id
    WHERE (p.pregunta ILIKE '%víctima%' OR p.pregunta ILIKE '%victima%' OR p.pregunta ILIKE '%maltrato%')
        AND r.respuesta_texto IS NOT NULL
        AND r.respuesta_texto != '[]'
    GROUP BY victima_id, r.grupo_id
),
total_grupo AS (
    SELECT 
        grupo_id,
        COUNT(*) as total_students
    FROM public.estudiantes
    GROUP BY grupo_id
)
SELECT 
    e.id AS student_id,
    e.nombre_estudiante AS name,
    g.nombre AS course,
    COALESCE(nv.nominaciones_vic, 0) AS nominations_vic,
    tg.total_students
FROM public.estudiantes e
JOIN public.grupos g ON e.grupo_id = g.id
LEFT JOIN nominaciones_victima nv ON e.id = nv.victima_id AND e.grupo_id = nv.grupo_id
JOIN total_grupo tg ON e.grupo_id = tg.grupo_id
WHERE COALESCE(nv.nominaciones_vic, 0) >= 0.25 * tg.total_students;

-- Vista para matriz de interacciones (vw_interactions)
CREATE OR REPLACE VIEW public.vw_interactions AS
WITH interacciones AS (
    SELECT 
        r1.estudiante_id AS aggressor_id,
        jsonb_array_elements_text(r1.respuesta_texto::jsonb)::uuid AS victim_id,
        r1.grupo_id,
        COUNT(*) as interaction_count
    FROM public.respuestas r1
    JOIN public.preguntas p1 ON r1.pregunta_id = p1.id
    WHERE (p1.pregunta ILIKE '%intimidar%' OR p1.pregunta ILIKE '%maltratar%')
        AND r1.respuesta_texto IS NOT NULL
        AND r1.respuesta_texto != '[]'
    GROUP BY r1.estudiante_id, victim_id, r1.grupo_id
)
SELECT 
    i.aggressor_id,
    i.victim_id,
    ea.nombre_estudiante AS aggressor_name,
    ev.nombre_estudiante AS victim_name,
    g.nombre AS course,
    i.interaction_count
FROM interacciones i
JOIN public.estudiantes ea ON i.aggressor_id = ea.id
JOIN public.estudiantes ev ON i.victim_id = ev.id
JOIN public.grupos g ON i.grupo_id = g.id
WHERE i.interaction_count > 0;

-- Habilitar RLS en todas las vistas
ALTER VIEW public.vw_roles SET (security_invoker = true);
ALTER VIEW public.vw_popularity SET (security_invoker = true);
ALTER VIEW public.vw_rejection SET (security_invoker = true);
ALTER VIEW public.vw_cohesion SET (security_invoker = true);
ALTER VIEW public.vw_aggressors SET (security_invoker = true);
ALTER VIEW public.vw_victims SET (security_invoker = true);
ALTER VIEW public.vw_interactions SET (security_invoker = true);

-- Crear índices para optimizar el rendimiento
CREATE INDEX IF NOT EXISTS idx_respuestas_estudiante_pregunta ON public.respuestas(estudiante_id, pregunta_id);
CREATE INDEX IF NOT EXISTS idx_respuestas_grupo_categoria ON public.respuestas(grupo_id) WHERE respuesta_texto IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_preguntas_categoria ON public.preguntas(categoria);
CREATE INDEX IF NOT EXISTS idx_estudiantes_grupo ON public.estudiantes(grupo_id);

-- Comentarios para documentación
COMMENT ON VIEW public.vw_roles IS 'Vista que clasifica a los estudiantes en roles: agresor, víctima, observador o sin rol específico basado en nominaciones';
COMMENT ON VIEW public.vw_popularity IS 'Vista que calcula el índice de popularidad (Pe) de cada estudiante basado en elecciones positivas';
COMMENT ON VIEW public.vw_rejection IS 'Vista que calcula el índice de rechazo (Sp) de cada estudiante basado en nominaciones negativas';
COMMENT ON VIEW public.vw_cohesion IS 'Vista que calcula el porcentaje de cohesión grupal basado en reciprocidades de elecciones';
COMMENT ON VIEW public.vw_aggressors IS 'Vista que identifica agresores con nominaciones >= 25% del total de estudiantes del grupo';
COMMENT ON VIEW public.vw_victims IS 'Vista que identifica víctimas con nominaciones >= 25% del total de estudiantes del grupo';
COMMENT ON VIEW public.vw_interactions IS 'Vista que muestra la matriz de interacciones agresor-víctima con frecuencia de nominaciones';

-- ============================================================
-- 20241225000002_CREATE_BULLS_METRICS_FUNCTIONS.SQL
-- ============================================================

-- Migración para crear funciones específicas de métricas del dashboard BULL-S
-- Estas funciones implementan los cálculos exactos requeridos por cada módulo

-- Función para calcular métricas del Panorama del Curso (Módulo 1)
CREATE OR REPLACE FUNCTION public.calcular_panorama_curso(
    p_curso text DEFAULT NULL,
    p_genero text DEFAULT NULL,
    p_periodo_inicio date DEFAULT NULL,
    p_periodo_fin date DEFAULT NULL
)
RETURNS TABLE (
    total_estudiantes bigint,
    estudiantes_en_riesgo bigint,
    porcentaje_en_riesgo numeric,
    cohesion_grupal numeric,
    seguridad_percibida numeric,
    alertas text[]
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_alertas text[] := ARRAY[]::text[];
    v_total bigint;
    v_riesgo bigint;
    v_victimas bigint;
BEGIN
    -- Calcular total de estudiantes con filtros
    SELECT COUNT(DISTINCT e.id)
    INTO v_total
    FROM public.estudiantes e
    JOIN public.grupos g ON e.grupo_id = g.id
    WHERE (p_curso IS NULL OR g.nombre = p_curso)
        AND (p_genero IS NULL OR e.genero = p_genero);
    
    -- Calcular estudiantes en riesgo
    SELECT COUNT(DISTINCT vr.student_id)
    INTO v_riesgo
    FROM public.vw_roles vr
    WHERE vr.role IN ('agresor', 'victima', 'agresor-victima')
        AND (p_curso IS NULL OR vr.course = p_curso)
        AND (p_genero IS NULL OR vr.gender = p_genero);
    
    -- Calcular cohesión grupal promedio
    WITH cohesion_data AS (
        SELECT AVG(vc.cohesion_pct) as avg_cohesion
        FROM public.vw_cohesion vc
        WHERE (p_curso IS NULL OR vc.course = p_curso)
    )
    SELECT COALESCE(avg_cohesion, 0) INTO cohesion_grupal FROM cohesion_data;
    
    -- Calcular seguridad percibida promedio usando item = 15
    WITH seguridad_data AS (
        SELECT 
            AVG(r.response) as avg_seguridad,
            SUM(CASE WHEN r.response >= 3 THEN 1 ELSE 0 END) * 100.0 / COUNT(*) as pct_seguro
        FROM public.answers r
        WHERE r.item = 15
            AND (p_curso IS NULL OR r.course = p_curso)
            AND (p_genero IS NULL OR r.student_id IN (
                SELECT id FROM public.students WHERE gender = p_genero
            ))
    )
    SELECT COALESCE(avg_seguridad, 2.6) INTO seguridad_percibida FROM seguridad_data;
    
    -- Generar alertas automáticas
    SELECT COUNT(*) INTO v_victimas
    FROM public.vw_roles
    WHERE role = 'victima'
        AND (p_curso IS NULL OR course = p_curso)
        AND (p_genero IS NULL OR gender = p_genero);
    
    IF v_victimas >= 5 THEN
        v_alertas := array_append(v_alertas, 'ALERTA: ≥5 víctimas identificadas');
    END IF;
    
    IF cohesion_grupal < 25 THEN
        v_alertas := array_append(v_alertas, 'ALERTA: Cohesión grupal crítica (<25%)');
    END IF;
    
    IF seguridad_percibida < 50 THEN
        v_alertas := array_append(v_alertas, 'ALERTA: Baja percepción de seguridad (<50%)');
    END IF;
    
    RETURN QUERY SELECT 
        v_total,
        v_riesgo,
        CASE WHEN v_total > 0 THEN ROUND((v_riesgo::numeric / v_total::numeric) * 100, 2) ELSE 0 END,
        ROUND(cohesion_grupal, 2),
        ROUND(seguridad_percibida, 2),
        v_alertas;
END;
$$;

-- ============================================================================
-- FUNCIONES ESPECÍFICAS PARA MÉTRICAS DEL MÓDULO CONTEXTO
-- Implementación según especificaciones del usuario
-- ============================================================================

-- 1. Formas de Agresión – Distribución Porcentual (Item 11)
CREATE OR REPLACE FUNCTION public.obtener_formas_agresion(
    p_curso text DEFAULT NULL
)
RETURNS TABLE (
    course text,
    forma text,
    pct numeric,
    total_respuestas bigint
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    WITH first_choice AS (
        SELECT 
            a.course,
            SPLIT_PART(a.response, ',', 1)::text AS forma,
            1 AS cnt
        FROM public.answers a
        WHERE a.item = 11
            AND (p_curso IS NULL OR a.course = p_curso)
    )
    SELECT 
        fc.course,
        fc.forma,
        ROUND(100.0 * SUM(fc.cnt) / SUM(SUM(fc.cnt)) OVER (PARTITION BY fc.course), 1) AS pct,
        SUM(fc.cnt) AS total_respuestas
    FROM first_choice fc
    GROUP BY fc.course, fc.forma 
    ORDER BY fc.course, pct DESC;
END;
$$;

-- 2. Lugares – Distribución Porcentual (Item 12)
CREATE OR REPLACE FUNCTION public.obtener_lugares_agresion(
    p_curso text DEFAULT NULL
)
RETURNS TABLE (
    course text,
    lugar text,
    pct numeric,
    total_respuestas bigint
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    WITH first_place AS (
        SELECT 
            a.course,
            SPLIT_PART(a.response, ',', 1)::text AS lugar,
            1 AS cnt
        FROM public.answers a
        WHERE a.item = 12
            AND (p_curso IS NULL OR a.course = p_curso)
    )
    SELECT 
        fp.course,
        fp.lugar,
        ROUND(100.0 * SUM(fp.cnt) / SUM(SUM(fp.cnt)) OVER (PARTITION BY fp.course), 1) AS pct,
        SUM(fp.cnt) AS total_respuestas
    FROM first_place fp
    GROUP BY fp.course, fp.lugar 
    ORDER BY fp.course, pct DESC;
END;
$$;

-- 3. Dimensión Temporal (Item 13)
CREATE OR REPLACE FUNCTION public.obtener_dimension_temporal(
    p_curso text DEFAULT NULL
)
RETURNS TABLE (
    course text,
    frecuencia text,
    n bigint,
    pct numeric
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.course,
        a.response AS frecuencia,
        COUNT(*) AS n,
        ROUND(100.0 * COUNT(*) / SUM(COUNT(*)) OVER (PARTITION BY a.course), 1) AS pct
    FROM public.answers a
    WHERE a.item = 13
        AND (p_curso IS NULL OR a.course = p_curso)
    GROUP BY a.course, a.response 
    ORDER BY a.course, pct DESC;
END;
$$;

-- 4. Análisis Cruzado (Items 11 x 12)
CREATE OR REPLACE FUNCTION public.obtener_analisis_cruzado(
    p_curso text DEFAULT NULL
)
RETURNS TABLE (
    forma text,
    lugar text,
    cruce bigint,
    pct_cruce numeric
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        f.response AS forma,
        l.response AS lugar,
        COUNT(*) AS cruce,
        ROUND(100.0 * COUNT(*) / SUM(COUNT(*)) OVER (), 1) AS pct_cruce
    FROM public.answers f 
    JOIN public.answers l 
        ON f.student_id = l.student_id 
        AND f.course = l.course
    WHERE f.item = 11 
        AND l.item = 12
        AND (p_curso IS NULL OR f.course = p_curso)
    GROUP BY f.response, l.response 
    ORDER BY pct_cruce DESC;
END;
$$;

-- 5. Función helper para verificar datos disponibles
CREATE OR REPLACE FUNCTION public.verificar_datos_contexto()
RETURNS TABLE (
    item integer,
    total_respuestas bigint,
    cursos_con_datos bigint,
    estudiantes_respondieron bigint
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.item,
        COUNT(*) as total_respuestas,
        COUNT(DISTINCT a.course) as cursos_con_datos,
        COUNT(DISTINCT a.student_id) as estudiantes_respondieron
    FROM public.answers a
    WHERE a.item IN (11, 12, 13)
    GROUP BY a.item
    ORDER BY a.item;
END;
$$;

-- Función para calcular métricas del Sociograma (Módulo 2)
CREATE OR REPLACE FUNCTION public.calcular_sociograma(
    p_curso text DEFAULT NULL
)
RETURNS TABLE (
    student_id uuid,
    name text,
    popularidad numeric,
    rechazo numeric,
    reciprocidades bigint,
    role text,
    estatus_sociometrico text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    WITH reciprocidades_calc AS (
        SELECT 
            e.id as student_id,
            COUNT(CASE WHEN r1.estudiante_id IS NOT NULL AND r2.estudiante_id IS NOT NULL THEN 1 END) as reciprocidades
        FROM public.estudiantes e
        LEFT JOIN public.respuestas r1 ON e.id = r1.estudiante_id
        LEFT JOIN public.respuestas r2 ON 
            r1.grupo_id = r2.grupo_id
            AND e.id::text = ANY(string_to_array(trim(r2.respuesta_texto, '[]"'), ','))
            AND r2.estudiante_id::text = ANY(string_to_array(trim(r1.respuesta_texto, '[]"'), ','))
        JOIN public.preguntas p1 ON r1.pregunta_id = p1.id
        JOIN public.preguntas p2 ON r2.pregunta_id = p2.id
        WHERE p1.categoria = 'sociometrica' AND p2.categoria = 'sociometrica'
        GROUP BY e.id
    )
    SELECT 
        vp.student_id,
        vp.name,
        vp.Pe as popularidad,
        vr.Sp as rechazo,
        COALESCE(rc.reciprocidades, 0) as reciprocidades,
        vro.role,
        CASE 
            WHEN vp.Pe >= 70 AND vr.Sp <= 30 THEN 'popular'
            WHEN vp.Pe <= 30 AND vr.Sp >= 70 THEN 'rechazado'
            WHEN vp.Pe <= 30 AND vr.Sp <= 30 THEN 'aislado'
            WHEN vp.Pe >= 50 AND vr.Sp >= 50 THEN 'controvertido'
            ELSE 'promedio'
        END as estatus_sociometrico
    FROM public.vw_popularity vp
    JOIN public.vw_rejection vr ON vp.student_id = vr.student_id
    JOIN public.vw_roles vro ON vp.student_id = vro.student_id
    LEFT JOIN reciprocidades_calc rc ON vp.student_id = rc.student_id
    WHERE (p_curso IS NULL OR vp.course = p_curso);
END;
$$;

-- Función para calcular total de nominaciones de agresión (items 5-10)
CREATE OR REPLACE FUNCTION public.calcular_nominaciones_agresion(
    p_curso text DEFAULT NULL,
    p_genero text DEFAULT NULL
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    total_nominaciones integer;
    top_agresion_tipo text;
    top_agresion_count integer;
BEGIN
    -- Calcular total de nominaciones de agresión (items 5-10)
    SELECT COALESCE(SUM(n.value), 0)
    INTO total_nominaciones
    FROM public.nominations n
    WHERE n.item IN (5,6,7,8,9,10)
        AND (p_curso IS NULL OR n.course = p_curso);
    
    -- Obtener top forma de agresión del item 11
    SELECT r.response, COUNT(*)
    INTO top_agresion_tipo, top_agresion_count
    FROM public.answers r
    WHERE r.item = 11
        AND (p_curso IS NULL OR r.course = p_curso)
        AND (p_genero IS NULL OR r.student_id IN (
            SELECT id FROM public.students WHERE gender = p_genero
        ))
    GROUP BY r.response
    ORDER BY COUNT(*) DESC
    LIMIT 1;
    
    RETURN jsonb_build_object(
        'total_nominaciones_agresion', total_nominaciones,
        'top_forma_agresion', COALESCE(top_agresion_tipo, 'Sin datos'),
        'top_forma_count', COALESCE(top_agresion_count, 0)
    );
END;
$$;

-- Función para calcular métricas de Contexto (Módulo 4)
-- Actualizada según especificaciones: items 11, 12, 13 de tabla answers
CREATE OR REPLACE FUNCTION public.calcular_contexto(
    p_curso text DEFAULT NULL,
    p_genero text DEFAULT NULL
)
RETURNS TABLE (
    forma_agresion jsonb,
    lugar_agresion jsonb,
    frecuencia jsonb,
    analisis_cruzado jsonb
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    WITH forma_agresion_data AS (
        -- Item 11: "Las agresiones suelen ser..." - usar solo primera opción
        SELECT jsonb_agg(
            jsonb_build_object(
                'forma', SPLIT_PART(r.response, ',', 1)::text,
                'count', COUNT(*),
                'percentage', ROUND(100.0 * COUNT(*) / SUM(COUNT(*)) OVER (), 1)
            )
        ) as forma_agresion
        FROM public.answers r
        WHERE r.item = 11
            AND (p_curso IS NULL OR r.course = p_curso)
            AND (p_genero IS NULL OR r.student_id IN (
                SELECT id FROM public.students WHERE gender = p_genero
            ))
        GROUP BY SPLIT_PART(r.response, ',', 1)
    ),
    lugar_agresion_data AS (
        -- Item 12: "¿Dónde suelen ocurrir...?" - usar solo primera opción
        SELECT jsonb_agg(
            jsonb_build_object(
                'lugar', SPLIT_PART(r.response, ',', 1)::text,
                'count', COUNT(*),
                'percentage', ROUND(100.0 * COUNT(*) / SUM(COUNT(*)) OVER (), 1)
            )
        ) as lugar_agresion
        FROM public.answers r
        WHERE r.item = 12
            AND (p_curso IS NULL OR r.course = p_curso)
            AND (p_genero IS NULL OR r.student_id IN (
                SELECT id FROM public.students WHERE gender = p_genero
            ))
        GROUP BY SPLIT_PART(r.response, ',', 1)
    ),
    frecuencia_data AS (
        -- Item 13: "¿Con qué frecuencia...?" - respuesta única
        SELECT jsonb_agg(
            jsonb_build_object(
                'frecuencia', r.response,
                'count', COUNT(*),
                'percentage', ROUND(100.0 * COUNT(*) / SUM(COUNT(*)) OVER (), 1)
            )
        ) as frecuencia
        FROM public.answers r
        WHERE r.item = 13
            AND (p_curso IS NULL OR r.course = p_curso)
            AND (p_genero IS NULL OR r.student_id IN (
                SELECT id FROM public.students WHERE gender = p_genero
            ))
        GROUP BY r.response
    ),
    analisis_cruzado_data AS (
        -- Análisis cruzado: forma x lugar (items 11 x 12)
        SELECT jsonb_agg(
            jsonb_build_object(
                'forma', f.response,
                'lugar', l.response,
                'count', COUNT(*),
                'percentage', ROUND(100.0 * COUNT(*) / SUM(COUNT(*)) OVER (), 1)
            )
        ) as analisis_cruzado
        FROM public.answers f
        JOIN public.answers l ON f.student_id = l.student_id AND f.course = l.course
        WHERE f.item = 11 AND l.item = 12
            AND (p_curso IS NULL OR f.course = p_curso)
            AND (p_genero IS NULL OR f.student_id IN (
                SELECT id FROM public.students WHERE gender = p_genero
            ))
        GROUP BY f.response, l.response
    )
    SELECT 
        COALESCE(fad.forma_agresion, '[]'::jsonb),
        COALESCE(lad.lugar_agresion, '[]'::jsonb),
        COALESCE(fd.frecuencia, '[]'::jsonb),
        COALESCE(acd.analisis_cruzado, '[]'::jsonb)
    FROM forma_agresion_data fad
    CROSS JOIN lugar_agresion_data lad
    CROSS JOIN frecuencia_data fd
    CROSS JOIN analisis_cruzado_data acd;
END;
$$;

-- Función para calcular métricas Comparativas (Módulo 5)
CREATE OR REPLACE FUNCTION public.calcular_comparativas()
RETURNS TABLE (
    comparacion_curso jsonb,
    comparacion_genero jsonb,
    evolucion_temporal jsonb
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    WITH comparacion_curso_data AS (
        SELECT jsonb_agg(
            jsonb_build_object(
                'course', base.course,
                'estudiantes', base.estudiantes,
                'participacion', base.participacion,
                'bullying_pct', base.bullying_pct,
                'cohesion_pct', base.cohesion_pct,
                'seguridad_avg', base.seguridad_avg,
                'studentsAtRisk', base.bullying_pct,
                'cohesion', base.cohesion_pct,
                'safety', ROUND((base.seguridad_avg - 1) * 100.0 / 3, 1)
            )
        ) as comparacion_curso
        FROM (
            SELECT 
                s.course,
                COUNT(DISTINCT s.id) AS estudiantes,
                ROUND(COUNT(DISTINCT a.student_id) * 100.0 / COUNT(DISTINCT s.id), 1) AS participacion,
                ROUND(100.0 * COUNT(DISTINCT r.student_id) FILTER (WHERE r.role IN ('agresor','victima','agresor-victima')) / COUNT(DISTINCT s.id), 1) AS bullying_pct,
                ROUND(COALESCE(c.cohesion_pct, 0), 1) AS cohesion_pct,
                ROUND(AVG(a15.response), 2) AS seguridad_avg
            FROM public.students s 
            LEFT JOIN public.answers a ON s.id = a.student_id 
            LEFT JOIN public.answers a15 ON s.id = a15.student_id AND a15.item = 15 
            LEFT JOIN public.vw_roles r ON s.id = r.student_id 
            LEFT JOIN public.vw_cohesion c ON s.course = c.course 
            GROUP BY s.course, c.cohesion_pct
        ) base
    ),
    comparacion_genero_data AS (
        SELECT jsonb_agg(
            jsonb_build_object(
                'gender', vr.gender,
                'role', vr.role,
                'count', COUNT(*)
            )
        ) as comparacion_genero
        FROM public.vw_roles vr
        GROUP BY vr.gender, vr.role
    ),
    evolucion_temporal_data AS (
        SELECT jsonb_agg(
            jsonb_build_object(
                'test_date', r.fecha_respuesta,
                'role', vr.role,
                'count', COUNT(*)
            ) ORDER BY r.fecha_respuesta
        ) as evolucion_temporal
        FROM public.vw_roles vr
        JOIN public.respuestas r ON vr.student_id = r.estudiante_id
        GROUP BY r.fecha_respuesta, vr.role
    )
    SELECT 
        COALESCE(ccd.comparacion_curso, '[]'::jsonb),
        COALESCE(cgd.comparacion_genero, '[]'::jsonb),
        COALESCE(etd.evolucion_temporal, '[]'::jsonb)
    FROM comparacion_curso_data ccd
    CROSS JOIN comparacion_genero_data cgd
    CROSS JOIN evolucion_temporal_data etd;
END;
$$;

-- Función para generar acciones recomendadas (Módulo 6)
CREATE OR REPLACE FUNCTION public.generar_acciones_recomendadas(
    p_curso text DEFAULT NULL
)
RETURNS TABLE (
    prioridad text,
    tipo_intervencion text,
    descripcion text,
    estudiantes_objetivo text[]
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_victimas bigint;
    v_agresores bigint;
    v_cohesion numeric;
BEGIN
    -- Contar víctimas
    SELECT COUNT(*) INTO v_victimas
    FROM public.vw_victims
    WHERE (p_curso IS NULL OR course = p_curso);
    
    -- Contar agresores
    SELECT COUNT(*) INTO v_agresores
    FROM public.vw_aggressors
    WHERE (p_curso IS NULL OR course = p_curso);
    
    -- Obtener cohesión promedio
    SELECT AVG(cohesion_pct) INTO v_cohesion
    FROM public.vw_cohesion
    WHERE (p_curso IS NULL OR course = p_curso);
    
    -- Generar recomendaciones basadas en criterios
    IF v_victimas > 5 OR COALESCE(v_cohesion, 0) < 25 THEN
        RETURN QUERY SELECT 
            'CRÍTICA'::text,
            'Institucional'::text,
            'Intervención inmediata requerida: Alto nivel de victimización o cohesión crítica'::text,
            ARRAY(SELECT name FROM public.vw_victims WHERE (p_curso IS NULL OR course = p_curso));
    END IF;
    
    IF v_agresores > 3 THEN
        RETURN QUERY SELECT 
            'ALTA'::text,
            'Individual'::text,
            'Intervención con agresores identificados'::text,
            ARRAY(SELECT name FROM public.vw_aggressors WHERE (p_curso IS NULL OR course = p_curso));
    END IF;
    
    -- Siempre incluir recomendación grupal
    RETURN QUERY SELECT 
        'MEDIA'::text,
        'Grupal'::text,
        'Programa de fortalecimiento de convivencia escolar'::text,
        ARRAY[]::text[];
END;
$$;

-- Otorgar permisos para las funciones
GRANT EXECUTE ON FUNCTION public.calcular_panorama_curso TO authenticated;
GRANT EXECUTE ON FUNCTION public.calcular_sociograma TO authenticated;
GRANT EXECUTE ON FUNCTION public.calcular_nominaciones_agresion TO authenticated;
GRANT EXECUTE ON FUNCTION public.calcular_contexto TO authenticated;
GRANT EXECUTE ON FUNCTION public.calcular_comparativas TO authenticated;
GRANT EXECUTE ON FUNCTION public.generar_acciones_recomendadas TO authenticated;

-- Comentarios para documentación
COMMENT ON FUNCTION public.calcular_panorama_curso IS 'Calcula métricas del módulo Panorama del Curso: total estudiantes, en riesgo, cohesión, seguridad y alertas';
COMMENT ON FUNCTION public.calcular_sociograma IS 'Calcula métricas del módulo Sociograma: popularidad, rechazo, reciprocidades y roles';
COMMENT ON FUNCTION public.calcular_nominaciones_agresion IS 'Calcula total de nominaciones de agresión usando items 5-10 y top forma de agresión del item 11';
COMMENT ON FUNCTION public.calcular_contexto IS 'Calcula métricas del módulo Contexto: formas, lugares, frecuencia y gravedad de agresiones';
COMMENT ON FUNCTION public.calcular_comparativas IS 'Calcula métricas del módulo Comparativas: por curso, género y evolución temporal';
COMMENT ON FUNCTION public.generar_acciones_recomendadas IS 'Genera acciones recomendadas basadas en criterios de riesgo y prioridad';

-- ============================================================
-- VERIFICACIONES FINALES
-- ============================================================

-- Verificar vistas creadas
SELECT 'vw_roles' as vista, count(*) as registros FROM vw_roles
UNION ALL
SELECT 'vw_cohesion' as vista, count(*) as registros FROM vw_cohesion
UNION ALL
SELECT 'vw_victims' as vista, count(*) as registros FROM vw_victims
UNION ALL
SELECT 'vw_aggressors' as vista, count(*) as registros FROM vw_aggressors;

-- Verificar funciones creadas
SELECT routine_name, routine_type 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('calcular_comparativas', 'calcular_panorama_curso', 'calcular_sociograma', 'calcular_contexto')
ORDER BY routine_name;

