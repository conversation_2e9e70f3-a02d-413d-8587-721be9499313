-- Vista para implementar el Criterio Oficial BULL-S
-- Un estudiante está en riesgo si es nombrado por al menos el 25% de sus compañeros
-- en alguno de los siguientes roles:
-- Agresor: Items 5, 7, 9 ("<PERSON><PERSON><PERSON>", "Agresivo", "Provoca")
-- Víctima: Items 6, 8, 10 ("<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Man<PERSON>")

-- Mapeo de preguntas según criterio BULL-S:
-- q5: '0489df06-c6e7-48ec-8fb0-49469ec541ae' - "¿Quiénes son los/as más fuertes de la clase?" (Agresor)
-- q6: '775af389-a84d-4f40-8fb9-7b94cbea5498' - "¿Quiénes actúan como un/a cobarde o un bebé?" (Víctima)
-- q7: '8e0be6b5-fa0b-4215-bc60-0c91065bbaa9' - "¿Quiénes maltratan o pegan a otros/as compañeros/as?" (Agresor)
-- q8: 'eec6513e-f5b7-45b1-b21d-4e4551b3e504' - "¿Quiénes suelen ser las víctimas?" (Víctima)
-- q9: '8074fef6-4952-4857-b97c-08a1a8805522' - "¿Quiénes suelen empezar las peleas?" (Agresor)
-- q10: '00000000-0000-0000-0000-000000000010' - "¿A quiénes se les tiene manía?" (Víctima)

CREATE OR REPLACE VIEW public.vw_estudiantes_en_riesgo_bulls AS
WITH total_grupo AS (
    SELECT 
        g.id as grupo_id,
        g.nombre as curso,
        COUNT(DISTINCT e.id) AS total_estudiantes
    FROM public.grupos g
    JOIN public.estudiantes e ON e.grupo_id = g.id
    GROUP BY g.id, g.nombre
),
nominaciones AS (
    SELECT 
        e.grupo_id,
        g.nombre as curso,
        nominado.id AS student_id,
        nominado.nombre_completo AS student_name,
        -- Contar nominaciones como agresor (items 5, 7, 9)
        SUM(CASE 
            WHEN r.pregunta_id IN (
                '0489df06-c6e7-48ec-8fb0-49469ec541ae', -- q5: fuertes
                '8e0be6b5-fa0b-4215-bc60-0c91065bbaa9', -- q7: maltratan
                '8074fef6-4952-4857-b97c-08a1a8805522'  -- q9: empezar peleas
            ) THEN 1 
            ELSE 0 
        END) AS n_agr,
        -- Contar nominaciones como víctima (items 6, 8, 10)
        SUM(CASE 
            WHEN r.pregunta_id IN (
                '775af389-a84d-4f40-8fb9-7b94cbea5498', -- q6: cobarde
                'eec6513e-f5b7-45b1-b21d-4e4551b3e504', -- q8: víctimas
                '00000000-0000-0000-0000-000000000010'  -- q10: manía
            ) THEN 1 
            ELSE 0 
        END) AS n_vic
    FROM public.respuestas_cuestionario r
    JOIN public.estudiantes e ON r.estudiante_id = e.id
    JOIN public.grupos g ON e.grupo_id = g.id
    CROSS JOIN LATERAL (
        SELECT DISTINCT 
            est.id,
            est.nombre_completo
        FROM public.estudiantes est,
        LATERAL jsonb_array_elements_text(r.respuesta_texto::jsonb) AS nominado_id
        WHERE est.id::text = nominado_id
        AND est.grupo_id = e.grupo_id
    ) AS nominado
    WHERE r.respuesta_texto IS NOT NULL
    AND r.respuesta_texto != ''
    AND r.respuesta_texto::text ~ '^\[.*\]$' -- Solo arrays JSON válidos
    GROUP BY e.grupo_id, g.nombre, nominado.id, nominado.nombre_completo
),
riesgo AS (
    SELECT 
        n.grupo_id,
        n.curso,
        n.student_id,
        n.student_name,
        t.total_estudiantes,
        n.n_agr,
        n.n_vic,
        -- Calcular porcentajes
        ROUND(100.0 * n.n_agr / NULLIF(t.total_estudiantes, 0), 1) AS pct_agr,
        ROUND(100.0 * n.n_vic / NULLIF(t.total_estudiantes, 0), 1) AS pct_vic
    FROM nominaciones n
    JOIN total_grupo t ON n.grupo_id = t.grupo_id
)
SELECT 
    r.grupo_id,
    r.curso,
    r.student_id,
    r.student_name,
    r.total_estudiantes,
    r.n_agr as nominaciones_agresor,
    r.n_vic as nominaciones_victima,
    r.pct_agr as porcentaje_agresor,
    r.pct_vic as porcentaje_victima,
    -- Clasificación según criterio BULL-S (≥25%)
    CASE 
        WHEN r.pct_agr >= 25 AND r.pct_vic >= 25 THEN 'Agresor-Víctima'
        WHEN r.pct_agr >= 25 THEN 'Agresor'
        WHEN r.pct_vic >= 25 THEN 'Víctima'
        ELSE 'Sin Riesgo'
    END AS rol_riesgo,
    -- Indicadores booleanos
    (r.pct_agr >= 25) AS es_agresor,
    (r.pct_vic >= 25) AS es_victima,
    (r.pct_agr >= 25 OR r.pct_vic >= 25) AS en_riesgo,
    NOW() as fecha_calculo
FROM riesgo r
ORDER BY r.curso, r.rol_riesgo, r.pct_agr DESC, r.pct_vic DESC;

-- Comentarios para documentar la vista
COMMENT ON VIEW public.vw_estudiantes_en_riesgo_bulls IS 'Vista que implementa el criterio oficial BULL-S para identificar estudiantes en riesgo de bullying';

-- Vista resumen por curso
CREATE OR REPLACE VIEW public.vw_resumen_bulls_por_curso AS
SELECT 
    curso,
    grupo_id,
    total_estudiantes,
    COUNT(*) FILTER (WHERE rol_riesgo = 'Agresor') as total_agresores,
    COUNT(*) FILTER (WHERE rol_riesgo = 'Víctima') as total_victimas,
    COUNT(*) FILTER (WHERE rol_riesgo = 'Agresor-Víctima') as total_agresor_victima,
    COUNT(*) FILTER (WHERE rol_riesgo = 'Sin Riesgo') as total_sin_riesgo,
    COUNT(*) FILTER (WHERE en_riesgo = true) as total_en_riesgo,
    ROUND(100.0 * COUNT(*) FILTER (WHERE en_riesgo = true) / NULLIF(total_estudiantes, 0), 1) as porcentaje_riesgo,
    -- Alerta si ≥25% de estudiantes están en riesgo
    CASE 
        WHEN (100.0 * COUNT(*) FILTER (WHERE en_riesgo = true) / NULLIF(total_estudiantes, 0)) >= 25 
        THEN 'CRÍTICO' 
        ELSE 'NORMAL' 
    END as nivel_alerta,
    NOW() as fecha_calculo
FROM public.vw_estudiantes_en_riesgo_bulls
GROUP BY curso, grupo_id, total_estudiantes
ORDER BY porcentaje_riesgo DESC;

COMMENT ON VIEW public.vw_resumen_bulls_por_curso IS 'Vista resumen que muestra estadísticas del criterio BULL-S por curso con alertas automáticas';

-- Función para obtener estudiantes en riesgo por grupo
CREATE OR REPLACE FUNCTION public.get_estudiantes_riesgo_bulls(
    p_grupo_id UUID DEFAULT NULL,
    p_curso TEXT DEFAULT NULL
)
RETURNS TABLE(
    grupo_id UUID,
    curso TEXT,
    student_id UUID,
    student_name TEXT,
    rol_riesgo TEXT,
    porcentaje_agresor NUMERIC,
    porcentaje_victima NUMERIC,
    en_riesgo BOOLEAN
)
LANGUAGE SQL
STABLE
AS $$
    SELECT 
        v.grupo_id,
        v.curso,
        v.student_id,
        v.student_name,
        v.rol_riesgo,
        v.porcentaje_agresor,
        v.porcentaje_victima,
        v.en_riesgo
    FROM public.vw_estudiantes_en_riesgo_bulls v
    WHERE (p_grupo_id IS NULL OR v.grupo_id = p_grupo_id)
    AND (p_curso IS NULL OR v.curso = p_curso)
    ORDER BY v.curso, v.rol_riesgo, v.porcentaje_agresor DESC, v.porcentaje_victima DESC;
$$;

COMMENT ON FUNCTION public.get_estudiantes_riesgo_bulls IS 'Función para obtener estudiantes en riesgo según criterio BULL-S filtrado por grupo o curso';

-- Índices para optimizar consultas
CREATE INDEX IF NOT EXISTS idx_respuestas_cuestionario_pregunta_bulls 
ON public.respuestas_cuestionario(pregunta_id) 
WHERE pregunta_id IN (
    '0489df06-c6e7-48ec-8fb0-49469ec541ae', -- q5: fuertes
    '775af389-a84d-4f40-8fb9-7b94cbea5498', -- q6: cobarde
    '8e0be6b5-fa0b-4215-bc60-0c91065bbaa9', -- q7: maltratan
    'eec6513e-f5b7-45b1-b21d-4e4551b3e504', -- q8: víctimas
    '8074fef6-4952-4857-b97c-08a1a8805522', -- q9: empezar peleas
    '00000000-0000-0000-0000-000000000010'  -- q10: manía
);

CREATE INDEX IF NOT EXISTS idx_estudiantes_grupo_bulls 
ON public.estudiantes(grupo_id, id);