{"version": 2, "name": "bulls-analysis-platform", "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/(.*)", "dest": "/index.html"}], "env": {"VITE_APP_NAME": "BULL-S Analysis Platform", "VITE_APP_VERSION": "2.0.0"}, "functions": {"app/api/**/*.js": {"runtime": "nodejs18.x"}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}