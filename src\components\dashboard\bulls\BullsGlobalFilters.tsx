import React from 'react';
import { useBullsDashboard } from '../../../contexts/BullsDashboardContext';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../ui/select';
import { Button } from '../../ui/button';
import { DateRangeFilter } from '../filters/DateRangeFilter';
import { Badge } from '../../ui/badge';
import { X, Printer } from 'lucide-react';

// Definir la configuración de los filtros para hacer el componente más mantenible
const filterConfigs = [
  {
    key: 'groupId' as const,
    label: 'Curso',
    placeholder: 'Seleccionar curso',
    options: [
      { value: '6B', label: '6ºB' },
      { value: '8A', label: '8ºA' },
      { value: '8B', label: '8ºB' },
    ],
  },
  {
    key: 'patientType' as const,
    label: '<PERSON><PERSON><PERSON>',
    placeholder: '<PERSON><PERSON><PERSON><PERSON><PERSON> género',
    options: [
      { value: 'F', label: 'Fe<PERSON><PERSON>' },
      { value: 'M', label: 'Ma<PERSON><PERSON><PERSON>' },
    ],
  },
  {
    key: 'role' as const,
    label: 'Rol',
    placeholder: 'Se<PERSON>ccionar rol',
    options: [
      { value: 'agresor', label: 'Agresor' },
      { value: 'victima', label: 'Víctima' },
      { value: 'observador', label: 'Observador' },
      { value: 'no_rol', label: 'Sin rol específico' },
    ],
  },
];

export const BullsGlobalFilters = () => {
  // Usar el contexto que ya está disponible en BullsDashboard
  const { filters, updateFilter, setDateRange, clearFilters } = useBullsDashboard();
  
  // Función helper para obtener el valor actual del filtro
  const getFilterValue = (filterValue: string | undefined) => {
    return filterValue || 'all';
  };



  // Función helper para manejar cambios en los filtros
  const handleFilterChange = (key: keyof typeof filters, value: string) => {
    const newValue = value === 'all' ? undefined : value;
    updateFilter(key, newValue);
  };

  // Función para obtener la etiqueta de un valor de filtro para mostrar en las insignias
  const getLabelForValue = (filterKey: string, value: string) => {
    const config = filterConfigs.find(f => f.key === filterKey);
    const option = config?.options.find(o => o.value === value);
    return option?.label || value;
  };

  // Verificar si se puede mostrar el botón de impresión
  const canShowPrintButton = () => {
    const selectedCourse = filters.groupId;
    return selectedCourse && ['6B', '8A', '8B'].includes(selectedCourse);
  };

  // Función para capturar estilos CSS necesarios
  const captureStyles = () => {
    const styles = [];
    // Capturar estilos de las hojas de estilo
    for (let i = 0; i < document.styleSheets.length; i++) {
      try {
        const styleSheet = document.styleSheets[i];
        if (styleSheet.cssRules) {
          for (let j = 0; j < styleSheet.cssRules.length; j++) {
            const rule = styleSheet.cssRules[j];
            if (rule.cssText) {
              styles.push(rule.cssText);
            }
          }
        }
      } catch (e) {
        // Ignorar errores de CORS
      }
    }
    return styles.join('\n');
  };

  // Función para clonar elemento con estilos
  const cloneElementWithStyles = (element: Element) => {
    const clone = element.cloneNode(true) as Element;
    
    // Copiar estilos computados para SVG y elementos gráficos
    const copyComputedStyles = (original: Element, cloned: Element) => {
      const originalElements = original.querySelectorAll('*');
      const clonedElements = cloned.querySelectorAll('*');
      
      for (let i = 0; i < originalElements.length; i++) {
        const originalEl = originalElements[i] as HTMLElement;
        const clonedEl = clonedElements[i] as HTMLElement;
        
        if (originalEl && clonedEl) {
          const computedStyle = window.getComputedStyle(originalEl);
          
          // Copiar estilos importantes para gráficos
          const importantStyles = [
            'fill', 'stroke', 'stroke-width', 'opacity', 'transform',
            'font-family', 'font-size', 'font-weight', 'color',
            'background-color', 'border', 'padding', 'margin',
            'width', 'height', 'display', 'position'
          ];
          
          importantStyles.forEach(prop => {
            const value = computedStyle.getPropertyValue(prop);
            if (value) {
              clonedEl.style.setProperty(prop, value);
            }
          });
        }
      }
    };
    
    copyComputedStyles(element, clone);
    return clone;
  };

  // Función para manejar la impresión
  const handlePrint = () => {
    const selectedCourse = filters.groupId;
    if (!selectedCourse) return;

    // Crear una nueva ventana para la impresión
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    // Obtener todos los módulos del dashboard
    const kpiCards = document.querySelector('[data-testid="kpi-cards"]') || document.querySelector('.kpi-cards');
    const detailPanels = document.querySelector('[data-testid="detail-panels"]') || document.querySelector('.detail-panels');
    
    // Clonar elementos con estilos
    const kpiCardsClone = kpiCards ? cloneElementWithStyles(kpiCards) : null;
    const detailPanelsClone = detailPanels ? cloneElementWithStyles(detailPanels) : null;
    
    // Capturar estilos CSS
    const capturedStyles = captureStyles();
    
    // Crear el contenido HTML para imprimir
    const printContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Dashboard BULL-S - Curso ${getLabelForValue('groupId', selectedCourse)}</title>
          <meta charset="utf-8">
          <style>
            ${capturedStyles}
            
            /* Estilos específicos para impresión */
            body { 
              font-family: Arial, sans-serif; 
              margin: 20px; 
              color: #333;
              background: white;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 2px solid #e5e7eb;
              padding-bottom: 20px;
            }
            .header h1 {
              color: #1f2937;
              margin-bottom: 10px;
            }
            .header h2 {
              color: #6b7280;
              font-weight: normal;
            }
            .module {
              margin-bottom: 40px;
              page-break-inside: avoid;
            }
            .module-title {
              font-size: 18px;
              font-weight: bold;
              color: #1f2937;
              margin-bottom: 15px;
              border-left: 4px solid #3b82f6;
              padding-left: 15px;
            }
            .panel-content {
              border: 1px solid #e5e7eb;
              border-radius: 8px;
              padding: 20px;
              background: white;
            }
            
            /* Estilos para gráficos SVG */
            svg {
              max-width: 100% !important;
              height: auto !important;
            }
            
            .recharts-wrapper {
              width: 100% !important;
            }
            
            /* Estilos para tablas */
            table {
              width: 100%;
              border-collapse: collapse;
              margin: 10px 0;
            }
            
            th, td {
              border: 1px solid #e5e7eb;
              padding: 8px;
              text-align: left;
            }
            
            th {
              background-color: #f9fafb;
              font-weight: bold;
            }
            
            /* Estilos para badges y elementos UI */
            .badge {
              display: inline-block;
              padding: 2px 8px;
              border-radius: 4px;
              font-size: 12px;
              font-weight: 500;
            }
            
            /* Ocultar elementos no necesarios para impresión */
            button, .print-hidden {
              display: none !important;
            }
            
            @media print {
              body { margin: 0; }
              .module { page-break-after: always; }
              .module:last-child { page-break-after: auto; }
              
              /* Asegurar que los gráficos se impriman correctamente */
              svg, canvas {
                max-width: 100% !important;
                height: auto !important;
              }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Dashboard BULL-S</h1>
            <h2>Análisis integral de convivencia escolar - Curso ${getLabelForValue('groupId', selectedCourse)}</h2>
            <p>Fecha de generación: ${new Date().toLocaleDateString('es-ES', { 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}</p>
          </div>
          
          <div class="module">
            <div class="module-title">📊 Indicadores Clave de Rendimiento (KPIs)</div>
            <div class="panel-content">
              ${kpiCardsClone ? kpiCardsClone.outerHTML : '<p>No hay datos de KPIs disponibles</p>'}
            </div>
          </div>
          
          <div class="module">
             <div class="module-title">📈 Módulos de Análisis Detallado</div>
             <div class="panel-content">
               ${detailPanelsClone ? detailPanelsClone.outerHTML : '<p>No hay datos de paneles disponibles</p>'}
             </div>
           </div>
         </body>
       </html>
     `;

     // Escribir el contenido en la nueva ventana
     printWindow.document.write(printContent);
     printWindow.document.close();
     
     // Esperar a que se cargue el contenido antes de imprimir
     setTimeout(() => {
       printWindow.focus();
       printWindow.print();
     }, 1000);
   };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <h3 className="text-lg font-semibold text-gray-900">Filtros Globales</h3>
          {/* Indicador de filtros activos */}
          {(filters.groupId || filters.patientType || filters.role || filters.startDate || filters.endDate) && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              Filtros activos
            </span>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {/* Botón de impresión - solo visible para cursos específicos */}
          {canShowPrintButton() && (
            <Button
              variant="default"
              size="sm"
              onClick={handlePrint}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Printer className="h-4 w-4 mr-2" />
              Imprimir Módulos
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={clearFilters}
            className="text-gray-600 hover:text-gray-900"
          >
            Limpiar Filtros
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Filtros dinámicos basados en configuración */}
        {filterConfigs.map(filter => (
          <div className="space-y-2" key={filter.key}>
            <label className="text-sm font-medium text-gray-700">{filter.label}</label>
            <Select
              value={getFilterValue(filters[filter.key])}
              onValueChange={(value: string) => handleFilterChange(filter.key, value)}
            >
              <SelectTrigger>
                <SelectValue placeholder={filter.placeholder} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                {filter.options.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        ))}

        {/* Filtro de Período */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Período</label>
          <DateRangeFilter
            startDate={filters.startDate}
            endDate={filters.endDate}
            onDateRangeChange={setDateRange}
            onClear={() => setDateRange('', '')}
          />
        </div>
      </div>

      {/* Filtros activos */}
      <div className="mt-4 flex flex-wrap gap-2">
        {filterConfigs.map(filter => {
          const filterValue = filters[filter.key];
          if (!filterValue) return null;
          
          return (
            <div key={filter.key} className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold bg-secondary text-secondary-foreground">
              {filter.label}: {getLabelForValue(filter.key, filterValue)}
              <X
                className="h-3 w-3 cursor-pointer ml-1"
                onClick={() => updateFilter(filter.key, undefined)}
              />
            </div>
          );
        })}
        {(filters.startDate || filters.endDate) && (
          <div className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold bg-secondary text-secondary-foreground">
            Período personalizado
            <X 
              className="h-3 w-3 cursor-pointer ml-1" 
              onClick={() => setDateRange('', '')}
            />
          </div>
        )}
      </div>


    </div>
  );
};