/**
 * Script para importar datos reales del BULL-S al sistema optimizado
 * Migra datos desde los archivos CSV a las tablas optimizadas
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import csv from 'csv-parser';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Configuración de Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'http://localhost:54321';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';

const supabase = createClient(supabaseUrl, supabaseKey);

// Mapeo de IDs de preguntas sociométricas
const PREGUNTAS_SOCIOMETRICAS = {
  'q1': 'd90ddd09-3878-4efc-9059-7279570157bc', // ¿Con qué compañeros/as te gusta estar más? (elección)
  'q2': '47b56067-0c8c-4565-b645-80348852907f', // ¿Con qué compañeros/as te gusta estar menos? (rechazo)
  'q3': 'dae67e87-db3e-4637-ace1-f1148f1d7d69', // ¿Quiénes suelen intimidar o maltratar? (agresores)
  'q4': 'd2888d67-9878-4cdf-8a58-592c251c1cb6', // ¿Quiénes suelen ser víctimas? (víctimas)
  'q6': '775af389-a84d-4f40-8fb9-7b94cbea5498'  // ¿Con quién te gusta trabajar? (colaboración)
};

// Mapeo de tipos de nominación
const TIPOS_NOMINACION = {
  'd90ddd09-3878-4efc-9059-7279570157bc': 'eleccion',
  '47b56067-0c8c-4565-b645-80348852907f': 'rechazo',
  'dae67e87-db3e-4637-ace1-f1148f1d7d69': 'agresion',
  'd2888d67-9878-4cdf-8a58-592c251c1cb6': 'victimizacion',
  '775af389-a84d-4f40-8fb9-7b94cbea5498': 'colaboracion'
};

/**
 * Lee un archivo CSV y devuelve los datos como array
 */
function leerCSV(rutaArchivo) {
  return new Promise((resolve, reject) => {
    const datos = [];
    fs.createReadStream(rutaArchivo)
      .pipe(csv())
      .on('data', (fila) => datos.push(fila))
      .on('end', () => resolve(datos))
      .on('error', reject);
  });
}

/**
 * Procesa las respuestas sociométricas y las convierte al formato optimizado
 */
function procesarRespuestasSociometricas(respuestas) {
  const nominaciones = [];
  
  respuestas.forEach(respuesta => {
    const { estudiante_id, grupo_id, pregunta_id, respuesta_texto } = respuesta;
    
    // Solo procesar preguntas sociométricas
    if (!TIPOS_NOMINACION[pregunta_id]) {
      return;
    }
    
    // Parsear la respuesta JSON que contiene los IDs de estudiantes nominados
    let estudiantesNominados = [];
    try {
      if (respuesta_texto && respuesta_texto.startsWith('[')) {
        estudiantesNominados = JSON.parse(respuesta_texto);
      }
    } catch (error) {
      console.warn(`Error parseando respuesta sociométrica para estudiante ${estudiante_id}:`, error);
      return;
    }
    
    // Crear nominaciones individuales
    estudiantesNominados.forEach(nominado_id => {
      nominaciones.push({
        estudiante_nominador_id: estudiante_id,
        estudiante_nominado_id: nominado_id,
        grupo_id: grupo_id,
        tipo_nominacion: TIPOS_NOMINACION[pregunta_id],
        pregunta_id: pregunta_id,
        fecha_nominacion: respuesta.fecha_respuesta || new Date().toISOString()
      });
    });
  });
  
  return nominaciones;
}

/**
 * Importa estudiantes desde el CSV
 */
async function importarEstudiantes() {
  console.log('📚 Importando estudiantes...');
  
  const rutaEstudiantes = path.join(__dirname, '..', 'respuestas bulls-s', 'estudiantes_rows.csv');
  const estudiantes = await leerCSV(rutaEstudiantes);
  
  console.log(`Encontrados ${estudiantes.length} estudiantes`);
  
  // Verificar si ya existen estudiantes
  const { data: estudiantesExistentes } = await supabase
    .from('estudiantes')
    .select('id')
    .limit(1);
  
  if (estudiantesExistentes && estudiantesExistentes.length > 0) {
    console.log('✅ Los estudiantes ya están importados');
    return;
  }
  
  // Importar estudiantes en lotes
  const LOTE_SIZE = 50;
  for (let i = 0; i < estudiantes.length; i += LOTE_SIZE) {
    const lote = estudiantes.slice(i, i + LOTE_SIZE);
    
    const { error } = await supabase
      .from('estudiantes')
      .insert(lote);
    
    if (error) {
      console.error('Error importando lote de estudiantes:', error);
    } else {
      console.log(`✅ Importado lote ${Math.floor(i/LOTE_SIZE) + 1}/${Math.ceil(estudiantes.length/LOTE_SIZE)}`);
    }
  }
  
  console.log('✅ Estudiantes importados correctamente');
}

/**
 * Importa respuestas desde el CSV
 */
async function importarRespuestas() {
  console.log('📝 Importando respuestas...');
  
  const rutaRespuestas = path.join(__dirname, '..', 'respuestas bulls-s', 'respuestas_rows.csv');
  const respuestas = await leerCSV(rutaRespuestas);
  
  console.log(`Encontradas ${respuestas.length} respuestas`);
  
  // Verificar si ya existen respuestas
  const { data: respuestasExistentes } = await supabase
    .from('respuestas')
    .select('id')
    .limit(1);
  
  if (respuestasExistentes && respuestasExistentes.length > 0) {
    console.log('✅ Las respuestas ya están importadas');
    return;
  }
  
  // Importar respuestas en lotes
  const LOTE_SIZE = 100;
  for (let i = 0; i < respuestas.length; i += LOTE_SIZE) {
    const lote = respuestas.slice(i, i + LOTE_SIZE);
    
    const { error } = await supabase
      .from('respuestas')
      .insert(lote);
    
    if (error) {
      console.error('Error importando lote de respuestas:', error);
    } else {
      console.log(`✅ Importado lote ${Math.floor(i/LOTE_SIZE) + 1}/${Math.ceil(respuestas.length/LOTE_SIZE)}`);
    }
  }
  
  console.log('✅ Respuestas importadas correctamente');
}

/**
 * Migra datos sociométricos al sistema optimizado
 */
async function migrarDatosSociometricos() {
  console.log('🔄 Migrando datos sociométricos al sistema optimizado...');
  
  // Verificar si ya existen nominaciones
  const { data: nominacionesExistentes } = await supabase
    .from('nominaciones_sociometricas')
    .select('id')
    .limit(1);
  
  if (nominacionesExistentes && nominacionesExistentes.length > 0) {
    console.log('✅ Las nominaciones sociométricas ya están migradas');
    return;
  }
  
  // Obtener respuestas sociométricas
  const { data: respuestas, error } = await supabase
    .from('respuestas')
    .select('*')
    .in('pregunta_id', Object.keys(TIPOS_NOMINACION));
  
  if (error) {
    console.error('Error obteniendo respuestas sociométricas:', error);
    return;
  }
  
  console.log(`Procesando ${respuestas.length} respuestas sociométricas`);
  
  // Procesar y convertir respuestas
  const nominaciones = procesarRespuestasSociometricas(respuestas);
  
  console.log(`Generadas ${nominaciones.length} nominaciones individuales`);
  
  // Insertar nominaciones en lotes
  const LOTE_SIZE = 100;
  for (let i = 0; i < nominaciones.length; i += LOTE_SIZE) {
    const lote = nominaciones.slice(i, i + LOTE_SIZE);
    
    const { error } = await supabase
      .from('nominaciones_sociometricas')
      .insert(lote);
    
    if (error) {
      console.error('Error insertando lote de nominaciones:', error);
    } else {
      console.log(`✅ Insertado lote ${Math.floor(i/LOTE_SIZE) + 1}/${Math.ceil(nominaciones.length/LOTE_SIZE)}`);
    }
  }
  
  console.log('✅ Datos sociométricos migrados correctamente');
}

/**
 * Verifica la salud del sistema después de la importación
 */
async function verificarSaludSistema() {
  console.log('🔍 Verificando salud del sistema...');
  
  try {
    // Verificar vistas
    const { data: vistaSociometrica } = await supabase
      .from('view_sociometric_stats')
      .select('*')
      .limit(1);
    
    const { data: vistaConvivencia } = await supabase
      .from('view_convivencia_metrics')
      .select('*')
      .limit(1);
    
    // Contar datos
    const { count: totalEstudiantes } = await supabase
      .from('estudiantes')
      .select('*', { count: 'exact', head: true });
    
    const { count: totalRespuestas } = await supabase
      .from('respuestas')
      .select('*', { count: 'exact', head: true });
    
    const { count: totalNominaciones } = await supabase
      .from('nominaciones_sociometricas')
      .select('*', { count: 'exact', head: true });
    
    console.log('📊 Estadísticas del sistema:');
    console.log(`   - Estudiantes: ${totalEstudiantes}`);
    console.log(`   - Respuestas: ${totalRespuestas}`);
    console.log(`   - Nominaciones sociométricas: ${totalNominaciones}`);
    console.log(`   - Vista sociométrica: ${vistaSociometrica ? '✅ Funcional' : '❌ Error'}`);
    console.log(`   - Vista convivencia: ${vistaConvivencia ? '✅ Funcional' : '❌ Error'}`);
    
    // Probar función de métricas
    const { data: metricas, error: errorMetricas } = await supabase
      .rpc('calcular_metricas_grupo', { p_grupo_id: 'ffd31ec0-a1c4-4530-9db0-983f93cb239f' });
    
    if (errorMetricas) {
      console.log('❌ Error en función de métricas:', errorMetricas.message);
    } else {
      console.log('✅ Función de métricas operativa');
    }
    
  } catch (error) {
    console.error('Error verificando salud del sistema:', error);
  }
}

/**
 * Función principal
 */
async function main() {
  console.log('🚀 Iniciando importación de datos reales del BULL-S');
  console.log('=' .repeat(60));
  
  try {
    // 1. Importar estudiantes
    await importarEstudiantes();
    
    // 2. Importar respuestas
    await importarRespuestas();
    
    // 3. Migrar datos sociométricos
    await migrarDatosSociometricos();
    
    // 4. Verificar salud del sistema
    await verificarSaludSistema();
    
    console.log('=' .repeat(60));
    console.log('🎉 Importación completada exitosamente');
    console.log('El sistema optimizado ahora tiene datos reales del BULL-S');
    
  } catch (error) {
    console.error('❌ Error durante la importación:', error);
    process.exit(1);
  }
}

// Ejecutar si es llamado directamente
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export {
  importarEstudiantes,
  importarRespuestas,
  migrarDatosSociometricos,
  verificarSaludSistema
};