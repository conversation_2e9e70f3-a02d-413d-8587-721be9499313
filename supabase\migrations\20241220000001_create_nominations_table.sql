-- Migración para crear tabla de nominaciones sociométricas
-- Esta tabla optimiza el almacenamiento de elecciones y rechazos entre estudiantes

-- Primero, agregar columnas a la tabla preguntas para categorización
ALTER TABLE public.preguntas 
ADD COLUMN IF NOT EXISTS categoria TEXT DEFAULT 'general',
ADD COLUMN IF NOT EXISTS tipo_pregunta TEXT DEFAULT 'multiple_choice';

-- Actualizar preguntas existentes con categorías apropiadas
UPDATE public.preguntas 
SET categoria = 'sociometrica', tipo_pregunta = 'eleccion'
WHERE pregunta ILIKE '%con quien%' OR pregunta ILIKE '%elegir%' OR pregunta ILIKE '%trabajar%';

UPDATE public.preguntas 
SET categoria = 'sociometrica', tipo_pregunta = 'rechazo'
WHERE pregunta ILIKE '%no elegir%' OR pregunta ILIKE '%evitar%' OR pregunta ILIKE '%rechazar%';

UPDATE public.preguntas 
SET categoria = 'bullying'
WHERE pregunta ILIKE '%agresión%' OR pregunta ILIKE '%intimidación%' OR pregunta ILIKE '%acoso%';

-- Crear tabla de nominaciones sociométricas
CREATE TABLE IF NOT EXISTS public.nominaciones_sociometricas (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    evaluador_id uuid NOT NULL REFERENCES public.estudiantes(id) ON DELETE CASCADE,
    evaluado_id uuid NOT NULL REFERENCES public.estudiantes(id) ON DELETE CASCADE,
    pregunta_id uuid NOT NULL REFERENCES public.preguntas(id) ON DELETE CASCADE,
    tipo_nominacion TEXT NOT NULL CHECK (tipo_nominacion IN ('eleccion', 'rechazo')),
    grupo_id uuid NOT NULL REFERENCES public.grupos(id) ON DELETE CASCADE,
    fecha_creacion TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Evitar auto-nominaciones
    CONSTRAINT no_self_nomination CHECK (evaluador_id != evaluado_id),
    
    -- Evitar nominaciones duplicadas
    CONSTRAINT unique_nomination UNIQUE (evaluador_id, evaluado_id, pregunta_id)
);

-- Crear índices para optimizar consultas
CREATE INDEX IF NOT EXISTS idx_nominaciones_evaluador ON public.nominaciones_sociometricas(evaluador_id);
CREATE INDEX IF NOT EXISTS idx_nominaciones_evaluado ON public.nominaciones_sociometricas(evaluado_id);
CREATE INDEX IF NOT EXISTS idx_nominaciones_grupo ON public.nominaciones_sociometricas(grupo_id);
CREATE INDEX IF NOT EXISTS idx_nominaciones_tipo ON public.nominaciones_sociometricas(tipo_nominacion);

-- Habilitar RLS
ALTER TABLE public.nominaciones_sociometricas ENABLE ROW LEVEL SECURITY;

-- Política para que los usuarios solo vean datos de su institución
CREATE POLICY "Users can view nominations from their institution" ON public.nominaciones_sociometricas
    FOR SELECT USING (
        grupo_id IN (
            SELECT g.id FROM public.grupos g
            JOIN public.instituciones i ON g.institucion_id = i.id
            WHERE i.id = auth.jwt() ->> 'institucion_id'
        )
    );

-- Política para insertar nominaciones
CREATE POLICY "Users can insert nominations for their institution" ON public.nominaciones_sociometricas
    FOR INSERT WITH CHECK (
        grupo_id IN (
            SELECT g.id FROM public.grupos g
            JOIN public.instituciones i ON g.institucion_id = i.id
            WHERE i.id = auth.jwt() ->> 'institucion_id'
        )
    );