import React from 'react';

interface DashboardGridProps {
  children: React.ReactNode;
  columns?: number;
  gap?: string;
}

export const DashboardGrid: React.FC<DashboardGridProps> = ({
  children,
  columns = 12,
  gap = 'gap-4'
}) => {
  const getGridClass = () => {
    const baseClass = `grid ${gap}`;
    
    switch (columns) {
      case 1:
        return `${baseClass} grid-cols-1`;
      case 2:
        return `${baseClass} grid-cols-1 md:grid-cols-2`;
      case 3:
        return `${baseClass} grid-cols-1 md:grid-cols-3`;
      case 4:
        return `${baseClass} grid-cols-1 sm:grid-cols-2 lg:grid-cols-4`;
      case 6:
        return `${baseClass} grid-cols-2 md:grid-cols-3 lg:grid-cols-6`;
      case 12:
        return `${baseClass} grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-12`;
      default:
        return `${baseClass} grid-cols-1 md:grid-cols-2 lg:grid-cols-${columns}`;
    }
  };

  return (
    <div className={getGridClass()}>
      {children}
    </div>
  );
};

interface GridItemProps {
  children: React.ReactNode;
  span?: number;
  className?: string;
}

export const GridItem: React.FC<GridItemProps> = ({
  children,
  span = 1,
  className = ''
}) => {
  const getSpanClass = () => {
    if (span === 'full') return 'col-span-full';
    return `col-span-1 md:col-span-${Math.min(span, 2)} lg:col-span-${span}`;
  };

  return (
    <div className={`${getSpanClass()} ${className}`}>
      {children}
    </div>
  );
};