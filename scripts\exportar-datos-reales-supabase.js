import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import csv from 'csv-parser';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Cargar variables de entorno
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuración de Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Variables de entorno VITE_SUPABASE_URL y VITE_SUPABASE_ANON_KEY no configuradas');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Función para leer CSV
function leerCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', reject);
  });
}

// Función para insertar estudiantes
async function insertarEstudiantes(estudiantes) {
  console.log(`Insertando ${estudiantes.length} estudiantes...`);
  
  const estudiantesParaInsertar = estudiantes.map(est => ({
    id: est.id,
    nombre_estudiante: est.nombre_estudiante,
    apellido_estudiante: est.apellido_estudiante,
    genero: est.genero,
    codigo_anonimizado: est.codigo_anonimizado,
    grado: est.grado,
    institucion_id: est.institucion_id,
    fecha_creacion: est.fecha_creacion,
    fecha_actualizacion: est.fecha_actualizacion,
    edad: parseInt(est.edad),
    grupo_id: est.grupo_id,
    numero_documento: est.numero_documento
  }));

  const { data, error } = await supabase
    .from('estudiantes')
    .upsert(estudiantesParaInsertar, { onConflict: 'id' });

  if (error) {
    console.error('Error insertando estudiantes:', error);
    throw error;
  }

  console.log(`✅ ${estudiantesParaInsertar.length} estudiantes insertados correctamente`);
  return data;
}

// Función para insertar respuestas
async function insertarRespuestas(respuestas) {
  console.log(`Insertando ${respuestas.length} respuestas...`);
  
  const respuestasParaInsertar = respuestas.map(resp => ({
    id: resp.id,
    estudiante_id: resp.estudiante_id,
    grupo_id: resp.grupo_id,
    pregunta_id: resp.pregunta_id,
    opcion_respuesta_id: resp.opcion_respuesta_id || null,
    respuesta_texto: resp.respuesta_texto,
    fecha_respuesta: resp.fecha_respuesta,
    fecha_creacion: resp.fecha_creacion,
    fecha_actualizacion: resp.fecha_actualizacion
  }));

  // Insertar en lotes de 100 para evitar límites de Supabase
  const batchSize = 100;
  let insertedCount = 0;

  for (let i = 0; i < respuestasParaInsertar.length; i += batchSize) {
    const batch = respuestasParaInsertar.slice(i, i + batchSize);
    
    const { data, error } = await supabase
      .from('respuestas')
      .upsert(batch, { onConflict: 'id' });

    if (error) {
      console.error(`Error insertando lote ${Math.floor(i/batchSize) + 1}:`, error);
      throw error;
    }

    insertedCount += batch.length;
    console.log(`✅ Lote ${Math.floor(i/batchSize) + 1} insertado (${insertedCount}/${respuestasParaInsertar.length})`);
  }

  console.log(`✅ ${insertedCount} respuestas insertadas correctamente`);
  return insertedCount;
}

// Función para verificar conexión a Supabase
async function verificarConexion() {
  try {
    const { data, error } = await supabase
      .from('estudiantes')
      .select('count', { count: 'exact', head: true });

    if (error) {
      console.error('Error conectando a Supabase:', error);
      return false;
    }

    console.log('✅ Conexión a Supabase exitosa');
    return true;
  } catch (error) {
    console.error('Error verificando conexión:', error);
    return false;
  }
}

// Función principal
async function main() {
  try {
    console.log('🚀 Iniciando exportación de datos reales a Supabase...');
    
    // Verificar conexión
    const conexionOk = await verificarConexion();
    if (!conexionOk) {
      console.error('❌ No se pudo conectar a Supabase. Verifica las variables de entorno.');
      return;
    }

    // Rutas de los archivos CSV
    const rutaEstudiantes = path.join(__dirname, '..', 'respuestas bulls-s', 'estudiantes_rows.csv');
    const rutaRespuestas = path.join(__dirname, '..', 'respuestas bulls-s', 'respuestas_rows.csv');

    // Verificar que los archivos existen
    if (!fs.existsSync(rutaEstudiantes)) {
      console.error(`❌ No se encontró el archivo: ${rutaEstudiantes}`);
      return;
    }

    if (!fs.existsSync(rutaRespuestas)) {
      console.error(`❌ No se encontró el archivo: ${rutaRespuestas}`);
      return;
    }

    console.log('📁 Leyendo archivos CSV...');
    
    // Leer datos de los CSV
    const estudiantes = await leerCSV(rutaEstudiantes);
    const respuestas = await leerCSV(rutaRespuestas);

    console.log(`📊 Datos leídos:`);
    console.log(`   - Estudiantes: ${estudiantes.length}`);
    console.log(`   - Respuestas: ${respuestas.length}`);

    // Insertar estudiantes primero
    console.log('\n👥 Insertando estudiantes...');
    await insertarEstudiantes(estudiantes);

    // Insertar respuestas
    console.log('\n📝 Insertando respuestas...');
    await insertarRespuestas(respuestas);

    console.log('\n🎉 ¡Exportación completada exitosamente!');
    
    // Verificar datos insertados
    console.log('\n🔍 Verificando datos insertados...');
    
    const { data: countEstudiantes } = await supabase
      .from('estudiantes')
      .select('*', { count: 'exact', head: true });
    
    const { data: countRespuestas } = await supabase
      .from('respuestas')
      .select('*', { count: 'exact', head: true });

    console.log(`✅ Total en base de datos:`);
    console.log(`   - Estudiantes: ${countEstudiantes?.length || 'Error al contar'}`);
    console.log(`   - Respuestas: ${countRespuestas?.length || 'Error al contar'}`);

  } catch (error) {
    console.error('❌ Error durante la exportación:', error);
    process.exit(1);
  }
}

// Ejecutar si es llamado directamente
if (import.meta.url === `file:///${process.argv[1].replace(/\\/g, '/')}`) {
  main();
}

// Fallback para Windows - ejecutar main() directamente
main().catch(console.error);

export { insertarEstudiantes, insertarRespuestas, verificarConexion };