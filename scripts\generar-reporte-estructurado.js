import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Configurar dotenv
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuración de Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Error: Variables de entorno VITE_SUPABASE_URL y VITE_SUPABASE_ANON_KEY son requeridas');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Mapeo de preguntas según la estructura solicitada
const PREGUNTAS_ESTRUCTURA = {
  // Preguntas 1-10: Selección de 3 estudiantes
  sociometricas: {
    1: "¿Con quién te gusta trabajar en equipo?",
    2: "¿Con quién te gusta jugar en el recreo?",
    3: "¿A quién invitarías a tu cumpleaños?",
    4: "¿Con quién te sientes más cómodo/a hablando?",
    5: "¿Quién te ayuda cuando tienes problemas?",
    6: "¿Con quién no te gusta trabajar en equipo?",
    7: "¿Con quién no te gusta jugar en el recreo?",
    8: "¿A quién no invitarías a tu cumpleaños?",
    9: "¿Con quién no te sientes cómodo/a hablando?",
    10: "¿Quién no te ayuda cuando tienes problemas?"
  },
  // Preguntas 11-15: Respuesta abierta o selección simple
  abiertas: {
    11: "¿Qué tipo de conflictos has observado en tu clase?",
    12: "¿Dónde ocurren más frecuentemente los conflictos?",
    13: "¿Con qué frecuencia ocurren conflictos en tu clase?",
    14: "¿Cómo calificarías el ambiente de convivencia en tu clase?",
    15: "¿Qué sugerencias tienes para mejorar la convivencia?"
  }
};

async function obtenerDatosCompletos() {
  try {
    console.log('📊 Obteniendo datos completos...');
    
    // Obtener estudiantes
    const { data: estudiantes, error: errorEstudiantes } = await supabase
      .from('estudiantes')
      .select('*')
      .order('grado', { ascending: true })
      .order('nombre_estudiante', { ascending: true });
    
    if (errorEstudiantes) {
      throw new Error(`Error al obtener estudiantes: ${errorEstudiantes.message}`);
    }
    
    console.log(`✅ ${estudiantes.length} estudiantes obtenidos`);
    
    // Obtener respuestas
    const { data: respuestas, error: errorRespuestas } = await supabase
      .from('respuestas')
      .select('*')
      .order('estudiante_id', { ascending: true })
      .order('pregunta_id', { ascending: true });
    
    if (errorRespuestas) {
      throw new Error(`Error al obtener respuestas: ${errorRespuestas.message}`);
    }
    
    console.log(`✅ ${respuestas.length} respuestas obtenidas`);
    
    return { estudiantes, respuestas };
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    throw error;
  }
}

function crearMapeoEstudiantes(estudiantes) {
  const mapeo = {};
  estudiantes.forEach(est => {
    mapeo[est.id] = {
      nombre_completo: `${est.nombre_estudiante} ${est.apellido_estudiante}`,
      codigo: est.codigo_anonimizado,
      grado: est.grado
    };
  });
  return mapeo;
}

function interpretarRespuestaSociometrica(respuestaTexto, mapeoEstudiantes) {
  if (!respuestaTexto) return [];
  
  try {
    const ids = JSON.parse(respuestaTexto);
    if (Array.isArray(ids)) {
      return ids.map(id => {
        const estudiante = mapeoEstudiantes[id];
        return estudiante ? `${estudiante.nombre_completo} (${estudiante.codigo})` : 'Estudiante no encontrado';
      });
    }
  } catch (e) {
    // Si no es JSON válido, devolver como texto
    return [respuestaTexto];
  }
  
  return [];
}

function interpretarRespuestaAbierta(respuestaTexto) {
  if (!respuestaTexto) return 'Sin respuesta';
  
  try {
    const parsed = JSON.parse(respuestaTexto);
    
    if (typeof parsed === 'object' && parsed.selected && parsed.other) {
      // Respuesta con selección y campo "otro"
      const seleccionados = Array.isArray(parsed.selected) ? parsed.selected.join(', ') : parsed.selected;
      return `${seleccionados}${parsed.other ? '; Otro: ' + parsed.other : ''}`;
    } else if (typeof parsed === 'object' && parsed.selected) {
      // Solo selección
      return Array.isArray(parsed.selected) ? parsed.selected.join(', ') : parsed.selected;
    } else {
      // Objeto complejo
      return JSON.stringify(parsed);
    }
  } catch (e) {
    // Es texto plano
    return respuestaTexto;
  }
}

function organizarRespuestasPorEstudiante(respuestas) {
  const respuestasPorEstudiante = {};
  
  respuestas.forEach(respuesta => {
    if (!respuestasPorEstudiante[respuesta.estudiante_id]) {
      respuestasPorEstudiante[respuesta.estudiante_id] = [];
    }
    respuestasPorEstudiante[respuesta.estudiante_id].push(respuesta);
  });
  
  return respuestasPorEstudiante;
}

function determinarTipoPregunta(preguntaId, respuestaTexto) {
  // Intentar determinar si es sociométrica (selección múltiple de estudiantes)
  if (respuestaTexto) {
    try {
      const parsed = JSON.parse(respuestaTexto);
      if (Array.isArray(parsed) && parsed.length > 0) {
        // Verificar si los elementos parecen UUIDs (formato de estudiantes)
        const primerElemento = parsed[0];
        if (typeof primerElemento === 'string' && primerElemento.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
          return 'sociometrica';
        }
      }
    } catch (e) {
      // No es JSON, probablemente respuesta abierta
    }
  }
  return 'abierta';
}

function generarReporteEstructurado(estudiantes, respuestas) {
  console.log('📝 Generando reporte estructurado...');
  
  const mapeoEstudiantes = crearMapeoEstudiantes(estudiantes);
  const respuestasPorEstudiante = organizarRespuestasPorEstudiante(respuestas);
  
  const reporte = {
    metadata: {
      fecha_generacion: new Date().toISOString(),
      total_estudiantes: estudiantes.length,
      total_respuestas: respuestas.length,
      estructura: {
        preguntas_1_10: "Selección de 3 estudiantes (sociométricas)",
        preguntas_11_15: "Respuestas abiertas o selección simple"
      }
    },
    estudiantes: []
  };
  
  // Procesar cada estudiante
  estudiantes.forEach(estudiante => {
    const respuestasEstudiante = respuestasPorEstudiante[estudiante.id] || [];
    
    // Organizar respuestas por tipo
    const respuestasSociometricas = [];
    const respuestasAbiertas = [];
    
    respuestasEstudiante.forEach(respuesta => {
      const tipo = determinarTipoPregunta(respuesta.pregunta_id, respuesta.respuesta_texto);
      
      if (tipo === 'sociometrica') {
        respuestasSociometricas.push({
          pregunta_id: respuesta.pregunta_id,
          respuestas_seleccionadas: interpretarRespuestaSociometrica(respuesta.respuesta_texto, mapeoEstudiantes),
          fecha_respuesta: respuesta.fecha_respuesta
        });
      } else {
        respuestasAbiertas.push({
          pregunta_id: respuesta.pregunta_id,
          respuesta: interpretarRespuestaAbierta(respuesta.respuesta_texto),
          fecha_respuesta: respuesta.fecha_respuesta
        });
      }
    });
    
    const estudianteReporte = {
      informacion_estudiante: {
        nombre_completo: `${estudiante.nombre_estudiante} ${estudiante.apellido_estudiante}`,
        codigo: estudiante.codigo_anonimizado,
        grado: estudiante.grado,
        genero: estudiante.genero,
        edad: estudiante.edad
      },
      preguntas_1_10_sociometricas: {
        descripcion: "Preguntas donde el estudiante selecciona 3 compañeros",
        total_respuestas: respuestasSociometricas.length,
        respuestas: respuestasSociometricas.map((resp, index) => ({
          numero_pregunta: index + 1,
          pregunta_id: resp.pregunta_id,
          estudiantes_seleccionados: resp.respuestas_seleccionadas,
          total_seleccionados: resp.respuestas_seleccionadas.length,
          fecha_respuesta: resp.fecha_respuesta
        }))
      },
      preguntas_11_15_abiertas: {
        descripcion: "Preguntas de respuesta abierta o selección simple",
        total_respuestas: respuestasAbiertas.length,
        respuestas: respuestasAbiertas.map((resp, index) => ({
          numero_pregunta: index + 11,
          pregunta_id: resp.pregunta_id,
          respuesta_texto: resp.respuesta,
          fecha_respuesta: resp.fecha_respuesta
        }))
      },
      resumen: {
        total_respuestas_sociometricas: respuestasSociometricas.length,
        total_respuestas_abiertas: respuestasAbiertas.length,
        total_respuestas_general: respuestasEstudiante.length
      }
    };
    
    reporte.estudiantes.push(estudianteReporte);
  });
  
  return reporte;
}

function generarCSVEstructurado(reporte) {
  console.log('📊 Generando CSV estructurado...');
  
  const nombreArchivo = `reporte-estructurado-${new Date().toISOString().split('T')[0]}.csv`;
  const rutaArchivo = path.join(__dirname, '..', nombreArchivo);
  
  let csvContent = 'Nombre_Estudiante,Codigo,Grado,Genero,Edad,Tipo_Pregunta,Numero_Pregunta,Pregunta_ID,Respuesta_1,Respuesta_2,Respuesta_3,Respuesta_Texto,Fecha_Respuesta\n';
  
  reporte.estudiantes.forEach(estudiante => {
    const info = estudiante.informacion_estudiante;
    
    // Agregar respuestas sociométricas (preguntas 1-10)
    estudiante.preguntas_1_10_sociometricas.respuestas.forEach(respuesta => {
      const resp1 = respuesta.estudiantes_seleccionados[0] || '';
      const resp2 = respuesta.estudiantes_seleccionados[1] || '';
      const resp3 = respuesta.estudiantes_seleccionados[2] || '';
      
      csvContent += `"${info.nombre_completo}","${info.codigo}","${info.grado}","${info.genero}",${info.edad},"Sociométrica",${respuesta.numero_pregunta},"${respuesta.pregunta_id}","${resp1.replace(/"/g, '""')}","${resp2.replace(/"/g, '""')}","${resp3.replace(/"/g, '""')}","","${respuesta.fecha_respuesta}"\n`;
    });
    
    // Agregar respuestas abiertas (preguntas 11-15)
    estudiante.preguntas_11_15_abiertas.respuestas.forEach(respuesta => {
      csvContent += `"${info.nombre_completo}","${info.codigo}","${info.grado}","${info.genero}",${info.edad},"Abierta",${respuesta.numero_pregunta},"${respuesta.pregunta_id}","","","","${respuesta.respuesta_texto.replace(/"/g, '""')}","${respuesta.fecha_respuesta}"\n`;
    });
    
    // Si el estudiante no tiene respuestas, agregar una fila vacía
    if (estudiante.resumen.total_respuestas_general === 0) {
      csvContent += `"${info.nombre_completo}","${info.codigo}","${info.grado}","${info.genero}",${info.edad},"Sin respuestas","","","","","","Sin respuestas",""\n`;
    }
  });
  
  fs.writeFileSync(rutaArchivo, csvContent, 'utf8');
  console.log(`📊 CSV estructurado generado: ${rutaArchivo}`);
  
  return rutaArchivo;
}

function generarResumenEstructurado(reporte) {
  const totalSociometricas = reporte.estudiantes.reduce((acc, est) => acc + est.resumen.total_respuestas_sociometricas, 0);
  const totalAbiertas = reporte.estudiantes.reduce((acc, est) => acc + est.resumen.total_respuestas_abiertas, 0);
  const estudiantesConRespuestas = reporte.estudiantes.filter(est => est.resumen.total_respuestas_general > 0).length;
  const estudiantesSinRespuestas = reporte.estudiantes.length - estudiantesConRespuestas;
  
  const resumenTexto = `REPORTE ESTRUCTURADO DE ESTUDIANTES Y RESPUESTAS\n` +
    `Fecha de generación: ${new Date().toLocaleString('es-ES')}\n` +
    `=======================================================\n\n` +
    `ESTRUCTURA DEL REPORTE:\n` +
    `- Preguntas 1-10: Sociométricas (selección de 3 estudiantes)\n` +
    `- Preguntas 11-15: Abiertas (respuesta libre o selección simple)\n\n` +
    `RESUMEN GENERAL:\n` +
    `- Total de estudiantes: ${reporte.metadata.total_estudiantes}\n` +
    `- Estudiantes con respuestas: ${estudiantesConRespuestas}\n` +
    `- Estudiantes sin respuestas: ${estudiantesSinRespuestas}\n` +
    `- Total respuestas sociométricas: ${totalSociometricas}\n` +
    `- Total respuestas abiertas: ${totalAbiertas}\n` +
    `- Total respuestas generales: ${reporte.metadata.total_respuestas}\n\n` +
    `DISTRIBUCIÓN POR GRADO:\n` +
    reporte.estudiantes.reduce((acc, est) => {
      const grado = est.informacion_estudiante.grado;
      acc[grado] = (acc[grado] || 0) + 1;
      return acc;
    }, {})
    .toString()
    .split(',')
    .map(item => `- ${item}\n`)
    .join('') +
    `\nFORMATO DE DATOS:\n` +
    `- Nombres completos de estudiantes (totalmente legibles)\n` +
    `- Respuestas sociométricas: 3 estudiantes seleccionados por pregunta\n` +
    `- Respuestas abiertas: texto completo de la respuesta\n` +
    `- Fechas de respuesta incluidas\n` +
    `- Códigos de estudiantes para referencia\n`;
  
  const nombreResumen = `resumen-estructurado-${new Date().toISOString().split('T')[0]}.txt`;
  const rutaResumen = path.join(__dirname, '..', nombreResumen);
  fs.writeFileSync(rutaResumen, resumenTexto, 'utf8');
  console.log(`📈 Resumen estructurado generado: ${rutaResumen}`);
  
  return rutaResumen;
}

async function main() {
  try {
    console.log('🚀 Iniciando generación de reporte estructurado...');
    
    // Obtener todos los datos
    const { estudiantes, respuestas } = await obtenerDatosCompletos();
    
    // Generar reporte estructurado
    const reporteEstructurado = generarReporteEstructurado(estudiantes, respuestas);
    
    // Guardar reporte JSON estructurado
    const nombreArchivoJSON = `reporte-estructurado-${new Date().toISOString().split('T')[0]}.json`;
    const rutaArchivoJSON = path.join(__dirname, '..', nombreArchivoJSON);
    fs.writeFileSync(rutaArchivoJSON, JSON.stringify(reporteEstructurado, null, 2), 'utf8');
    console.log(`📄 Reporte JSON estructurado generado: ${rutaArchivoJSON}`);
    
    // Generar CSV estructurado
    const rutaCSV = generarCSVEstructurado(reporteEstructurado);
    
    // Generar resumen
    const rutaResumen = generarResumenEstructurado(reporteEstructurado);
    
    console.log('\n✅ Proceso completado exitosamente!');
    console.log('\n📁 Archivos generados:');
    console.log(`   - JSON estructurado: ${path.basename(rutaArchivoJSON)}`);
    console.log(`   - CSV estructurado: ${path.basename(rutaCSV)}`);
    console.log(`   - Resumen: ${path.basename(rutaResumen)}`);
    
    console.log('\n📋 ESTRUCTURA DEL REPORTE:');
    console.log('   - Nombre del estudiante');
    console.log('   - Preguntas 1-10: Las 3 respuestas del estudiante (sociométricas)');
    console.log('   - Preguntas 11-15: La respuesta que suministró (abiertas)');
    console.log('   - Todos los datos totalmente legibles');
    
  } catch (error) {
    console.error('❌ Error en el proceso:', error.message);
    process.exit(1);
  }
}

// Ejecutar si es llamado directamente
if (import.meta.url === `file://${process.argv[1]}` || process.argv[1].endsWith(path.basename(__filename))) {
  main();
}

// También ejecutar directamente como fallback para Windows
main();

export { obtenerDatosCompletos, generarReporteEstructurado };