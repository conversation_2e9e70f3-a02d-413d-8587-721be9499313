import React from 'react';
import { AlertTriangle, Users, Shield, TrendingUp, TrendingDown, Minus, UserX } from 'lucide-react';
import { BullsKPIs } from '../../../types/bulls-dashboard';
import { useBullsDashboard } from '../../../contexts/BullsDashboardContext';
import { useBullsRealData } from '../../../hooks/dashboard/useBullsRealData';

// Helper function para asegurar valores válidos
const safeNumber = (value: any, defaultValue: number = 0): number => {
  const num = Number(value);
  return isNaN(num) || !isFinite(num) ? defaultValue : num;
};

export const BullsKPICards: React.FC = React.memo(() => {
  // Memoizar el filtro para evitar re-renders innecesarios
  const courseFilter = React.useMemo(() => {
    // Por ahora usar un filtro fijo para evitar parpadeo
    return '8A'; // Cambiar a 6B o 8B para probar otros cursos
  }, []);

  // Usar el nuevo hook con datos reales de BULL-S
  const { kpis, loading, error } = useBullsRealData(
    courseFilter,
    false, // Desactivar auto-refresh para evitar parpadeo
    0 // Sin intervalo automático
  );
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-sm border p-6 animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-full"></div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
        <div className="col-span-full bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
            <span className="text-red-700">Error al cargar las métricas: {error}</span>
          </div>
        </div>
      </div>
    );
  }

  const getTrendIcon = (trend: number) => {
    if (trend > 0) return <TrendingUp className="h-4 w-4 text-red-500" />;
    if (trend < 0) return <TrendingDown className="h-4 w-4 text-green-500" />;
    return <Minus className="h-4 w-4 text-gray-400" />;
  };

  const getTrendColor = (trend: number) => {
    if (trend > 0) return 'text-red-600';
    if (trend < 0) return 'text-green-600';
    return 'text-gray-500';
  };

  const getCohesionColor = (level: string) => {
    switch (level) {
      case 'alto': return 'text-green-600';
      case 'medio': return 'text-yellow-600';
      case 'bajo': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getSafetyColor = (level: string) => {
    switch (level) {
      case 'alto': return 'text-green-600';
      case 'medio': return 'text-yellow-600';
      case 'bajo': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8" data-testid="kpi-cards">
      {/* Estudiantes en Riesgo */}
      <div className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
          </div>
          <div className="flex items-center space-x-1">
            {getTrendIcon(kpis.studentsAtRisk.trend)}
            <span className={`text-sm font-medium ${getTrendColor(kpis.studentsAtRisk.trend)}`}>
              {Math.abs(kpis.studentsAtRisk.trend)}%
            </span>
          </div>
        </div>
        <div className="space-y-1">
          <h3 className="text-sm font-medium text-gray-600">Estudiantes en Riesgo</h3>
          <div className="flex items-baseline space-x-2">
            <span className="text-2xl font-bold text-gray-900">
              {safeNumber(kpis.studentsAtRisk.value)}
            </span>
            <span className="text-sm text-gray-500">
              ({safeNumber(kpis.studentsAtRisk.percentage)}%)
            </span>
          </div>
          <p className="text-xs text-gray-500">Agresores + Víctimas</p>
        </div>
      </div>

      {/* Cohesión del Grupo */}
      <div className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className={`p-2 rounded-lg ${
              kpis.groupCohesion.color === 'green' ? 'bg-green-100' :
              kpis.groupCohesion.color === 'yellow' ? 'bg-yellow-100' : 'bg-red-100'
            }`}>
              <Users className={`h-6 w-6 ${
                kpis.groupCohesion.color === 'green' ? 'text-green-600' :
                kpis.groupCohesion.color === 'yellow' ? 'text-yellow-600' : 'text-red-600'
              }`} />
            </div>
          </div>
          <div className={`px-2 py-1 rounded-full text-xs font-medium ${
            kpis.groupCohesion.level === 'alto' ? 'bg-green-100 text-green-800' :
            kpis.groupCohesion.level === 'medio' ? 'bg-yellow-100 text-yellow-800' :
            'bg-red-100 text-red-800'
          }`}>
            {kpis.groupCohesion.level.toUpperCase()}
          </div>
        </div>
        <div className="space-y-1">
          <h3 className="text-sm font-medium text-gray-600">Cohesión del Grupo</h3>
          <div className="flex items-baseline space-x-2">
            <span className={`text-2xl font-bold ${getCohesionColor(kpis.groupCohesion.level)}`}>
              {safeNumber(kpis.groupCohesion.value)}%
            </span>
          </div>
          <p className="text-xs text-gray-500">Reciprocidades / Posibles × 100</p>
        </div>
      </div>

      {/* Seguridad Percibida */}
      <div className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className={`p-2 rounded-lg ${
              kpis.perceivedSafety.color === 'green' ? 'bg-green-100' :
              kpis.perceivedSafety.color === 'yellow' ? 'bg-yellow-100' : 'bg-red-100'
            }`}>
              <Shield className={`h-6 w-6 ${
                kpis.perceivedSafety.color === 'green' ? 'text-green-600' :
                kpis.perceivedSafety.color === 'yellow' ? 'text-yellow-600' : 'text-red-600'
              }`} />
            </div>
          </div>
          <div className={`px-2 py-1 rounded-full text-xs font-medium ${
            kpis.perceivedSafety.level === 'alto' ? 'bg-green-100 text-green-800' :
            kpis.perceivedSafety.level === 'medio' ? 'bg-yellow-100 text-yellow-800' :
            'bg-red-100 text-red-800'
          }`}>
            {kpis.perceivedSafety.level.toUpperCase()}
          </div>
        </div>
        <div className="space-y-1">
          <h3 className="text-sm font-medium text-gray-600">Seguridad Percibida</h3>
          <div className="flex items-baseline space-x-2">
            <span className={`text-2xl font-bold ${getSafetyColor(kpis.perceivedSafety.level)}`}>
              {safeNumber(kpis.perceivedSafety.value)}%
            </span>
          </div>
          <p className="text-xs text-gray-500">"Me siento seguro/a"</p>
        </div>
      </div>

      {/* Agresores Identificados */}
      <div className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <UserX className="h-6 w-6 text-red-600" />
            </div>
          </div>
          <div className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium">
            {kpis.aggressors.percentage}%
          </div>
        </div>
        <div className="space-y-1">
          <h3 className="text-sm font-medium text-gray-600">Agresores Identificados</h3>
          <div className="flex items-baseline space-x-2">
            <span className="text-2xl font-bold text-gray-900">
              {safeNumber(kpis.aggressors.value)}
            </span>
          </div>
          {kpis.aggressors.topAggressors.length > 0 && (
            <div className="mt-2">
              <p className="text-xs text-gray-500 mb-1">Top 3 Agresores:</p>
              <div className="space-y-1">
                {kpis.aggressors.topAggressors.slice(0, 3).map((aggressor, index) => (
                  <div key={aggressor.id} className="flex items-center justify-between text-xs">
                    <span className="truncate">{index + 1}. {aggressor.name}</span>
                    <span className="text-red-600 font-medium">{aggressor.nominations}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Víctimas Identificadas */}
      <div className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Shield className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
            {kpis.victims.percentage}%
          </div>
        </div>
        <div className="space-y-1">
          <h3 className="text-sm font-medium text-gray-600">Víctimas Identificadas</h3>
          <div className="flex items-baseline space-x-2">
            <span className="text-2xl font-bold text-gray-900">
              {safeNumber(kpis.victims.value)}
            </span>
          </div>
          {kpis.victims.topVictims.length > 0 && (
            <div className="mt-2">
              <p className="text-xs text-gray-500 mb-1">Top 3 Víctimas:</p>
              <div className="space-y-1">
                {kpis.victims.topVictims.slice(0, 3).map((victim, index) => (
                  <div key={victim.id} className="flex items-center justify-between text-xs">
                    <span className="truncate">{index + 1}. {victim.name}</span>
                    <span className="text-blue-600 font-medium">{victim.nominations}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
});