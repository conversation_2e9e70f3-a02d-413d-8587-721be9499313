import React from 'react';
import { Al<PERSON><PERSON>riangle, Users, Shield, TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { BullsKPIs } from '../../../types/bulls-dashboard';
import { useBullsDashboard } from '../../../contexts/BullsDashboardContext';

export const BullsKPICards: React.FC = React.memo(() => {
  const { kpis, loading, filters } = useBullsDashboard();
  
  // Debug: Log para verificar que los datos se actualizan con los filtros
  React.useEffect(() => {
    console.log('BullsKPICards - Filtros cambiaron:', filters);
    console.log('BullsKPICards - KPIs actualizados:', kpis);
  }, [filters, kpis]);
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-sm border p-6 animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-full"></div>
          </div>
        ))}
      </div>
    );
  }

  const getTrendIcon = (trend: number) => {
    if (trend > 0) return <TrendingUp className="h-4 w-4 text-red-500" />;
    if (trend < 0) return <TrendingDown className="h-4 w-4 text-green-500" />;
    return <Minus className="h-4 w-4 text-gray-400" />;
  };

  const getTrendColor = (trend: number) => {
    if (trend > 0) return 'text-red-600';
    if (trend < 0) return 'text-green-600';
    return 'text-gray-500';
  };

  const getCohesionColor = (level: string) => {
    switch (level) {
      case 'alto': return 'text-green-600';
      case 'medio': return 'text-yellow-600';
      case 'bajo': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getSafetyColor = (level: string) => {
    switch (level) {
      case 'alto': return 'text-green-600';
      case 'medio': return 'text-yellow-600';
      case 'bajo': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8" data-testid="kpi-cards">
      {/* Estudiantes en Riesgo */}
      <div className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
          </div>
          <div className="flex items-center space-x-1">
            {getTrendIcon(kpis.studentsAtRisk.trend)}
            <span className={`text-sm font-medium ${getTrendColor(kpis.studentsAtRisk.trend)}`}>
              {Math.abs(kpis.studentsAtRisk.trend)}%
            </span>
          </div>
        </div>
        <div className="space-y-1">
          <h3 className="text-sm font-medium text-gray-600">Estudiantes en Riesgo</h3>
          <div className="flex items-baseline space-x-2">
            <span className="text-2xl font-bold text-gray-900">
              {kpis.studentsAtRisk.value}
            </span>
            <span className="text-sm text-gray-500">
              ({kpis.studentsAtRisk.percentage}%)
            </span>
          </div>
          <p className="text-xs text-gray-500">Agresores + Víctimas</p>
        </div>
      </div>

      {/* Cohesión del Grupo */}
      <div className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className={`p-2 rounded-lg ${
              kpis.groupCohesion.color === 'green' ? 'bg-green-100' :
              kpis.groupCohesion.color === 'yellow' ? 'bg-yellow-100' : 'bg-red-100'
            }`}>
              <Users className={`h-6 w-6 ${
                kpis.groupCohesion.color === 'green' ? 'text-green-600' :
                kpis.groupCohesion.color === 'yellow' ? 'text-yellow-600' : 'text-red-600'
              }`} />
            </div>
          </div>
          <div className={`px-2 py-1 rounded-full text-xs font-medium ${
            kpis.groupCohesion.level === 'alto' ? 'bg-green-100 text-green-800' :
            kpis.groupCohesion.level === 'medio' ? 'bg-yellow-100 text-yellow-800' :
            'bg-red-100 text-red-800'
          }`}>
            {kpis.groupCohesion.level.toUpperCase()}
          </div>
        </div>
        <div className="space-y-1">
          <h3 className="text-sm font-medium text-gray-600">Cohesión del Grupo</h3>
          <div className="flex items-baseline space-x-2">
            <span className={`text-2xl font-bold ${getCohesionColor(kpis.groupCohesion.level)}`}>
              {kpis.groupCohesion.value}%
            </span>
          </div>
          <p className="text-xs text-gray-500">Reciprocidades / Posibles × 100</p>
        </div>
      </div>

      {/* Seguridad Percibida */}
      <div className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className={`p-2 rounded-lg ${
              kpis.perceivedSafety.color === 'green' ? 'bg-green-100' :
              kpis.perceivedSafety.color === 'yellow' ? 'bg-yellow-100' : 'bg-red-100'
            }`}>
              <Shield className={`h-6 w-6 ${
                kpis.perceivedSafety.color === 'green' ? 'text-green-600' :
                kpis.perceivedSafety.color === 'yellow' ? 'text-yellow-600' : 'text-red-600'
              }`} />
            </div>
          </div>
          <div className={`px-2 py-1 rounded-full text-xs font-medium ${
            kpis.perceivedSafety.level === 'alto' ? 'bg-green-100 text-green-800' :
            kpis.perceivedSafety.level === 'medio' ? 'bg-yellow-100 text-yellow-800' :
            'bg-red-100 text-red-800'
          }`}>
            {kpis.perceivedSafety.level.toUpperCase()}
          </div>
        </div>
        <div className="space-y-1">
          <h3 className="text-sm font-medium text-gray-600">Seguridad Percibida</h3>
          <div className="flex items-baseline space-x-2">
            <span className={`text-2xl font-bold ${getSafetyColor(kpis.perceivedSafety.level)}`}>
              {kpis.perceivedSafety.value}%
            </span>
          </div>
          <p className="text-xs text-gray-500">"Me siento seguro/a"</p>
        </div>
      </div>

      {/* Top 1 Forma de Agresión Detectada */}
      <div className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <AlertTriangle className="h-6 w-6 text-orange-600" />
            </div>
          </div>
          {kpis.topAggressionTypes.length > 0 && (
            <div className="px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-xs font-medium">
              #{kpis.topAggressionTypes[0].percentage}%
            </div>
          )}
        </div>
        <div className="space-y-1">
          <h3 className="text-sm font-medium text-gray-600">Top 1 Agresión Detectada</h3>
          {kpis.topAggressionTypes.length > 0 ? (
            <div className="space-y-2">
              <div className="flex flex-col space-y-1">
                <span className="text-lg font-bold text-gray-900 truncate">
                  {kpis.topAggressionTypes[0].type}
                </span>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-700">
                    {kpis.topAggressionTypes[0].count} casos
                  </span>
                  <span className="text-xs text-gray-500">
                    ({kpis.topAggressionTypes[0].percentage}% del total)
                  </span>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-sm text-gray-500">
              No hay datos disponibles
            </div>
          )}
        </div>
      </div>
    </div>
  );
});