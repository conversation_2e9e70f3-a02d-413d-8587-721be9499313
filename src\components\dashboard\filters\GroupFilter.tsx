import React from 'react';
import { Users } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../ui/select';

interface GroupFilterProps {
  value?: string;
  onChange: (value: string | undefined) => void;
  placeholder?: string;
}

const GROUP_OPTIONS = [
  { value: 'edad-18-25', label: '18-25 años' },
  { value: 'edad-26-35', label: '26-35 años' },
  { value: 'edad-36-45', label: '36-45 años' },
  { value: 'edad-46-55', label: '46-55 años' },
  { value: 'edad-56+', label: '56+ años' },
  { value: 'genero-masculino', label: 'Masculino' },
  { value: 'genero-femenino', label: 'Femenino' },
  { value: 'nivel-educacion-basica', label: 'Educación Básica' },
  { value: 'nivel-educacion-media', label: 'Educación Media' },
  { value: 'nivel-educacion-superior', label: 'Educación Superior' },
];

export const GroupFilter: React.FC<GroupFilterProps> = ({
  value,
  onChange,
  placeholder = 'Todos los grupos'
}) => {
  const handleChange = (newValue: string) => {
    onChange(newValue === 'all' ? undefined : newValue);
  };

  return (
    <div className="flex items-center space-x-2">
      <Users className="h-4 w-4 text-muted-foreground" />
      <Select value={value || 'all'} onValueChange={handleChange}>
        <SelectTrigger className="w-[200px]">
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">{placeholder}</SelectItem>
          {GROUP_OPTIONS.map((group) => (
            <SelectItem key={group.value} value={group.value}>
              {group.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};