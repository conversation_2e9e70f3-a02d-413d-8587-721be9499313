import { useMemo } from 'react';
import { DashboardData, DashboardMetric } from '../../types/dashboard';

export const useDashboardMetrics = (data: DashboardData | null) => {
  const metrics = useMemo((): DashboardMetric[] => {
    if (!data) return [];

    return [
      {
        id: 'total-patients',
        label: 'Total Pacientes',
        value: data.totalPatients,
        icon: 'users',
        color: 'blue'
      },
      {
        id: 'total-institutions',
        label: 'Total Instituciones',
        value: data.totalInstitutions,
        icon: 'building',
        color: 'green'
      },
      {
        id: 'total-results',
        label: 'Total Resultados',
        value: data.totalResults,
        icon: 'clipboard-check',
        color: 'purple'
      },
      {
        id: 'average-score',
        label: 'Puntaje Promedio',
        value: Math.round(data.averageScore),
        icon: 'star',
        color: 'yellow'
      }
    ];
  }, [data]);

  const chartData = useMemo(() => {
    if (!data) return null;

    // Institution distribution
    const institutionChart = {
      labels: data.institutionMetrics.map(inst => inst.name),
      datasets: [
        {
          label: 'Pacientes por Institución',
          data: data.institutionMetrics.map(inst => inst.patientCount),
          backgroundColor: [
            '#3B82F6',
            '#10B981',
            '#8B5CF6',
            '#F59E0B',
            '#EF4444',
            '#06B6D4',
            '#84CC16',
            '#F97316'
          ],
          borderWidth: 2
        }
      ]
    };

    // Results over time
    const resultsByMonth = data.resultMetrics.reduce((acc, result) => {
      const month = new Date(result.createdAt).toLocaleDateString('es-ES', { month: 'short', year: '2-digit' });
      acc[month] = (acc[month] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const resultsChart = {
      labels: Object.keys(resultsByMonth),
      datasets: [
        {
          label: 'Resultados por Mes',
          data: Object.values(resultsByMonth),
          borderColor: '#3B82F6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderWidth: 2,
          fill: true
        }
      ]
    };

    // Score distribution
    const scoreRanges = {
      '0-20': 0,
      '21-40': 0,
      '41-60': 0,
      '61-80': 0,
      '81-100': 0
    };

    data.resultMetrics.forEach(result => {
      const score = result.score;
      if (score <= 20) scoreRanges['0-20']++;
      else if (score <= 40) scoreRanges['21-40']++;
      else if (score <= 60) scoreRanges['41-60']++;
      else if (score <= 80) scoreRanges['61-80']++;
      else scoreRanges['81-100']++;
    });

    const scoreDistribution = {
      labels: Object.keys(scoreRanges),
      datasets: [
        {
          label: 'Distribución de Puntajes',
          data: Object.values(scoreRanges),
          backgroundColor: [
            '#EF4444',
            '#F59E0B',
            '#84CC16',
            '#10B981',
            '#3B82F6'
          ],
          borderWidth: 1
        }
      ]
    };

    return {
      institutionChart,
      resultsChart,
      scoreDistribution
    };
  }, [data]);

  const topPerformers = useMemo(() => {
    if (!data) return [];
    
    return data.patientMetrics
      .filter(patient => patient.resultCount > 0)
      .sort((a, b) => b.averageScore - a.averageScore)
      .slice(0, 5);
  }, [data]);

  const recentActivity = useMemo(() => {
    if (!data) return [];
    
    return data.resultMetrics
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 10);
  }, [data]);

  return {
    metrics,
    chartData,
    topPerformers,
    recentActivity
  };
};