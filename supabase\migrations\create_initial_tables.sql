```sql
/*
  # Create Initial Tables, Foreign Keys, and Basic RLS

  This migration creates the foundational tables for the application, adds foreign key constraints, and enables basic Row Level Security.

  1. New Tables
     - `instituciones_educativas`: Stores information about educational institutions.
       - `id` (uuid, pk): Unique identifier.
       - `nombre` (text): Name of the institution (required).
       - `tipo_institucion` (varchar(50)): Type of institution (e.g., 'Colegio', 'Instituto').
       - `direccion` (text): Address of the institution.
       - `telefono` (varchar(50)): Phone number.
       - `email` (varchar(255)): Email address.
       - `fecha_creacion` (timestamptz): Timestamp of creation.
       - `fecha_actualizacion` (timestamptz): Timestamp of last update.
     - `estudiantes`: Stores student information.
       - `id` (uuid, pk): Unique identifier.
       - `nombre_estudiante` (varchar): Student's first name (required).
       - `apellido_estudiante` (varchar): Student's last name (required).
       - `genero` (varchar(1)): Student's gender ('M', 'F', 'O').
       - `codigo_anonimizado` (varchar(50)): Anonymized student code (required).
       - `numero_documento` (varchar(50)): Student's document number (optional, unique).
       - `edad` (integer): Student's age.
       - `grado` (varchar(20)): Grade or level (e.g., '5A', '1 ESO').
       - `institucion_id` (uuid, fk): Foreign key referencing `instituciones_educativas` (required).
       - `grupo_id` (uuid, fk): Foreign key referencing `grupos` (optional).
       - `fecha_creacion` (timestamptz): Timestamp of creation.
       - `fecha_actualizacion` (timestamptz): Timestamp of last update.
     - `grupos`: Stores group/class information.
       - `id` (uuid, pk): Unique identifier.
       - `nombre` (varchar(100)): Name of the group (required).
       - `descripcion` (text): Optional description of the group.
       - `grado` (varchar(10)): Grade level.
       - `seccion` (varchar(10)): Section identifier.
       - `ano_escolar` (varchar(10)): School year.
       - `institucion_id` (uuid, fk): Foreign key referencing `instituciones_educativas` (required).
       - `fecha_creacion` (timestamptz): Timestamp of creation.
       - `fecha_actualizacion` (timestamptz): Timestamp of last update.
     - `cuestionarios`: Stores questionnaire definitions.
       - `id` (uuid, pk): Unique identifier.
       - `titulo` (varchar(200)): Title of the questionnaire (required).
       - `descripcion` (text): Optional description.
       - `fecha_administracion` (timestamptz): Date the questionnaire was administered (optional).
       - `fecha_creacion` (timestamptz): Timestamp of creation.
       - `fecha_actualizacion` (timestamptz): Timestamp of last update.
     - `respuestas_cuestionario`: Stores questionnaire responses.
       - `id` (uuid, pk): Unique identifier.
       - `estudiante_id` (uuid, fk): Foreign key referencing `estudiantes` (required).
       - `cuestionario_id` (uuid, fk): Foreign key referencing `cuestionarios` (required).
       - `pregunta_id` (varchar(50)): Question identifier (required).
       - `respuesta` (text): Response content (required).
       - `companeros_seleccionados` (text[]): Array of selected peers for sociometric questions.
       - `fecha_respuesta` (timestamptz): Timestamp of response.
       - `fecha_creacion` (timestamptz): Timestamp of creation.
       - `fecha_actualizacion` (timestamptz): Timestamp of last update.
     - `administrators`: Stores administrator information.
       - `id` (uuid, pk): Unique identifier.
       - `user_id` (uuid): Reference to auth.users (required).
       - `email` (varchar(255)): Administrator email (required).
       - `nombre` (varchar(100)): Administrator name.
       - `activo` (boolean): Whether the administrator is active.
       - `fecha_creacion` (timestamptz): Timestamp of creation.
       - `fecha_actualizacion` (timestamptz): Timestamp of last update.
     - `profesores_instituciones`: Links teachers to institutions.
       - `id` (uuid, pk): Unique identifier.
       - `profesor_id` (uuid): Reference to auth.users (required).
       - `institucion_id` (uuid, fk): Foreign key referencing `instituciones_educativas` (required).
       - `activo` (boolean): Whether the relationship is active.
       - `fecha_creacion` (timestamptz): Timestamp of creation.
       - `fecha_actualizacion` (timestamptz): Timestamp of last update.

  2. Security
     - Enabled Row Level Security (RLS) on all tables.
     - Added comprehensive RLS policies for proper access control.
     - Triggers for automatic `fecha_actualizacion` updates.

  3. Foreign Key Constraints
     - All foreign key relationships are properly defined.
     - Cascading deletes where appropriate.
*/

-- Crear tablas principales
CREATE TABLE IF NOT EXISTS instituciones_educativas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    nombre TEXT NOT NULL,
    tipo_institucion VARCHAR(50),
    direccion TEXT,
    telefono VARCHAR(50),
    email VARCHAR(255),
    fecha_creacion TIMESTAMPTZ NOT NULL DEFAULT now(),
    fecha_actualizacion TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS grupos (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    nombre VARCHAR(100) NOT NULL,
    descripcion TEXT,
    grado VARCHAR(10),
    seccion VARCHAR(10),
    ano_escolar VARCHAR(10),
    institucion_id UUID NOT NULL,
    fecha_creacion TIMESTAMPTZ NOT NULL DEFAULT now(),
    fecha_actualizacion TIMESTAMPTZ NOT NULL DEFAULT now(),
    CONSTRAINT fk_grupos_institucion FOREIGN KEY (institucion_id) REFERENCES instituciones_educativas(id) ON DELETE CASCADE,
    CONSTRAINT uk_grupos_nombre_institucion UNIQUE(nombre, institucion_id)
);

CREATE TABLE IF NOT EXISTS estudiantes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    nombre_estudiante VARCHAR(100) NOT NULL,
    apellido_estudiante VARCHAR(100) NOT NULL,
    genero VARCHAR(1) CHECK (genero IN ('M', 'F', 'O')),
    codigo_anonimizado VARCHAR(50) NOT NULL UNIQUE,
    numero_documento VARCHAR(50) UNIQUE,
    edad INTEGER CHECK (edad > 0 AND edad < 100),
    grado VARCHAR(20),
    institucion_id UUID NOT NULL,
    grupo_id UUID,
    fecha_creacion TIMESTAMPTZ NOT NULL DEFAULT now(),
    fecha_actualizacion TIMESTAMPTZ NOT NULL DEFAULT now(),
    CONSTRAINT fk_estudiantes_institucion FOREIGN KEY (institucion_id) REFERENCES instituciones_educativas(id) ON DELETE CASCADE,
    CONSTRAINT fk_estudiantes_grupo FOREIGN KEY (grupo_id) REFERENCES grupos(id) ON DELETE SET NULL
);

CREATE TABLE IF NOT EXISTS cuestionarios (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    titulo VARCHAR(200) NOT NULL,
    descripcion TEXT,
    fecha_administracion TIMESTAMPTZ,
    fecha_creacion TIMESTAMPTZ NOT NULL DEFAULT now(),
    fecha_actualizacion TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS respuestas_cuestionario (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    estudiante_id UUID NOT NULL,
    cuestionario_id UUID NOT NULL,
    pregunta_id VARCHAR(50) NOT NULL,
    respuesta TEXT NOT NULL,
    companeros_seleccionados TEXT[],
    fecha_respuesta TIMESTAMPTZ NOT NULL DEFAULT now(),
    fecha_creacion TIMESTAMPTZ NOT NULL DEFAULT now(),
    fecha_actualizacion TIMESTAMPTZ NOT NULL DEFAULT now(),
    CONSTRAINT fk_respuestas_estudiante FOREIGN KEY (estudiante_id) REFERENCES estudiantes(id) ON DELETE CASCADE,
    CONSTRAINT fk_respuestas_cuestionario FOREIGN KEY (cuestionario_id) REFERENCES cuestionarios(id) ON DELETE CASCADE,
    CONSTRAINT uk_respuestas_estudiante_pregunta UNIQUE(estudiante_id, cuestionario_id, pregunta_id)
);

CREATE TABLE IF NOT EXISTS administrators (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    nombre VARCHAR(100),
    activo BOOLEAN NOT NULL DEFAULT true,
    fecha_creacion TIMESTAMPTZ NOT NULL DEFAULT now(),
    fecha_actualizacion TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS profesores_instituciones (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    profesor_id UUID NOT NULL,
    institucion_id UUID NOT NULL,
    activo BOOLEAN NOT NULL DEFAULT true,
    fecha_creacion TIMESTAMPTZ NOT NULL DEFAULT now(),
    fecha_actualizacion TIMESTAMPTZ NOT NULL DEFAULT now(),
    CONSTRAINT fk_profesores_institucion FOREIGN KEY (institucion_id) REFERENCES instituciones_educativas(id) ON DELETE CASCADE,
    CONSTRAINT uk_profesor_institucion UNIQUE(profesor_id, institucion_id)
);

-- Crear función para actualizar fecha_actualizacion automáticamente
CREATE OR REPLACE FUNCTION update_fecha_actualizacion()
RETURNS TRIGGER AS $$
BEGIN
    NEW.fecha_actualizacion = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Habilitar Row Level Security para todas las tablas
ALTER TABLE instituciones_educativas ENABLE ROW LEVEL SECURITY;
ALTER TABLE estudiantes ENABLE ROW LEVEL SECURITY;
ALTER TABLE grupos ENABLE ROW LEVEL SECURITY;
ALTER TABLE cuestionarios ENABLE ROW LEVEL SECURITY;
ALTER TABLE respuestas_cuestionario ENABLE ROW LEVEL SECURITY;
ALTER TABLE administrators ENABLE ROW LEVEL SECURITY;
ALTER TABLE profesores_instituciones ENABLE ROW LEVEL SECURITY;

-- Crear triggers para actualización automática de fecha_actualizacion
CREATE TRIGGER trigger_update_instituciones_fecha_actualizacion
    BEFORE UPDATE ON instituciones_educativas
    FOR EACH ROW EXECUTE FUNCTION update_fecha_actualizacion();

CREATE TRIGGER trigger_update_estudiantes_fecha_actualizacion
    BEFORE UPDATE ON estudiantes
    FOR EACH ROW EXECUTE FUNCTION update_fecha_actualizacion();

CREATE TRIGGER trigger_update_grupos_fecha_actualizacion
    BEFORE UPDATE ON grupos
    FOR EACH ROW EXECUTE FUNCTION update_fecha_actualizacion();

CREATE TRIGGER trigger_update_cuestionarios_fecha_actualizacion
    BEFORE UPDATE ON cuestionarios
    FOR EACH ROW EXECUTE FUNCTION update_fecha_actualizacion();

CREATE TRIGGER trigger_update_respuestas_fecha_actualizacion
    BEFORE UPDATE ON respuestas_cuestionario
    FOR EACH ROW EXECUTE FUNCTION update_fecha_actualizacion();

CREATE TRIGGER trigger_update_administrators_fecha_actualizacion
    BEFORE UPDATE ON administrators
    FOR EACH ROW EXECUTE FUNCTION update_fecha_actualizacion();

CREATE TRIGGER trigger_update_profesores_fecha_actualizacion
    BEFORE UPDATE ON profesores_instituciones
    FOR EACH ROW EXECUTE FUNCTION update_fecha_actualizacion();

-- Crear políticas RLS comprehensivas para cada tabla

-- Políticas para instituciones_educativas
DROP POLICY IF EXISTS "Instituciones acceso administradores" ON instituciones_educativas;
CREATE POLICY "Instituciones acceso administradores" ON instituciones_educativas
    FOR ALL
    USING (
      exists(select 1 from public.administrators where user_id = auth.uid() and activo = true)
    )
    WITH CHECK (
      exists(select 1 from public.administrators where user_id = auth.uid() and activo = true)
    );

CREATE POLICY "Instituciones lectura profesores" ON instituciones_educativas
    FOR SELECT
    USING (
      exists(select 1 from public.profesores_instituciones pi where pi.profesor_id = auth.uid() and pi.institucion_id = instituciones_educativas.id and pi.activo = true)
    );

-- Políticas para estudiantes
DROP POLICY IF EXISTS "Estudiantes acceso restringido" ON estudiantes;
CREATE POLICY "Estudiantes acceso administradores" ON estudiantes
    FOR ALL
    USING (
      exists(select 1 from public.administrators where user_id = auth.uid() and activo = true)
    )
    WITH CHECK (
      exists(select 1 from public.administrators where user_id = auth.uid() and activo = true)
    );

CREATE POLICY "Estudiantes acceso profesores" ON estudiantes
    FOR ALL
    USING (
      exists(select 1 from public.profesores_instituciones pi where pi.profesor_id = auth.uid() and pi.institucion_id = estudiantes.institucion_id and pi.activo = true)
    )
    WITH CHECK (
      exists(select 1 from public.profesores_instituciones pi where pi.profesor_id = auth.uid() and pi.institucion_id = estudiantes.institucion_id and pi.activo = true)
    );

-- Políticas para grupos
DROP POLICY IF EXISTS "Grupos acceso restringido" ON grupos;
CREATE POLICY "Grupos acceso administradores" ON grupos
    FOR ALL
    USING (
      exists(select 1 from public.administrators where user_id = auth.uid() and activo = true)
    )
    WITH CHECK (
      exists(select 1 from public.administrators where user_id = auth.uid() and activo = true)
    );

CREATE POLICY "Grupos acceso profesores" ON grupos
    FOR ALL
    USING (
      exists(select 1 from public.profesores_instituciones pi where pi.profesor_id = auth.uid() and pi.institucion_id = grupos.institucion_id and pi.activo = true)
    )
    WITH CHECK (
      exists(select 1 from public.profesores_instituciones pi where pi.profesor_id = auth.uid() and pi.institucion_id = grupos.institucion_id and pi.activo = true)
    );

-- Políticas para cuestionarios
DROP POLICY IF EXISTS "Cuestionarios acceso autenticado" ON cuestionarios;
DROP POLICY IF EXISTS "Cuestionarios manejo administradores" ON cuestionarios;
CREATE POLICY "Cuestionarios lectura autenticados" ON cuestionarios
    FOR SELECT
    USING (auth.role() = 'authenticated');

CREATE POLICY "Cuestionarios manejo administradores" ON cuestionarios
    FOR ALL
    USING (
      exists(select 1 from public.administrators where user_id = auth.uid() and activo = true)
    )
    WITH CHECK (
      exists(select 1 from public.administrators where user_id = auth.uid() and activo = true)
    );

-- Políticas para respuestas_cuestionario
CREATE POLICY "Respuestas acceso administradores" ON respuestas_cuestionario
    FOR ALL
    USING (
      exists(select 1 from public.administrators where user_id = auth.uid() and activo = true)
    )
    WITH CHECK (
      exists(select 1 from public.administrators where user_id = auth.uid() and activo = true)
    );

CREATE POLICY "Respuestas acceso profesores" ON respuestas_cuestionario
    FOR ALL
    USING (
      exists(
        select 1 from public.profesores_instituciones pi 
        join public.estudiantes e on e.institucion_id = pi.institucion_id 
        where pi.profesor_id = auth.uid() and e.id = respuestas_cuestionario.estudiante_id and pi.activo = true
      )
    )
    WITH CHECK (
      exists(
        select 1 from public.profesores_instituciones pi 
        join public.estudiantes e on e.institucion_id = pi.institucion_id 
        where pi.profesor_id = auth.uid() and e.id = respuestas_cuestionario.estudiante_id and pi.activo = true
      )
    );

-- Políticas para administrators
CREATE POLICY "Administrators acceso propio" ON administrators
    FOR ALL
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "Administrators lectura administradores" ON administrators
    FOR SELECT
    USING (
      exists(select 1 from public.administrators where user_id = auth.uid() and activo = true)
    );

-- Políticas para profesores_instituciones
CREATE POLICY "Profesores_instituciones acceso administradores" ON profesores_instituciones
    FOR ALL
    USING (
      exists(select 1 from public.administrators where user_id = auth.uid() and activo = true)
    )
    WITH CHECK (
      exists(select 1 from public.administrators where user_id = auth.uid() and activo = true)
    );

CREATE POLICY "Profesores_instituciones lectura propia" ON profesores_instituciones
    FOR SELECT
    USING (profesor_id = auth.uid());

    ```