import React from 'react';
import { DashboardLayout } from '../../../components/dashboard/layouts/DashboardLayout';
import { DashboardGrid, GridItem } from '../../../components/dashboard/layouts/DashboardGrid';
import { ChartWidget } from '../../../components/dashboard/widgets/ChartWidget';
import { TableWidget } from '../../../components/dashboard/widgets/TableWidget';
import { KPIWidget } from '../../../components/dashboard/widgets/KPIWidget';
import { useDashboardData } from '../../../hooks/dashboard/useDashboardData';
import { useDashboardFilters } from '../../../hooks/dashboard/useDashboardFilters';
import { useDashboardMetrics } from '../../../hooks/dashboard/useDashboardMetrics';
import { DateRangeFilter } from '../../../components/dashboard/filters/DateRangeFilter';
import { InstitutionFilter } from '../../../components/dashboard/filters/InstitutionFilter';
import { GroupFilter } from '../../../components/dashboard/filters/GroupFilter';

export const AnalyticsDashboard: React.FC = () => {
  const { filters, updateFilter, setDateRange, clearFilters } = useDashboardFilters();
  const { data, loading, error, lastUpdated, refreshData } = useDashboardData(filters);
  const { chartData, topPerformers } = useDashboardMetrics(data);

  const handleDateRangeChange = (startDate: string, endDate: string) => {
    setDateRange(startDate, endDate);
  };

  const handleDateClear = () => {
    clearFilters();
  };

  if (error) {
    return (
      <DashboardLayout
        title="Análisis Detallado"
        subtitle="Análisis profundo de los datos"
        onRefresh={refreshData}
        loading={loading}
      >
        <div className="text-center py-8">
          <div className="text-red-500">Error: {error}</div>
          <button onClick={refreshData} className="mt-4 px-4 py-2 bg-blue-500 text-white rounded">
            Reintentar
          </button>
        </div>
      </DashboardLayout>
    );
  }

  // Calculate KPIs for analytics
  const kpis = data ? [
    {
      title: 'Tasa de Evaluación',
      current: data.totalResults,
      target: data.totalPatients * 2, // Assume 2 evaluations per patient
      unit: ' evaluaciones'
    },
    {
      title: 'Promedio General',
      current: Math.round(data.averageScore),
      target: 75, // Target score
      unit: ' pts'
    },
    {
      title: 'Cobertura Institucional',
      current: data.totalInstitutions,
      target: 10, // Target institutions
      unit: ' instituciones'
    }
  ] : [];

  return (
    <DashboardLayout
      title="Análisis Detallado"
      subtitle="Análisis profundo de los datos"
      onRefresh={refreshData}
      lastUpdated={lastUpdated}
      loading={loading}
      actions={
        <div className="flex items-center space-x-4">
          <DateRangeFilter
            startDate={filters.startDate}
            endDate={filters.endDate}
            onDateRangeChange={handleDateRangeChange}
            onClear={handleDateClear}
          />
          <InstitutionFilter
            value={filters.institutionId}
            onChange={(value) => updateFilter('institutionId', value)}
          />
          <GroupFilter
            value={filters.groupId}
            onChange={(value) => updateFilter('groupId', value)}
          />
        </div>
      }
    >
      {/* KPI Section */}
      <DashboardGrid columns={3}>
        {kpis.map((kpi) => (
          <KPIWidget
            key={kpi.title}
            title={kpi.title}
            current={kpi.current}
            target={kpi.target}
            unit={kpi.unit}
            loading={loading}
          />
        ))}
      </DashboardGrid>

      {/* Advanced Charts */}
      <DashboardGrid columns={2}>
        <GridItem span={1}>
          <ChartWidget
            title="Evolución Temporal de Resultados"
            data={chartData?.resultsChart}
            type="line"
            loading={loading}
            height={350}
          />
        </GridItem>
        
        <GridItem span={1}>
          <ChartWidget
            title="Comparación por Institución"
            data={chartData?.institutionChart}
            type="bar"
            loading={loading}
            height={350}
          />
        </GridItem>
      </DashboardGrid>

      {/* Detailed Tables */}
      <DashboardGrid columns={2}>
        <GridItem span={1}>
          <TableWidget
            title="Análisis por Institución"
            data={data?.institutionMetrics || []}
            columns={[
              { key: 'name', label: 'Institución' },
              { key: 'patientCount', label: 'Pacientes', type: 'number' },
              { key: 'resultCount', label: 'Evaluaciones', type: 'number' },
              { key: 'averageScore', label: 'Promedio', type: 'badge' },
              { key: 'lastActivity', label: 'Última Actividad', type: 'date' }
            ]}
            loading={loading}
            maxHeight={400}
          />
        </GridItem>
        
        <GridItem span={1}>
          <TableWidget
            title="Top 10 Mejores Pacientes"
            data={topPerformers.slice(0, 10)}
            columns={[
              { key: 'name', label: 'Nombre' },
              { key: 'institutionName', label: 'Institución' },
              { key: 'resultCount', label: 'Evaluaciones', type: 'number' },
              { key: 'averageScore', label: 'Promedio', type: 'badge' },
              { key: 'lastResultDate', label: 'Última Evaluación', type: 'date' }
            ]}
            loading={loading}
            maxHeight={400}
          />
        </GridItem>
      </DashboardGrid>

      {/* Score Distribution Analysis */}
      <DashboardGrid columns={1}>
        <GridItem span={1}>
          <ChartWidget
            title="Análisis de Distribución de Puntajes"
            data={chartData?.scoreDistribution}
            type="bar"
            loading={loading}
            height={300}
          />
        </GridItem>
      </DashboardGrid>
    </DashboardLayout>
  );
};

export default AnalyticsDashboard;