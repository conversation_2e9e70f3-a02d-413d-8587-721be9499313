/**
 * Script para configurar la base de datos de BULL-S en Supabase
 * Este script crea todas las tablas necesarias para el funcionamiento del sistema
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuración de Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Error: Variables de entorno no configuradas');
  console.log('Asegúrate de tener configuradas:');
  console.log('- VITE_SUPABASE_URL');
  console.log('- SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupDatabase() {
  try {
    console.log('🚀 Iniciando configuración de la base de datos...');
    
    // Leer el archivo SQL
    const sqlPath = path.join(__dirname, '..', 'sql', 'create_tables.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');
    
    console.log('📄 Ejecutando script SQL...');
    
    // Dividir el SQL en comandos individuales
    const commands = sqlContent
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0 && !cmd.startsWith('--'));
    
    let successCount = 0;
    let errorCount = 0;
    
    for (const command of commands) {
      try {
        if (command.trim()) {
          const { error } = await supabase.rpc('exec_sql', { sql: command });
          
          if (error) {
            // Intentar ejecutar directamente si RPC falla
            const { error: directError } = await supabase
              .from('_temp_sql_execution')
              .select('*')
              .limit(0);
            
            if (directError) {
              console.log(`⚠️  Comando omitido (puede ser normal): ${command.substring(0, 50)}...`);
            }
          } else {
            successCount++;
          }
        }
      } catch (cmdError) {
        console.log(`⚠️  Error en comando: ${cmdError.message}`);
        errorCount++;
      }
    }
    
    console.log(`✅ Configuración completada:`);
    console.log(`   - Comandos ejecutados exitosamente: ${successCount}`);
    console.log(`   - Comandos con errores/omitidos: ${errorCount}`);
    
    // Verificar que las tablas principales existan
    console.log('\n🔍 Verificando tablas creadas...');
    
    const tablesToCheck = [
      'instituciones',
      'grupos', 
      'estudiantes',
      'cuestionarios',
      'respuestas_cuestionario'
    ];
    
    for (const table of tablesToCheck) {
      try {
        const { error } = await supabase
          .from(table)
          .select('*')
          .limit(1);
          
        if (error) {
          console.log(`❌ Tabla '${table}' no encontrada o inaccesible`);
        } else {
          console.log(`✅ Tabla '${table}' verificada`);
        }
      } catch (error) {
        console.log(`❌ Error verificando tabla '${table}': ${error.message}`);
      }
    }
    
    console.log('\n🎉 ¡Configuración de base de datos completada!');
    console.log('Ahora puedes usar la aplicación BULL-S con datos reales.');
    
  } catch (error) {
    console.error('❌ Error durante la configuración:', error);
    process.exit(1);
  }
}

// Función alternativa usando SQL directo
async function setupDatabaseDirect() {
  try {
    console.log('🚀 Configuración alternativa usando SQL directo...');
    
    // Crear tablas una por una
    const tables = [
      {
        name: 'instituciones',
        sql: `
          CREATE TABLE IF NOT EXISTS public.instituciones (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            nombre TEXT NOT NULL,
            direccion TEXT,
            telefono TEXT,
            email TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      },
      {
        name: 'grupos',
        sql: `
          CREATE TABLE IF NOT EXISTS public.grupos (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            nombre TEXT NOT NULL,
            institucion_id UUID NOT NULL,
            grado TEXT,
            ano_escolar TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      },
      {
        name: 'estudiantes',
        sql: `
          CREATE TABLE IF NOT EXISTS public.estudiantes (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            nombre TEXT NOT NULL,
            apellido TEXT,
            edad INTEGER,
            genero TEXT,
            numero_documento TEXT,
            tipo_documento TEXT,
            grupo_id UUID NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      },
      {
        name: 'cuestionarios',
        sql: `
          CREATE TABLE IF NOT EXISTS public.cuestionarios (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            titulo TEXT NOT NULL,
            descripcion TEXT,
            tipo TEXT NOT NULL,
            fecha_creacion TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            fecha_aplicacion TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      },
      {
        name: 'respuestas_cuestionario',
        sql: `
          CREATE TABLE IF NOT EXISTS public.respuestas_cuestionario (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            estudiante_id UUID NOT NULL,
            cuestionario_id UUID NOT NULL,
            fecha_respuesta TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            completado BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      }
    ];
    
    for (const table of tables) {
      try {
        console.log(`📝 Creando tabla ${table.name}...`);
        
        // Usar una consulta simple para crear la tabla
        const { error } = await supabase.rpc('exec_sql', { 
          sql: table.sql 
        });
        
        if (error) {
          console.log(`⚠️  Error creando ${table.name}: ${error.message}`);
        } else {
          console.log(`✅ Tabla ${table.name} creada exitosamente`);
        }
      } catch (error) {
        console.log(`⚠️  Error con tabla ${table.name}: ${error.message}`);
      }
    }
    
    // Insertar datos de ejemplo
    console.log('\n📊 Insertando datos de ejemplo...');
    
    try {
      // Insertar institución
      const { data: institucion, error: instError } = await supabase
        .from('instituciones')
        .insert([{
          nombre: 'Colegio La SALLE',
          direccion: 'Calle Principal #123',
          telefono: '555-1234',
          email: '<EMAIL>'
        }])
        .select()
        .single();
      
      if (instError) {
        console.log('⚠️  Error insertando institución:', instError.message);
      } else {
        console.log('✅ Institución de ejemplo creada');
        
        // Insertar grupos
        const grupos = [
          { nombre: '6A', grado: '6°', ano_escolar: '2024-2025' },
          { nombre: '6B', grado: '6°', ano_escolar: '2024-2025' },
          { nombre: '7A', grado: '7°', ano_escolar: '2024-2025' }
        ];
        
        for (const grupo of grupos) {
          const { error: grupoError } = await supabase
            .from('grupos')
            .insert([{
              ...grupo,
              institucion_id: institucion.id
            }]);
          
          if (grupoError) {
            console.log(`⚠️  Error insertando grupo ${grupo.nombre}:`, grupoError.message);
          } else {
            console.log(`✅ Grupo ${grupo.nombre} creado`);
          }
        }
      }
    } catch (error) {
      console.log('⚠️  Error insertando datos de ejemplo:', error.message);
    }
    
    console.log('\n🎉 ¡Configuración alternativa completada!');
    
  } catch (error) {
    console.error('❌ Error en configuración alternativa:', error);
  }
}

// Ejecutar configuración
if (process.argv.includes('--direct')) {
  setupDatabaseDirect();
} else {
  setupDatabase();
}