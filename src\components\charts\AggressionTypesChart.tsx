import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Bar, <PERSON>Axi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer, Cell } from 'recharts';
import { Filter, ChevronDown, ChevronUp } from 'lucide-react';

interface AggressionTypeData {
  course: string;
  insultos: number;
  insultos_n: number;
  maltrato_fisico: number;
  maltrato_fisico_n: number;
  rechazo_social: number;
  rechazo_social_n: number;
  otros: number;
  otros_n: number;
}

interface AggressionTypesChartProps {
  data: AggressionTypeData[];
  title?: string;
  className?: string;
}

const COLORS = {
  'insultos': '#FF8042',
  'maltrato_fisico': '#0088FE', 
  'rechazo_social': '#FFBB28',
  'otros': '#00C49F'
};

const AggressionTypesChart: React.FC<AggressionTypesChartProps> = ({
  data,
  title = "Distribución Porcentual de Formas de Agresión por Curso",
  className
}) => {
  const [showFilters, setShowFilters] = useState(false);
  const [minPercentage, setMinPercentage] = useState(0);
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);

  // Filtrar datos según los filtros aplicados
  const filteredData = data.filter(item => {
    const totalPercentage = item.insultos + item.maltrato_fisico + item.rechazo_social + item.otros;
    if (minPercentage > 0 && totalPercentage < minPercentage) return false;
    if (selectedTypes.length > 0) {
      const hasSelectedType = selectedTypes.some(type => {
        const percentage = item[type as keyof AggressionTypeData] as number;
        return percentage > 0;
      });
      if (!hasSelectedType) return false;
    }
    return true;
  });

  // Lista de todos los tipos para el filtro
  const allTypes = ['insultos', 'maltrato_fisico', 'rechazo_social', 'otros'];
  const typeLabels = {
    'insultos': 'Insultos',
    'maltrato_fisico': 'Maltrato físico',
    'rechazo_social': 'Rechazo social',
    'otros': 'Otros'
  };

  const handleTypeSelection = (type: string) => {
    if (selectedTypes.includes(type)) {
      setSelectedTypes(selectedTypes.filter(t => t !== type));
    } else {
      setSelectedTypes([...selectedTypes, type]);
    }
  };

  const clearFilters = () => {
    setMinPercentage(0);
    setSelectedTypes([]);
  };

  return (
    <div className={`bg-white p-4 rounded-lg shadow-md ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-medium text-gray-800 text-lg">{title}</h3>
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="flex items-center justify-center px-3 py-1 bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100 transition-colors text-sm"
        >
          <Filter className="h-3.5 w-3.5 mr-1" />
          Filtros
          {showFilters ?
            <ChevronUp className="h-3.5 w-3.5 ml-1" /> :
            <ChevronDown className="h-3.5 w-3.5 ml-1" />
          }
        </button>
      </div>

      {showFilters && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Valor mínimo (%)
              </label>
              <input
                type="range"
                min="0"
                max="100"
                value={minPercentage}
                onChange={(e) => setMinPercentage(parseInt(e.target.value))}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>0%</span>
                <span>{minPercentage}%</span>
                <span>100%</span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tipos de agresión
              </label>
              <div className="grid grid-cols-2 gap-2">
                {allTypes.map(type => (
                  <div key={type} className="flex items-center">
                    <input
                      type="checkbox"
                      id={`type-${type}`}
                      checked={selectedTypes.length === 0 || selectedTypes.includes(type)}
                      onChange={() => handleTypeSelection(type)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label htmlFor={`type-${type}`} className="ml-2 text-sm text-gray-700">
                      {typeLabels[type as keyof typeof typeLabels]}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="mt-3 flex justify-end">
            <button
              onClick={clearFilters}
              className="px-3 py-1 text-sm bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 mr-2"
            >
              Limpiar
            </button>
            <button
              onClick={() => setShowFilters(false)}
              className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Aplicar
            </button>
          </div>
        </div>
      )}

      <div className="h-96">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={filteredData}
            margin={{
              top: 20,
              right: 30,
              left: 60,
              bottom: 40,
            }}
            layout="vertical"
          >
            <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
            <XAxis
              type="number"
              domain={[0, 100]}
              label={{
                value: 'Porcentaje (%)',
                position: 'insideBottom',
                offset: -5
              }}
            />
            <YAxis
              dataKey="course"
              type="category"
              width={50}
              tick={{ fontSize: 12 }}
            />
            <Tooltip
              content={({ active, payload, label }) => {
                if (active && payload && payload.length) {
                  const data = payload[0].payload;
                  return (
                    <div className="bg-white p-3 border border-gray-300 rounded-lg shadow-lg">
                      <p className="font-semibold text-gray-800 mb-2">{`Curso: ${label}`}</p>
                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="w-3 h-3 bg-orange-500 rounded mr-2"></div>
                            <span className="text-sm">Insultos:</span>
                          </div>
                          <span className="text-sm font-medium">{data.insultos}% ({data.insultos_n} casos)</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="w-3 h-3 bg-blue-500 rounded mr-2"></div>
                            <span className="text-sm">Maltrato físico:</span>
                          </div>
                          <span className="text-sm font-medium">{data.maltrato_fisico}% ({data.maltrato_fisico_n} casos)</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="w-3 h-3 bg-yellow-500 rounded mr-2"></div>
                            <span className="text-sm">Rechazo social:</span>
                          </div>
                          <span className="text-sm font-medium">{data.rechazo_social}% ({data.rechazo_social_n} casos)</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="w-3 h-3 bg-green-500 rounded mr-2"></div>
                            <span className="text-sm">Otros:</span>
                          </div>
                          <span className="text-sm font-medium">{data.otros}% ({data.otros_n} casos)</span>
                        </div>
                      </div>
                    </div>
                  );
                }
                return null;
              }}
            />
            <Legend 
              wrapperStyle={{ paddingTop: 10 }}
              payload={[
                { value: 'Insultos', type: 'rect', color: COLORS.insultos },
                { value: 'Maltrato físico', type: 'rect', color: COLORS.maltrato_fisico },
                { value: 'Rechazo social', type: 'rect', color: COLORS.rechazo_social },
                { value: 'Otros', type: 'rect', color: COLORS.otros }
              ]}
            />
            <Bar dataKey="insultos" stackId="a" fill={COLORS.insultos} name="Insultos" />
            <Bar dataKey="maltrato_fisico" stackId="a" fill={COLORS.maltrato_fisico} name="Maltrato físico" />
            <Bar dataKey="rechazo_social" stackId="a" fill={COLORS.rechazo_social} name="Rechazo social" />
            <Bar dataKey="otros" stackId="a" fill={COLORS.otros} name="Otros" />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {filteredData.length === 0 && (
        <div className="text-center text-gray-500 mt-4">
          No hay datos que cumplan con los criterios de filtrado.
        </div>
      )}

      {minPercentage > 0 && (
        <div className="text-xs text-gray-500 italic mt-2">
          * Mostrando solo tipos con un porcentaje ≥ {minPercentage}%
        </div>
      )}
    </div>
  );
};

export default AggressionTypesChart;