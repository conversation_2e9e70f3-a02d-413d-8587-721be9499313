-- Migración para crear las vistas específicas del dashboard BULL-S
-- Estas vistas implementan las métricas exactas requeridas por cada módulo

-- Vista para roles de estudiantes (vw_roles)
CREATE OR REPLACE VIEW public.vw_roles AS
WITH nominaciones_agresion AS (
    SELECT 
        r.estudiante_id,
        r.grupo_id,
        COUNT(*) as nom_agr
    FROM public.respuestas r
    JOIN public.preguntas p ON r.pregunta_id = p.id
    WHERE (p.pregunta ILIKE '%intimidar%' OR p.pregunta ILIKE '%maltratar%' OR p.pregunta ILIKE '%agresi%')
        AND r.respuesta_texto IS NOT NULL
        AND r.respuesta_texto != '[]'
    GROUP BY r.estudiante_id, r.grupo_id
),
nominaciones_victima AS (
    SELECT 
        r.estudiante_id,
        r.grupo_id,
        COUNT(*) as nom_vic
    FROM public.respuestas r
    JOIN public.preguntas p ON r.pregunta_id = p.id
    WHERE (p.pregunta ILIKE '%víctima%' OR p.pregunta ILIKE '%victima%' OR p.pregunta ILIKE '%maltrato%')
        AND r.respuesta_texto IS NOT NULL
        AND r.respuesta_texto != '[]'
    GROUP BY r.estudiante_id, r.grupo_id
),
nominaciones_observador AS (
    SELECT 
        r.estudiante_id,
        r.grupo_id,
        COUNT(*) as nom_obs
    FROM public.respuestas r
    JOIN public.preguntas p ON r.pregunta_id = p.id
    WHERE (p.pregunta ILIKE '%observ%' OR p.pregunta ILIKE '%ve%')
        AND r.respuesta_texto IS NOT NULL
        AND r.respuesta_texto != '[]'
    GROUP BY r.estudiante_id, r.grupo_id
),
total_estudiantes AS (
    SELECT 
        grupo_id,
        COUNT(*) as total
    FROM public.estudiantes
    GROUP BY grupo_id
)
SELECT 
    e.id AS student_id,
    e.nombre_estudiante AS name,
    g.nombre AS course,
    e.genero AS gender,
    CASE 
        WHEN COALESCE(na.nom_agr, 0) >= 0.25 * te.total AND COALESCE(nv.nom_vic, 0) >= 0.25 * te.total THEN 'agresor-victima'
        WHEN COALESCE(na.nom_agr, 0) >= 0.25 * te.total THEN 'agresor'
        WHEN COALESCE(nv.nom_vic, 0) >= 0.25 * te.total THEN 'victima'
        WHEN COALESCE(no.nom_obs, 0) >= 0.25 * te.total THEN 'observador'
        ELSE 'sin rol específico'
    END AS role
FROM public.estudiantes e
JOIN public.grupos g ON e.grupo_id = g.id
LEFT JOIN nominaciones_agresion na ON e.id = na.estudiante_id
LEFT JOIN nominaciones_victima nv ON e.id = nv.estudiante_id
LEFT JOIN nominaciones_observador no ON e.id = no.estudiante_id
JOIN total_estudiantes te ON e.grupo_id = te.grupo_id;

-- Vista para popularidad (vw_popularity)
CREATE OR REPLACE VIEW public.vw_popularity AS
WITH elecciones_positivas AS (
    SELECT 
        jsonb_array_elements_text(r.respuesta_texto::jsonb)::uuid AS elegido_id,
        r.grupo_id,
        COUNT(*) as elecciones
    FROM public.respuestas r
    JOIN public.preguntas p ON r.pregunta_id = p.id
    WHERE p.categoria = 'sociometrica' 
        AND (p.pregunta ILIKE '%gusta%' OR p.pregunta ILIKE '%amigo%' OR p.pregunta ILIKE '%jugar%')
        AND r.respuesta_texto IS NOT NULL
        AND r.respuesta_texto != '[]'
    GROUP BY elegido_id, r.grupo_id
),
total_grupo AS (
    SELECT 
        grupo_id,
        COUNT(*) as total_estudiantes
    FROM public.estudiantes
    GROUP BY grupo_id
)
SELECT 
    e.id AS student_id,
    e.nombre_estudiante AS name,
    g.nombre AS course,
    COALESCE(ep.elecciones, 0) AS elecciones_recibidas,
    ROUND((COALESCE(ep.elecciones, 0)::numeric / tg.total_estudiantes::numeric) * 100, 2) AS Pe
FROM public.estudiantes e
JOIN public.grupos g ON e.grupo_id = g.id
LEFT JOIN elecciones_positivas ep ON e.id = ep.elegido_id AND e.grupo_id = ep.grupo_id
JOIN total_grupo tg ON e.grupo_id = tg.grupo_id;

-- Vista para rechazo (vw_rejection)
CREATE OR REPLACE VIEW public.vw_rejection AS
WITH rechazos AS (
    SELECT 
        jsonb_array_elements_text(r.respuesta_texto::jsonb)::uuid AS rechazado_id,
        r.grupo_id,
        COUNT(*) as rechazos
    FROM public.respuestas r
    JOIN public.preguntas p ON r.pregunta_id = p.id
    WHERE p.categoria = 'sociometrica' 
        AND (p.pregunta ILIKE '%no gusta%' OR p.pregunta ILIKE '%rechazo%' OR p.pregunta ILIKE '%manía%')
        AND r.respuesta_texto IS NOT NULL
        AND r.respuesta_texto != '[]'
    GROUP BY rechazado_id, r.grupo_id
),
total_grupo AS (
    SELECT 
        grupo_id,
        COUNT(*) as total_estudiantes
    FROM public.estudiantes
    GROUP BY grupo_id
)
SELECT 
    e.id AS student_id,
    e.nombre_estudiante AS name,
    g.nombre AS course,
    COALESCE(r.rechazos, 0) AS rechazos_recibidos,
    ROUND((COALESCE(r.rechazos, 0)::numeric / tg.total_estudiantes::numeric) * 100, 2) AS Sp
FROM public.estudiantes e
JOIN public.grupos g ON e.grupo_id = g.id
LEFT JOIN rechazos r ON e.id = r.rechazado_id AND e.grupo_id = r.grupo_id
JOIN total_grupo tg ON e.grupo_id = tg.grupo_id;

-- Vista para cohesión grupal (vw_cohesion)
-- Implementa la fórmula especificada: reciprocal * 100.0 / possible
-- Usa sociomatrix con items 1,3 (elecciones positivas)
CREATE OR REPLACE VIEW public.vw_cohesion AS
WITH reciprocidades AS (
    SELECT 
        s1.course,
        SUM(CASE WHEN s1.chooser_id = s2.chosen_id AND s2.chooser_id = s1.chosen_id THEN 1 ELSE 0 END) AS reciprocal
    FROM public.sociomatrix s1
    JOIN public.sociomatrix s2 ON 
        s1.course = s2.course
        AND s1.chooser_id = s2.chosen_id
        AND s1.chosen_id = s2.chooser_id
        AND s1.item = s2.item
    WHERE s1.item IN (1, 3) -- elecciones positivas
    GROUP BY s1.course
),
total_posibles AS (
    SELECT 
        course,
        (COUNT(DISTINCT student_id) * (COUNT(DISTINCT student_id) - 1) / 2) AS possible
    FROM public.students
    GROUP BY course
)
SELECT 
    COALESCE(g.nombre, tp.course) AS course,
    tp.course AS grupo_id,
    COALESCE(r.reciprocal, 0) as reciprocidades,
    tp.possible as total_posibles_relaciones,
    ROUND(COALESCE(r.reciprocal, 0) * 100.0 / NULLIF(tp.possible, 0), 1) AS cohesion_pct
FROM total_posibles tp
LEFT JOIN reciprocidades r ON tp.course = r.course
LEFT JOIN public.grupos g ON g.nombre = tp.course;

-- Vista para agresores identificados (vw_aggressors)
CREATE OR REPLACE VIEW public.vw_aggressors AS
WITH nominaciones_agresor AS (
    SELECT 
        jsonb_array_elements_text(r.respuesta_texto::jsonb)::uuid AS agresor_id,
        r.grupo_id,
        COUNT(*) as nominaciones_agr
    FROM public.respuestas r
    JOIN public.preguntas p ON r.pregunta_id = p.id
    WHERE (p.pregunta ILIKE '%intimidar%' OR p.pregunta ILIKE '%maltratar%' OR p.pregunta ILIKE '%agresi%')
        AND r.respuesta_texto IS NOT NULL
        AND r.respuesta_texto != '[]'
    GROUP BY agresor_id, r.grupo_id
),
total_grupo AS (
    SELECT 
        grupo_id,
        COUNT(*) as total_students
    FROM public.estudiantes
    GROUP BY grupo_id
)
SELECT 
    e.id AS student_id,
    e.nombre_estudiante AS name,
    g.nombre AS course,
    COALESCE(na.nominaciones_agr, 0) AS nominations_agr,
    tg.total_students
FROM public.estudiantes e
JOIN public.grupos g ON e.grupo_id = g.id
LEFT JOIN nominaciones_agresor na ON e.id = na.agresor_id AND e.grupo_id = na.grupo_id
JOIN total_grupo tg ON e.grupo_id = tg.grupo_id
WHERE COALESCE(na.nominaciones_agr, 0) >= 0.25 * tg.total_students;

-- Vista para víctimas identificadas (vw_victims)
CREATE OR REPLACE VIEW public.vw_victims AS
WITH nominaciones_victima AS (
    SELECT 
        jsonb_array_elements_text(r.respuesta_texto::jsonb)::uuid AS victima_id,
        r.grupo_id,
        COUNT(*) as nominaciones_vic
    FROM public.respuestas r
    JOIN public.preguntas p ON r.pregunta_id = p.id
    WHERE (p.pregunta ILIKE '%víctima%' OR p.pregunta ILIKE '%victima%' OR p.pregunta ILIKE '%maltrato%')
        AND r.respuesta_texto IS NOT NULL
        AND r.respuesta_texto != '[]'
    GROUP BY victima_id, r.grupo_id
),
total_grupo AS (
    SELECT 
        grupo_id,
        COUNT(*) as total_students
    FROM public.estudiantes
    GROUP BY grupo_id
)
SELECT 
    e.id AS student_id,
    e.nombre_estudiante AS name,
    g.nombre AS course,
    COALESCE(nv.nominaciones_vic, 0) AS nominations_vic,
    tg.total_students
FROM public.estudiantes e
JOIN public.grupos g ON e.grupo_id = g.id
LEFT JOIN nominaciones_victima nv ON e.id = nv.victima_id AND e.grupo_id = nv.grupo_id
JOIN total_grupo tg ON e.grupo_id = tg.grupo_id
WHERE COALESCE(nv.nominaciones_vic, 0) >= 0.25 * tg.total_students;

-- Vista para matriz de interacciones (vw_interactions)
CREATE OR REPLACE VIEW public.vw_interactions AS
WITH interacciones AS (
    SELECT 
        r1.estudiante_id AS aggressor_id,
        jsonb_array_elements_text(r1.respuesta_texto::jsonb)::uuid AS victim_id,
        r1.grupo_id,
        COUNT(*) as interaction_count
    FROM public.respuestas r1
    JOIN public.preguntas p1 ON r1.pregunta_id = p1.id
    WHERE (p1.pregunta ILIKE '%intimidar%' OR p1.pregunta ILIKE '%maltratar%')
        AND r1.respuesta_texto IS NOT NULL
        AND r1.respuesta_texto != '[]'
    GROUP BY r1.estudiante_id, victim_id, r1.grupo_id
)
SELECT 
    i.aggressor_id,
    i.victim_id,
    ea.nombre_estudiante AS aggressor_name,
    ev.nombre_estudiante AS victim_name,
    g.nombre AS course,
    i.interaction_count
FROM interacciones i
JOIN public.estudiantes ea ON i.aggressor_id = ea.id
JOIN public.estudiantes ev ON i.victim_id = ev.id
JOIN public.grupos g ON i.grupo_id = g.id
WHERE i.interaction_count > 0;

-- Habilitar RLS en todas las vistas
ALTER VIEW public.vw_roles SET (security_invoker = true);
ALTER VIEW public.vw_popularity SET (security_invoker = true);
ALTER VIEW public.vw_rejection SET (security_invoker = true);
ALTER VIEW public.vw_cohesion SET (security_invoker = true);
ALTER VIEW public.vw_aggressors SET (security_invoker = true);
ALTER VIEW public.vw_victims SET (security_invoker = true);
ALTER VIEW public.vw_interactions SET (security_invoker = true);

-- Crear índices para optimizar el rendimiento
CREATE INDEX IF NOT EXISTS idx_respuestas_estudiante_pregunta ON public.respuestas(estudiante_id, pregunta_id);
CREATE INDEX IF NOT EXISTS idx_respuestas_grupo_categoria ON public.respuestas(grupo_id) WHERE respuesta_texto IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_preguntas_categoria ON public.preguntas(categoria);
CREATE INDEX IF NOT EXISTS idx_estudiantes_grupo ON public.estudiantes(grupo_id);

-- Comentarios para documentación
COMMENT ON VIEW public.vw_roles IS 'Vista que clasifica a los estudiantes en roles: agresor, víctima, observador o sin rol específico basado en nominaciones';
COMMENT ON VIEW public.vw_popularity IS 'Vista que calcula el índice de popularidad (Pe) de cada estudiante basado en elecciones positivas';
COMMENT ON VIEW public.vw_rejection IS 'Vista que calcula el índice de rechazo (Sp) de cada estudiante basado en nominaciones negativas';
COMMENT ON VIEW public.vw_cohesion IS 'Vista que calcula el porcentaje de cohesión grupal basado en reciprocidades de elecciones';
COMMENT ON VIEW public.vw_aggressors IS 'Vista que identifica agresores con nominaciones >= 25% del total de estudiantes del grupo';
COMMENT ON VIEW public.vw_victims IS 'Vista que identifica víctimas con nominaciones >= 25% del total de estudiantes del grupo';
COMMENT ON VIEW public.vw_interactions IS 'Vista que muestra la matriz de interacciones agresor-víctima con frecuencia de nominaciones';