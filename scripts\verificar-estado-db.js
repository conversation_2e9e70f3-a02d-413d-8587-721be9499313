/**
 * Script para verificar el estado actual de la base de datos
 * Verifica qué tablas, vistas y funciones existen
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function verificarEstadoDB() {
  console.log('🔍 VERIFICANDO ESTADO DE LA BASE DE DATOS');
  console.log('============================================================');

  try {
    // Verificar tablas principales
    console.log('\n📋 Verificando tablas principales...');
    const tablas = ['students', 'answers', 'sociomatrix', 'groups'];
    
    for (const tabla of tablas) {
      try {
        const { data, error, count } = await supabase
          .from(tabla)
          .select('*', { count: 'exact', head: true });
          
        if (error) {
          console.log(`❌ Tabla ${tabla}: ${error.message}`);
        } else {
          console.log(`✅ Tabla ${tabla}: ${count} registros`);
        }
      } catch (error) {
        console.log(`❌ Tabla ${tabla}: No disponible`);
      }
    }

    // Verificar vistas
    console.log('\n👁️ Verificando vistas...');
    const vistas = ['vw_roles', 'vw_cohesion', 'vw_victims', 'vw_aggressors'];
    
    for (const vista of vistas) {
      try {
        const { data, error, count } = await supabase
          .from(vista)
          .select('*', { count: 'exact', head: true });
          
        if (error) {
          console.log(`❌ Vista ${vista}: ${error.message}`);
        } else {
          console.log(`✅ Vista ${vista}: ${count} registros`);
        }
      } catch (error) {
        console.log(`❌ Vista ${vista}: No disponible`);
      }
    }

    // Verificar funciones (intentando llamarlas)
    console.log('\n⚙️ Verificando funciones...');
    const funciones = [
      { nombre: 'calcular_comparativas', params: {} },
      { nombre: 'calcular_panorama_curso', params: {} },
      { nombre: 'calcular_sociograma', params: {} },
      { nombre: 'calcular_contexto', params: {} }
    ];
    
    for (const funcion of funciones) {
      try {
        const { data, error } = await supabase.rpc(funcion.nombre, funcion.params);
        if (error) {
          console.log(`❌ Función ${funcion.nombre}: ${error.message}`);
        } else {
          console.log(`✅ Función ${funcion.nombre}: Disponible`);
        }
      } catch (error) {
        console.log(`❌ Función ${funcion.nombre}: No disponible`);
      }
    }

    // Verificar datos de muestra
    console.log('\n📊 Verificando datos de muestra...');
    
    try {
      const { data: students, error: studentsError } = await supabase
        .from('students')
        .select('course')
        .limit(5);
        
      if (studentsError) {
        console.log(`❌ Error obteniendo estudiantes: ${studentsError.message}`);
      } else {
        const cursos = [...new Set(students.map(s => s.course))];
        console.log(`✅ Cursos disponibles: ${cursos.join(', ')}`);
      }
    } catch (error) {
      console.log(`❌ Error verificando datos: ${error.message}`);
    }

    try {
      const { data: answers, error: answersError } = await supabase
        .from('answers')
        .select('item')
        .eq('item', 15)
        .limit(1);
        
      if (answersError) {
        console.log(`❌ Error verificando item 15: ${answersError.message}`);
      } else {
        console.log(`✅ Item 15 (seguridad): ${answers.length > 0 ? 'Disponible' : 'No encontrado'}`);
      }
    } catch (error) {
      console.log(`❌ Error verificando item 15: ${error.message}`);
    }

  } catch (error) {
    console.error('❌ Error general:', error.message);
  }

  console.log('\n✅ VERIFICACIÓN COMPLETADA');
  console.log('\n💡 RECOMENDACIONES:');
  console.log('- Si faltan vistas/funciones, aplicar migraciones manualmente en Supabase Dashboard');
  console.log('- SQL Editor > Copiar contenido de archivos .sql > Ejecutar');
  console.log('- Orden: sociometric_views → dashboard_views → metrics_functions');
}

verificarEstadoDB();