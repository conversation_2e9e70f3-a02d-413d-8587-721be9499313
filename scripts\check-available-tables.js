#!/usr/bin/env node

/**
 * Script para verificar qué tablas están disponibles en la base de datos
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Cargar variables de entorno
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Variables de entorno VITE_SUPABASE_URL y VITE_SUPABASE_ANON_KEY son requeridas');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkAvailableTables() {
  console.log('🔍 Verificando tablas disponibles en la base de datos...\n');

  const tablesToCheck = [
    'estudiantes',
    'instituciones_educativas',
    'grupos',
    'cuestionarios',
    'respuestas_cuestionario',
    'respuestas',
    'preguntas',
    'opciones_respuesta',
    'user_profiles',
    'administrators'
  ];

  const availableTables = [];
  const unavailableTables = [];

  for (const table of tablesToCheck) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true });

      if (error) {
        console.log(`❌ ${table}: ${error.message}`);
        unavailableTables.push(table);
      } else {
        console.log(`✅ ${table}: Disponible (${data?.length || 0} registros)`);
        availableTables.push(table);
      }
    } catch (err) {
      console.log(`❌ ${table}: Error inesperado`);
      unavailableTables.push(table);
    }
  }

  console.log('\n📊 RESUMEN:');
  console.log(`✅ Tablas disponibles: ${availableTables.length}`);
  console.log(`❌ Tablas no disponibles: ${unavailableTables.length}`);

  if (availableTables.length > 0) {
    console.log('\n🔍 Verificando estructura de tablas disponibles...');
    
    for (const table of availableTables.slice(0, 3)) { // Solo las primeras 3 para no saturar
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);

        if (!error && data && data.length > 0) {
          console.log(`\n📋 Estructura de ${table}:`);
          const columns = Object.keys(data[0]);
          columns.forEach(col => {
            const value = data[0][col];
            const type = typeof value;
            console.log(`   - ${col}: ${type} (ejemplo: ${value})`);
          });
        }
      } catch (err) {
        console.log(`   ❌ Error obteniendo estructura de ${table}`);
      }
    }
  }

  // Buscar tablas que contengan "respuesta" en el nombre
  console.log('\n🔍 Buscando tablas relacionadas con respuestas...');
  const responseTables = ['respuestas', 'respuesta', 'answers', 'responses'];
  
  for (const table of responseTables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true });

      if (!error) {
        console.log(`✅ Encontrada tabla de respuestas: ${table}`);
      }
    } catch (err) {
      // Silenciar errores para esta búsqueda
    }
  }
}

// Ejecutar la verificación
checkAvailableTables().catch(console.error);
