// Tipos para el Dashboard
export interface DashboardMetric {
  id: string;
  label: string;
  value: number;
  trend?: number;
  icon?: string;
  color?: string;
}

export interface DashboardFilter {
  institutionId?: string;
  groupId?: string;
  startDate?: string;
  endDate?: string;
  patientType?: string;
  role?: string;
}

export interface DashboardData {
  totalPatients: number;
  totalInstitutions: number;
  totalResults: number;
  averageScore: number;
  recentResults: any[];
  rawResponses: any[];
  institutionMetrics: InstitutionMetric[];
  patientMetrics: PatientMetric[];
  resultMetrics: ResultMetric[];
}

export interface InstitutionMetric {
  id: string;
  name: string;
  patientCount: number;
  resultCount: number;
  averageScore: number;
  lastActivity: string;
}

export interface PatientMetric {
  id: string;
  name: string;
  institutionName: string;
  resultCount: number;
  averageScore: number;
  lastResultDate: string;
}

export interface ResultMetric {
  id: string;
  patientName: string;
  institutionName: string;
  score: number;
  createdAt: string;
  testType: string;
}

export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string[];
    borderColor?: string[];
    borderWidth?: number;
  }[];
}

export interface WidgetConfig {
  id: string;
  type: 'metric' | 'chart' | 'table' | 'kpi';
  title: string;
  dataKey?: string;
  size: 'small' | 'medium' | 'large';
  refreshInterval?: number;
}

export interface DashboardState {
  data: DashboardData | null;
  loading: boolean;
  error: string | null;
  filters: DashboardFilter;
  lastUpdated: string | null;
}