import { useState, useCallback, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { DashboardFilter } from '../../types/dashboard';

// Mapeo de claves de filtro a claves de URL para mayor claridad y mantenibilidad
const URL_KEY_MAP: Partial<Record<keyof DashboardFilter, string>> = {
  institutionId: 'institution',
  groupId: 'course',
  patientType: 'gender',
};

// Mapeo inverso para obtener filtros desde la URL
const FILTER_KEY_MAP: Record<string, keyof DashboardFilter> = {
  institution: 'institutionId',
  course: 'groupId',
  gender: 'patientType',
  startDate: 'startDate',
  endDate: 'endDate',
  role: 'role',
};

// Función para obtener filtros iniciales desde la URL
const getInitialFilters = (searchParams: URLSearchParams): DashboardFilter => {
  const filters: DashboardFilter = {
    institutionId: undefined,
    groupId: '6B', // Filtro por defecto para mostrar el curso 6ºB
    startDate: undefined,
    endDate: undefined,
    patientType: undefined,
    role: undefined,
  };

  // Iterar sobre los parámetros de la URL y mapearlos a las claves de filtro
  searchParams.forEach((value, key) => {
    const filterKey = FILTER_KEY_MAP[key];
    if (filterKey && value) {
      filters[filterKey] = value;
    }
  });

  return filters;
};

export const useDashboardFilters = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [filters, setFilters] = useState<DashboardFilter>(() => getInitialFilters(searchParams));

  // Sincronizar el estado con la URL cuando cambian los filtros
  useEffect(() => {
    const newSearchParams = new URLSearchParams();
    
    // Solo agregar parámetros que tengan valores válidos
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== 'all' && value !== undefined && value !== '') {
        // Usar el mapeo centralizado para obtener la clave de URL
        const urlKey = URL_KEY_MAP[key as keyof DashboardFilter] || key;
        newSearchParams.set(urlKey, value);
      }
    });
    
    // Actualizar la URL sin comparar con searchParams para evitar bucle infinito
    setSearchParams(newSearchParams, { replace: true });
  }, [filters, setSearchParams]);

  const updateFilter = useCallback((key: keyof DashboardFilter, value: string | undefined) => {
    // Normalizar el valor: 'all', '', o null se convierten en undefined
    const normalizedValue = (!value || value === 'all' || value === '') ? undefined : value;
    
    // Crear un nuevo objeto para asegurar el re-renderizado
    setFilters(prevFilters => ({
      ...prevFilters,
      [key]: normalizedValue
    }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({
      institutionId: undefined,
      groupId: undefined,
      startDate: undefined,
      endDate: undefined,
      patientType: undefined,
      role: undefined,
    });
  }, []);

  const setDateRange = useCallback((startDate: string, endDate: string) => {
    setFilters(prevFilters => ({
      ...prevFilters,
      startDate: startDate || undefined,
      endDate: endDate || undefined
    }));
  }, []);

  const hasActiveFilters = useCallback(() => {
    return Object.values(filters).some(value => value !== undefined && value !== '');
  }, [filters]);

  const getActiveFiltersCount = useCallback(() => {
    return Object.values(filters).filter(value => value !== undefined && value !== '').length;
  }, [filters]);

  return {
    filters,
    updateFilter,
    clearFilters,
    setDateRange,
    hasActiveFilters,
    getActiveFiltersCount
  };
};