/**
 * Script para verificar que los datos reales se importaron correctamente
 */

import { createClient } from '@supabase/supabase-js';

// Configuración de Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'http://localhost:54321';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';

const supabase = createClient(supabaseUrl, supabaseKey);

async function verificarImportacion() {
  console.log('🔍 Verificando importación de datos reales...');
  console.log('=' .repeat(50));
  
  try {
    // Verificar estudiantes
    const { count: totalEstudiantes, error: errorEstudiantes } = await supabase
      .from('estudiantes')
      .select('*', { count: 'exact', head: true });
    
    if (errorEstudiantes) {
      console.error('❌ Error verificando estudiantes:', errorEstudiantes);
    } else {
      console.log(`📚 Estudiantes: ${totalEstudiantes}`);
    }
    
    // Verificar respuestas
    const { count: totalRespuestas, error: errorRespuestas } = await supabase
      .from('respuestas')
      .select('*', { count: 'exact', head: true });
    
    if (errorRespuestas) {
      console.error('❌ Error verificando respuestas:', errorRespuestas);
    } else {
      console.log(`📝 Respuestas: ${totalRespuestas}`);
    }
    
    // Verificar nominaciones sociométricas
    const { count: totalNominaciones, error: errorNominaciones } = await supabase
      .from('nominaciones_sociometricas')
      .select('*', { count: 'exact', head: true });
    
    if (errorNominaciones) {
      console.error('❌ Error verificando nominaciones:', errorNominaciones);
    } else {
      console.log(`🤝 Nominaciones sociométricas: ${totalNominaciones}`);
    }
    
    // Verificar vistas
    const { data: vistaSociometrica, error: errorVistaSocio } = await supabase
      .from('view_sociometric_stats')
      .select('*')
      .limit(5);
    
    if (errorVistaSocio) {
      console.error('❌ Error en vista sociométrica:', errorVistaSocio);
    } else {
      console.log(`📊 Vista sociométrica: ${vistaSociometrica?.length || 0} registros`);
    }
    
    const { data: vistaConvivencia, error: errorVistaConv } = await supabase
      .from('view_convivencia_metrics')
      .select('*')
      .limit(5);
    
    if (errorVistaConv) {
      console.error('❌ Error en vista convivencia:', errorVistaConv);
    } else {
      console.log(`📈 Vista convivencia: ${vistaConvivencia?.length || 0} registros`);
    }
    
    // Probar función de métricas con un grupo real
    const { data: grupos } = await supabase
      .from('estudiantes')
      .select('grupo_id')
      .limit(1);
    
    if (grupos && grupos.length > 0) {
      const grupoId = grupos[0].grupo_id;
      
      const { data: metricas, error: errorMetricas } = await supabase
        .rpc('calcular_metricas_grupo', { p_grupo_id: grupoId });
      
      if (errorMetricas) {
        console.error('❌ Error en función de métricas:', errorMetricas);
      } else {
        console.log('✅ Función de métricas operativa');
        console.log('📊 Métricas de ejemplo:', JSON.stringify(metricas, null, 2));
      }
    }
    
    console.log('=' .repeat(50));
    
    if (totalEstudiantes > 0 && totalRespuestas > 0) {
      console.log('🎉 ¡Datos reales importados correctamente!');
      console.log('El sistema optimizado está listo para usar con datos reales.');
    } else {
      console.log('⚠️  Faltan datos por importar.');
    }
    
  } catch (error) {
    console.error('❌ Error durante la verificación:', error);
  }
}

// Ejecutar verificación
verificarImportacion();