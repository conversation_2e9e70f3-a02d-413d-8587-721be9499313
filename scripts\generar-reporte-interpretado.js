import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Configurar dotenv
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuración de Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Error: Variables de entorno VITE_SUPABASE_URL y VITE_SUPABASE_ANON_KEY son requeridas');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function obtenerDatosCompletos() {
  try {
    console.log('📊 Obteniendo datos completos...');
    
    // Obtener estudiantes
    const { data: estudiantes, error: errorEstudiantes } = await supabase
      .from('estudiantes')
      .select('*')
      .order('nombre_estudiante');
    
    if (errorEstudiantes) {
      throw new Error(`Error al obtener estudiantes: ${errorEstudiantes.message}`);
    }
    
    console.log(`✅ ${estudiantes.length} estudiantes obtenidos`);
    
    // Obtener respuestas
    const { data: respuestas, error: errorRespuestas } = await supabase
      .from('respuestas')
      .select('*')
      .order('estudiante_id, pregunta_id');
    
    if (errorRespuestas) {
      throw new Error(`Error al obtener respuestas: ${errorRespuestas.message}`);
    }
    
    console.log(`✅ ${respuestas.length} respuestas obtenidas`);
    
    // Obtener preguntas
    const { data: preguntas, error: errorPreguntas } = await supabase
      .from('preguntas')
      .select('*')
      .order('numero_pregunta');
    
    if (errorPreguntas) {
      console.log('⚠️ No se pudieron obtener preguntas:', errorPreguntas.message);
    }
    
    console.log(`✅ ${preguntas?.length || 0} preguntas obtenidas`);
    
    // Obtener opciones de respuesta
    const { data: opciones, error: errorOpciones } = await supabase
      .from('opciones_respuesta')
      .select('*');
    
    if (errorOpciones) {
      console.log('⚠️ No se pudieron obtener opciones:', errorOpciones.message);
    }
    
    console.log(`✅ ${opciones?.length || 0} opciones obtenidas`);
    
    // Obtener grupos
    const { data: grupos, error: errorGrupos } = await supabase
      .from('grupos')
      .select('*');
    
    if (errorGrupos) {
      console.log('⚠️ No se pudieron obtener grupos:', errorGrupos.message);
    }
    
    console.log(`✅ ${grupos?.length || 0} grupos obtenidos`);
    
    return {
      estudiantes,
      respuestas,
      preguntas: preguntas || [],
      opciones: opciones || [],
      grupos: grupos || []
    };
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    throw error;
  }
}

function crearMapeos(datos) {
  const mapeos = {
    estudiantes: {},
    preguntas: {},
    opciones: {},
    grupos: {}
  };
  
  // Mapeo de estudiantes
  datos.estudiantes.forEach(est => {
    mapeos.estudiantes[est.id] = {
      nombre_completo: `${est.nombre_estudiante} ${est.apellido_estudiante}`,
      codigo: est.codigo_anonimizado,
      grado: est.grado
    };
  });
  
  // Mapeo de preguntas
  datos.preguntas.forEach(preg => {
    mapeos.preguntas[preg.id] = {
      numero: preg.numero_pregunta,
      texto: preg.texto_pregunta,
      tipo: preg.tipo_pregunta
    };
  });
  
  // Mapeo de opciones
  datos.opciones.forEach(opc => {
    mapeos.opciones[opc.id] = {
      texto: opc.texto_opcion,
      valor: opc.valor_opcion
    };
  });
  
  // Mapeo de grupos
  datos.grupos.forEach(grupo => {
    mapeos.grupos[grupo.id] = {
      nombre: grupo.nombre_grupo,
      descripcion: grupo.descripcion
    };
  });
  
  return mapeos;
}

function interpretarRespuesta(respuesta, mapeos) {
  const respuestaInterpretada = {
    pregunta_id: respuesta.pregunta_id,
    pregunta_info: mapeos.preguntas[respuesta.pregunta_id] || {
      numero: 'Desconocida',
      texto: 'Pregunta no encontrada',
      tipo: 'unknown'
    },
    respuesta_original: respuesta.respuesta_texto,
    respuesta_interpretada: null,
    opcion_respuesta_id: respuesta.opcion_respuesta_id,
    fecha_respuesta: respuesta.fecha_respuesta
  };
  
  // Interpretar respuesta según el tipo
  if (respuesta.respuesta_texto) {
    try {
      // Intentar parsear como JSON (para respuestas múltiples)
      const parsed = JSON.parse(respuesta.respuesta_texto);
      
      if (Array.isArray(parsed)) {
        // Es un array de IDs de estudiantes
        respuestaInterpretada.respuesta_interpretada = {
          tipo: 'seleccion_multiple_estudiantes',
          estudiantes_seleccionados: parsed.map(id => ({
            id: id,
            nombre: mapeos.estudiantes[id]?.nombre_completo || 'Estudiante no encontrado',
            codigo: mapeos.estudiantes[id]?.codigo || 'N/A'
          }))
        };
      } else if (typeof parsed === 'object' && parsed.selected) {
        // Es un objeto con selecciones
        respuestaInterpretada.respuesta_interpretada = {
          tipo: 'seleccion_con_otro',
          seleccionados: parsed.selected,
          otro: parsed.other || ''
        };
      } else {
        respuestaInterpretada.respuesta_interpretada = {
          tipo: 'json_complejo',
          valor: parsed
        };
      }
    } catch (e) {
      // No es JSON, es texto plano
      respuestaInterpretada.respuesta_interpretada = {
        tipo: 'texto_libre',
        valor: respuesta.respuesta_texto
      };
    }
  }
  
  // Interpretar opción de respuesta si existe
  if (respuesta.opcion_respuesta_id && mapeos.opciones[respuesta.opcion_respuesta_id]) {
    respuestaInterpretada.opcion_interpretada = mapeos.opciones[respuesta.opcion_respuesta_id];
  }
  
  return respuestaInterpretada;
}

async function generarReporteInterpretado(datos, mapeos) {
  console.log('📝 Generando reporte interpretado...');
  
  // Agrupar respuestas por estudiante
  const respuestasPorEstudiante = {};
  datos.respuestas.forEach(respuesta => {
    if (!respuestasPorEstudiante[respuesta.estudiante_id]) {
      respuestasPorEstudiante[respuesta.estudiante_id] = [];
    }
    respuestasPorEstudiante[respuesta.estudiante_id].push(
      interpretarRespuesta(respuesta, mapeos)
    );
  });
  
  // Crear reporte completo
  const reporteCompleto = {
    metadata: {
      fecha_generacion: new Date().toISOString(),
      total_estudiantes: datos.estudiantes.length,
      total_respuestas: datos.respuestas.length,
      estudiantes_con_respuestas: Object.keys(respuestasPorEstudiante).length,
      estudiantes_sin_respuestas: datos.estudiantes.length - Object.keys(respuestasPorEstudiante).length
    },
    mapeos_referencia: mapeos,
    estudiantes: datos.estudiantes.map(estudiante => {
      const respuestasEstudiante = respuestasPorEstudiante[estudiante.id] || [];
      
      return {
        informacion_estudiante: {
          id: estudiante.id,
          nombre_completo: `${estudiante.nombre_estudiante} ${estudiante.apellido_estudiante}`,
          codigo_anonimizado: estudiante.codigo_anonimizado,
          genero: estudiante.genero,
          grado: estudiante.grado,
          edad: estudiante.edad,
          numero_documento: estudiante.numero_documento,
          grupo: mapeos.grupos[estudiante.grupo_id] || { nombre: 'Grupo no encontrado' },
          fecha_creacion: estudiante.fecha_creacion
        },
        respuestas_interpretadas: respuestasEstudiante,
        resumen_respuestas: {
          total_respuestas: respuestasEstudiante.length,
          tipos_respuesta: respuestasEstudiante.reduce((acc, resp) => {
            const tipo = resp.respuesta_interpretada?.tipo || 'sin_interpretar';
            acc[tipo] = (acc[tipo] || 0) + 1;
            return acc;
          }, {})
        }
      };
    })
  };
  
  return reporteCompleto;
}

async function generarReporteCSVInterpretado(reporte) {
  console.log('📊 Generando CSV interpretado...');
  
  const nombreArchivo = `reporte-interpretado-${new Date().toISOString().split('T')[0]}.csv`;
  const rutaArchivo = path.join(__dirname, '..', nombreArchivo);
  
  let csvContent = 'ID_Estudiante,Nombre_Completo,Codigo,Genero,Grado,Edad,Grupo,Pregunta_Numero,Pregunta_Texto,Tipo_Respuesta,Respuesta_Interpretada,Fecha_Respuesta\n';
  
  reporte.estudiantes.forEach(item => {
    const estudiante = item.informacion_estudiante;
    
    if (item.respuestas_interpretadas.length === 0) {
      // Estudiante sin respuestas
      csvContent += `"${estudiante.id}","${estudiante.nombre_completo}","${estudiante.codigo_anonimizado}","${estudiante.genero}","${estudiante.grado}",${estudiante.edad},"${estudiante.grupo.nombre}",,,Sin respuestas,,\n`;
    } else {
      // Estudiante con respuestas
      item.respuestas_interpretadas.forEach(respuesta => {
        let respuestaTexto = '';
        
        if (respuesta.respuesta_interpretada) {
          switch (respuesta.respuesta_interpretada.tipo) {
            case 'seleccion_multiple_estudiantes':
              respuestaTexto = respuesta.respuesta_interpretada.estudiantes_seleccionados
                .map(est => `${est.nombre} (${est.codigo})`)
                .join('; ');
              break;
            case 'seleccion_con_otro':
              respuestaTexto = `Seleccionados: ${respuesta.respuesta_interpretada.seleccionados.join(', ')}${respuesta.respuesta_interpretada.otro ? '; Otro: ' + respuesta.respuesta_interpretada.otro : ''}`;
              break;
            case 'texto_libre':
              respuestaTexto = respuesta.respuesta_interpretada.valor;
              break;
            default:
              respuestaTexto = JSON.stringify(respuesta.respuesta_interpretada.valor);
          }
        }
        
        csvContent += `"${estudiante.id}","${estudiante.nombre_completo}","${estudiante.codigo_anonimizado}","${estudiante.genero}","${estudiante.grado}",${estudiante.edad},"${estudiante.grupo.nombre}","${respuesta.pregunta_info.numero}","${respuesta.pregunta_info.texto.replace(/"/g, '""')}","${respuesta.respuesta_interpretada?.tipo || 'sin_interpretar'}","${respuestaTexto.replace(/"/g, '""')}","${respuesta.fecha_respuesta}"\n`;
      });
    }
  });
  
  fs.writeFileSync(rutaArchivo, csvContent, 'utf8');
  console.log(`📊 CSV interpretado generado: ${rutaArchivo}`);
  
  return rutaArchivo;
}

async function main() {
  try {
    console.log('🚀 Iniciando generación de reporte interpretado...');
    
    // Obtener todos los datos
    const datos = await obtenerDatosCompletos();
    
    // Crear mapeos para interpretación
    console.log('🔄 Creando mapeos de interpretación...');
    const mapeos = crearMapeos(datos);
    
    // Generar reporte interpretado
    const reporteInterpretado = await generarReporteInterpretado(datos, mapeos);
    
    // Guardar reporte JSON interpretado
    const nombreArchivoJSON = `reporte-interpretado-${new Date().toISOString().split('T')[0]}.json`;
    const rutaArchivoJSON = path.join(__dirname, '..', nombreArchivoJSON);
    fs.writeFileSync(rutaArchivoJSON, JSON.stringify(reporteInterpretado, null, 2), 'utf8');
    console.log(`📄 Reporte JSON interpretado generado: ${rutaArchivoJSON}`);
    
    // Generar CSV interpretado
    const rutaCSV = await generarReporteCSVInterpretado(reporteInterpretado);
    
    // Generar resumen mejorado
    const resumenTexto = `REPORTE INTERPRETADO DE ESTUDIANTES Y RESPUESTAS\n` +
      `Fecha de generación: ${new Date().toLocaleString('es-ES')}\n` +
      `=======================================================\n\n` +
      `RESUMEN GENERAL:\n` +
      `- Total de estudiantes: ${reporteInterpretado.metadata.total_estudiantes}\n` +
      `- Total de respuestas: ${reporteInterpretado.metadata.total_respuestas}\n` +
      `- Estudiantes con respuestas: ${reporteInterpretado.metadata.estudiantes_con_respuestas}\n` +
      `- Estudiantes sin respuestas: ${reporteInterpretado.metadata.estudiantes_sin_respuestas}\n\n` +
      `MAPEOS DISPONIBLES:\n` +
      `- Estudiantes mapeados: ${Object.keys(mapeos.estudiantes).length}\n` +
      `- Preguntas mapeadas: ${Object.keys(mapeos.preguntas).length}\n` +
      `- Opciones mapeadas: ${Object.keys(mapeos.opciones).length}\n` +
      `- Grupos mapeados: ${Object.keys(mapeos.grupos).length}\n\n` +
      `TIPOS DE RESPUESTA ENCONTRADOS:\n` +
      reporteInterpretado.estudiantes
        .flatMap(est => est.respuestas_interpretadas)
        .reduce((acc, resp) => {
          const tipo = resp.respuesta_interpretada?.tipo || 'sin_interpretar';
          acc[tipo] = (acc[tipo] || 0) + 1;
          return acc;
        }, {})
        .toString()
        .split(',')
        .map(item => `- ${item}\n`)
        .join('');
    
    const nombreResumen = `resumen-interpretado-${new Date().toISOString().split('T')[0]}.txt`;
    const rutaResumen = path.join(__dirname, '..', nombreResumen);
    fs.writeFileSync(rutaResumen, resumenTexto, 'utf8');
    console.log(`📈 Resumen interpretado generado: ${rutaResumen}`);
    
    console.log('\n✅ Proceso completado exitosamente!');
    console.log('\n📁 Archivos generados:');
    console.log(`   - JSON interpretado: ${path.basename(rutaArchivoJSON)}`);
    console.log(`   - CSV interpretado: ${path.basename(rutaCSV)}`);
    console.log(`   - Resumen: ${path.basename(rutaResumen)}`);
    
  } catch (error) {
    console.error('❌ Error en el proceso:', error.message);
    process.exit(1);
  }
}

// Ejecutar si es llamado directamente
if (import.meta.url === `file://${process.argv[1]}` || process.argv[1].endsWith(path.basename(__filename))) {
  main();
}

// También ejecutar directamente como fallback para Windows
main();

export { obtenerDatosCompletos, crearMapeos, interpretarRespuesta, generarReporteInterpretado };