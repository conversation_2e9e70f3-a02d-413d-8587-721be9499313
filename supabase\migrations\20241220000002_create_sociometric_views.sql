-- Migración para crear vistas de análisis sociométrico
-- Estas vistas centralizan los cálculos de métricas en la base de datos

-- Vista para estadísticas sociométricas básicas
CREATE OR REPLACE VIEW public.view_sociometric_stats AS
WITH nominaciones AS (
    -- Extraer nominaciones de la tabla de respuestas (datos existentes)
    SELECT 
        r.grupo_id,
        r.estudiante_id AS elector_id,
        p.tipo_pregunta,
        -- Convertir JSON array a filas individuales
        jsonb_array_elements_text(r.respuesta_texto::jsonb) AS elegido_id
    FROM 
        public.respuestas r
    JOIN 
        public.preguntas p ON r.pregunta_id = p.id
    WHERE 
        p.categoria = 'sociometrica' 
        AND r.respuesta_texto IS NOT NULL 
        AND r.respuesta_texto <> '[]'
        AND r.respuesta_texto <> 'null'
    
    UNION ALL
    
    -- Incluir nominaciones de la nueva tabla optimizada
    SELECT 
        ns.grupo_id,
        ns.evaluador_id AS elector_id,
        ns.tipo_nominacion AS tipo_pregunta,
        ns.evaluado_id::text AS elegido_id
    FROM 
        public.nominaciones_sociometricas ns
),
elecciones_recibidas AS (
    SELECT 
        n.elegido_id::uuid,
        n.grupo_id,
        COUNT(*) AS total_elecciones
    FROM nominaciones n
    WHERE n.tipo_pregunta = 'eleccion'
    GROUP BY n.elegido_id, n.grupo_id
),
rechazos_recibidos AS (
    SELECT 
        n.elegido_id::uuid,
        n.grupo_id,
        COUNT(*) AS total_rechazos
    FROM nominaciones n
    WHERE n.tipo_pregunta = 'rechazo'
    GROUP BY n.elegido_id, n.grupo_id
),
estadisticas_grupo AS (
    SELECT 
        grupo_id,
        COUNT(*) AS total_estudiantes,
        AVG(COALESCE(er.total_elecciones, 0)) AS promedio_elecciones,
        AVG(COALESCE(rr.total_rechazos, 0)) AS promedio_rechazos
    FROM public.estudiantes e
    LEFT JOIN elecciones_recibidas er ON e.id = er.elegido_id
    LEFT JOIN rechazos_recibidos rr ON e.id = rr.elegido_id
    GROUP BY grupo_id
)
SELECT 
    e.id AS estudiante_id,
    e.grupo_id,
    g.nombre AS nombre_grupo,
    e.nombre_estudiante,
    e.apellido_estudiante,
    COALESCE(er.total_elecciones, 0) AS elecciones_recibidas,
    COALESCE(rr.total_rechazos, 0) AS rechazos_recibidos,
    eg.total_estudiantes,
    eg.promedio_elecciones,
    eg.promedio_rechazos,
    -- Calcular índice sociométrico (elecciones - rechazos)
    (COALESCE(er.total_elecciones, 0) - COALESCE(rr.total_rechazos, 0)) AS indice_sociometrico,
    -- Clasificación sociométrica estándar
    CASE 
        WHEN COALESCE(er.total_elecciones, 0) >= eg.promedio_elecciones + (0.5 * eg.promedio_elecciones) 
             AND COALESCE(rr.total_rechazos, 0) <= eg.promedio_rechazos 
        THEN 'popular'
        WHEN COALESCE(er.total_elecciones, 0) <= eg.promedio_elecciones * 0.5 
             AND COALESCE(rr.total_rechazos, 0) >= eg.promedio_rechazos + (0.5 * eg.promedio_rechazos)
        THEN 'rechazado'
        WHEN COALESCE(er.total_elecciones, 0) <= eg.promedio_elecciones * 0.5 
             AND COALESCE(rr.total_rechazos, 0) <= eg.promedio_rechazos * 0.5
        THEN 'aislado'
        WHEN COALESCE(er.total_elecciones, 0) >= eg.promedio_elecciones 
             AND COALESCE(rr.total_rechazos, 0) >= eg.promedio_rechazos
        THEN 'controvertido'
        ELSE 'promedio'
    END AS estatus_sociometrico
FROM 
    public.estudiantes e
LEFT JOIN 
    elecciones_recibidas er ON e.id = er.elegido_id
LEFT JOIN 
    rechazos_recibidos rr ON e.id = rr.elegido_id
JOIN 
    public.grupos g ON e.grupo_id = g.id
JOIN 
    estadisticas_grupo eg ON e.grupo_id = eg.grupo_id;

-- Vista para métricas de convivencia mejoradas
CREATE OR REPLACE VIEW public.view_convivencia_metrics AS
WITH bullying_responses AS (
    SELECT 
        r.estudiante_id,
        r.grupo_id,
        p.pregunta,
        r.respuesta_numerica,
        p.id as pregunta_id,
        -- Clasificar tipos de bullying según la pregunta
        CASE 
            WHEN p.pregunta ILIKE '%físic%' OR p.pregunta ILIKE '%golpe%' OR p.pregunta ILIKE '%empuj%' THEN 'fisico'
            WHEN p.pregunta ILIKE '%verbal%' OR p.pregunta ILIKE '%insult%' OR p.pregunta ILIKE '%burla%' THEN 'verbal'
            WHEN p.pregunta ILIKE '%social%' OR p.pregunta ILIKE '%exclu%' OR p.pregunta ILIKE '%aislam%' THEN 'social'
            WHEN p.pregunta ILIKE '%cyber%' OR p.pregunta ILIKE '%internet%' OR p.pregunta ILIKE '%redes%' THEN 'cibernetico'
            ELSE 'general'
        END AS tipo_bullying,
        -- Clasificar rol según la pregunta
        CASE 
            WHEN p.pregunta ILIKE '%haces%' OR p.pregunta ILIKE '%realizas%' THEN 'agresor'
            WHEN p.pregunta ILIKE '%sufres%' OR p.pregunta ILIKE '%recibes%' THEN 'victima'
            WHEN p.pregunta ILIKE '%observas%' OR p.pregunta ILIKE '%ves%' THEN 'observador'
            ELSE 'general'
        END AS rol_bullying
    FROM 
        public.respuestas r
    JOIN 
        public.preguntas p ON r.pregunta_id = p.id
    WHERE 
        p.categoria = 'bullying'
        AND r.respuesta_numerica IS NOT NULL
),
metricas_estudiante AS (
    SELECT 
        estudiante_id,
        grupo_id,
        -- Métricas por rol
        AVG(CASE WHEN rol_bullying = 'agresor' THEN respuesta_numerica END) AS nivel_agresion,
        AVG(CASE WHEN rol_bullying = 'victima' THEN respuesta_numerica END) AS nivel_victimizacion,
        AVG(CASE WHEN rol_bullying = 'observador' THEN respuesta_numerica END) AS nivel_observacion,
        -- Métricas por tipo
        AVG(CASE WHEN tipo_bullying = 'fisico' THEN respuesta_numerica END) AS bullying_fisico,
        AVG(CASE WHEN tipo_bullying = 'verbal' THEN respuesta_numerica END) AS bullying_verbal,
        AVG(CASE WHEN tipo_bullying = 'social' THEN respuesta_numerica END) AS bullying_social,
        AVG(CASE WHEN tipo_bullying = 'cibernetico' THEN respuesta_numerica END) AS bullying_cibernetico,
        -- Indicadores de riesgo
        MAX(respuesta_numerica) AS max_respuesta,
        COUNT(*) AS total_respuestas
    FROM bullying_responses
    GROUP BY estudiante_id, grupo_id
)
SELECT 
    e.id AS estudiante_id,
    e.grupo_id,
    g.nombre AS nombre_grupo,
    e.nombre_estudiante,
    e.apellido_estudiante,
    -- Métricas de bullying
    COALESCE(me.nivel_agresion, 0) AS nivel_agresion,
    COALESCE(me.nivel_victimizacion, 0) AS nivel_victimizacion,
    COALESCE(me.nivel_observacion, 0) AS nivel_observacion,
    COALESCE(me.bullying_fisico, 0) AS bullying_fisico,
    COALESCE(me.bullying_verbal, 0) AS bullying_verbal,
    COALESCE(me.bullying_social, 0) AS bullying_social,
    COALESCE(me.bullying_cibernetico, 0) AS bullying_cibernetico,
    -- Métricas sociométricas
    COALESCE(vs.elecciones_recibidas, 0) AS elecciones_recibidas,
    COALESCE(vs.rechazos_recibidos, 0) AS rechazos_recibidos,
    COALESCE(vs.estatus_sociometrico, 'sin_datos') AS estatus_sociometrico,
    -- Indicadores de riesgo
    CASE 
        WHEN me.nivel_victimizacion >= 3 OR me.nivel_agresion >= 3 THEN 'alto'
        WHEN me.nivel_victimizacion >= 2 OR me.nivel_agresion >= 2 THEN 'medio'
        WHEN me.nivel_victimizacion >= 1 OR me.nivel_agresion >= 1 THEN 'bajo'
        ELSE 'sin_riesgo'
    END AS nivel_riesgo_bullying,
    CASE 
        WHEN vs.estatus_sociometrico IN ('rechazado', 'aislado') THEN 'alto'
        WHEN vs.estatus_sociometrico = 'controvertido' THEN 'medio'
        ELSE 'bajo'
    END AS nivel_riesgo_social
FROM 
    public.estudiantes e
JOIN 
    public.grupos g ON e.grupo_id = g.id
LEFT JOIN 
    metricas_estudiante me ON e.id = me.estudiante_id
LEFT JOIN 
    public.view_sociometric_stats vs ON e.id = vs.estudiante_id;

-- Habilitar RLS en las vistas
ALTER VIEW public.view_sociometric_stats SET (security_invoker = true);
ALTER VIEW public.view_convivencia_metrics SET (security_invoker = true);