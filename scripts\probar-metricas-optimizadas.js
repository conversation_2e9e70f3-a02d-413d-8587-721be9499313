import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function probarMetricasOptimizadas() {
  try {
    console.log('🔍 Probando sistema de métricas optimizado...');
    console.log('=' .repeat(60));

    // 1. Verificar conexión
    console.log('\n1. Verificando conexión a Supabase...');
    const { data: connection, error: connError } = await supabase
      .from('grupos')
      .select('id, nombre')
      .limit(1);
    
    if (connError) {
      throw new Error(`Error de conexión: ${connError.message}`);
    }
    console.log('✅ Conexión exitosa');

    // 2. Obtener grupos disponibles
    console.log('\n2. Obteniendo grupos disponibles...');
    const { data: grupos, error: gruposError } = await supabase
      .from('grupos')
      .select('id, nombre, grado')
      .limit(5);
    
    if (gruposError) {
      throw new Error(`Error obteniendo grupos: ${gruposError.message}`);
    }
    
    console.log(`📊 Grupos encontrados: ${grupos.length}`);
    grupos.forEach(grupo => {
      console.log(`   - ${grupo.nombre} (${grupo.grado})`);
    });

    if (grupos.length === 0) {
      console.log('⚠️  No hay grupos disponibles para probar');
      return;
    }

    const grupoTest = grupos[0];
    console.log(`\n🎯 Usando grupo de prueba: ${grupoTest.nombre}`);

    // 3. Probar vista de estadísticas sociométricas
    console.log('\n3. Probando vista de estadísticas sociométricas...');
    const { data: sociometricStats, error: socioError } = await supabase
      .from('view_sociometric_stats')
      .select('*')
      .eq('grupo_id', grupoTest.id)
      .limit(5);
    
    if (socioError) {
      console.log(`⚠️  Error en vista sociométrica: ${socioError.message}`);
    } else {
      console.log(`📈 Estadísticas sociométricas: ${sociometricStats.length} estudiantes`);
      if (sociometricStats.length > 0) {
        const ejemplo = sociometricStats[0];
        console.log(`   Ejemplo: ${ejemplo.nombre_estudiante} - Estatus: ${ejemplo.estatus_sociometrico}`);
        console.log(`   Elecciones: ${ejemplo.elecciones_recibidas}, Rechazos: ${ejemplo.rechazos_recibidos}`);
      }
    }

    // 4. Probar vista de métricas de convivencia
    console.log('\n4. Probando vista de métricas de convivencia...');
    const { data: convivenciaMetrics, error: convError } = await supabase
      .from('view_convivencia_metrics')
      .select('*')
      .eq('grupo_id', grupoTest.id)
      .limit(5);
    
    if (convError) {
      console.log(`⚠️  Error en vista de convivencia: ${convError.message}`);
    } else {
      console.log(`📊 Métricas de convivencia: ${convivenciaMetrics.length} estudiantes`);
      if (convivenciaMetrics.length > 0) {
        const ejemplo = convivenciaMetrics[0];
        console.log(`   Ejemplo: ${ejemplo.nombre_estudiante}`);
        console.log(`   - Agresión: ${ejemplo.promedio_agresion?.toFixed(2) || 'N/A'}`);
        console.log(`   - Victimización: ${ejemplo.promedio_victimizacion?.toFixed(2) || 'N/A'}`);
        console.log(`   - Alto riesgo: ${ejemplo.alto_riesgo_bullying ? 'Sí' : 'No'}`);
        console.log(`   - Estatus social: ${ejemplo.estatus_sociometrico}`);
      }
    }

    // 5. Probar función de métricas de grupo
    console.log('\n5. Probando función de métricas de grupo...');
    const { data: metricasGrupo, error: metricasError } = await supabase
      .rpc('calcular_metricas_grupo', { 
        p_grupo_id: grupoTest.id 
      });
    
    if (metricasError) {
      console.log(`⚠️  Error en función de métricas: ${metricasError.message}`);
    } else {
      console.log('📈 Métricas de grupo calculadas:');
      if (metricasGrupo && metricasGrupo.length > 0) {
        const metricas = metricasGrupo[0];
        console.log(`   - Total estudiantes: ${metricas.total_estudiantes}`);
        console.log(`   - Alto riesgo bullying: ${metricas.estudiantes_alto_riesgo_bullying} (${metricas.porcentaje_alto_riesgo_bullying?.toFixed(1)}%)`);
        console.log(`   - Aislamiento: ${metricas.porcentaje_aislamiento?.toFixed(1)}%`);
        console.log(`   - Cohesión: ${metricas.nivel_cohesion} (${metricas.indice_cohesion?.toFixed(1)}%)`);
        console.log(`   - Patrón predominante: ${metricas.patron_predominante}`);
        console.log(`   - Nivel de riesgo: ${metricas.nivel_riesgo_grupo}`);
      } else {
        console.log('   No hay datos de métricas disponibles');
      }
    }

    // 6. Probar función de resumen ejecutivo
    console.log('\n6. Probando función de resumen ejecutivo...');
    const { data: resumen, error: resumenError } = await supabase
      .rpc('generar_resumen_convivencia', { 
        p_grupo_id: grupoTest.id 
      });
    
    if (resumenError) {
      console.log(`⚠️  Error en resumen ejecutivo: ${resumenError.message}`);
    } else {
      console.log('📋 Resumen ejecutivo generado:');
      if (resumen && resumen.length > 0) {
        const res = resumen[0];
        console.log(`   - Estudiantes evaluados: ${res.total_estudiantes}`);
        console.log(`   - Estudiantes en riesgo: ${res.estudiantes_riesgo}`);
        console.log(`   - Nivel de riesgo: ${res.nivel_riesgo}`);
        console.log(`   - Descripción: ${res.descripcion}`);
        console.log(`   - Requiere intervención: ${res.requiere_intervencion ? 'Sí' : 'No'}`);
        
        if (res.recomendaciones && res.recomendaciones.length > 0) {
          console.log('   - Recomendaciones:');
          res.recomendaciones.forEach((rec, index) => {
            console.log(`     ${index + 1}. ${rec}`);
          });
        }
        
        if (res.alertas && res.alertas.length > 0) {
          console.log('   - Alertas:');
          res.alertas.forEach((alerta, index) => {
            console.log(`     ⚠️  ${alerta}`);
          });
        }
      } else {
        console.log('   No hay datos de resumen disponibles');
      }
    }

    // 7. Verificar datos de respuestas
    console.log('\n7. Verificando datos de respuestas...');
    const { data: respuestas, error: respError } = await supabase
      .from('respuestas')
      .select('id, estudiante_id, pregunta_id, opcion_respuesta_id, respuesta_texto')
      .eq('grupo_id', grupoTest.id)
      .limit(5);
    
    if (respError) {
      console.log(`⚠️  Error obteniendo respuestas: ${respError.message}`);
    } else {
      console.log(`📝 Respuestas encontradas: ${respuestas.length}`);
      if (respuestas.length > 0) {
        console.log('   Ejemplos de respuestas:');
        respuestas.slice(0, 3).forEach((resp, index) => {
          console.log(`   ${index + 1}. Estudiante: ${resp.estudiante_id?.substring(0, 8)}...`);
          console.log(`      Opción: ${resp.opcion_respuesta_id?.substring(0, 8) || 'N/A'}...`);
          console.log(`      Texto: ${resp.respuesta_texto?.substring(0, 50) || 'N/A'}...`);
        });
      }
    }

    console.log('\n' + '=' .repeat(60));
    console.log('✅ Prueba del sistema de métricas optimizado completada');
    console.log('\n📊 RESUMEN:');
    console.log(`   - Vistas creadas: ${socioError ? '❌' : '✅'} Sociométrica, ${convError ? '❌' : '✅'} Convivencia`);
    console.log(`   - Funciones RPC: ${metricasError ? '❌' : '✅'} Métricas, ${resumenError ? '❌' : '✅'} Resumen`);
    console.log(`   - Datos disponibles: ${respuestas?.length > 0 ? '✅' : '⚠️ '} Respuestas`);
    
    if (respuestas?.length === 0) {
      console.log('\n💡 NOTA: Para obtener métricas reales, es necesario:');
      console.log('   1. Que los estudiantes completen el cuestionario BULL-S');
      console.log('   2. Que las preguntas estén categorizadas correctamente');
      console.log('   3. Que existan respuestas sociométricas (elecciones/rechazos)');
    }

  } catch (error) {
    console.error('❌ Error durante la prueba:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Ejecutar la prueba
probarMetricasOptimizadas().catch(console.error);

export { probarMetricasOptimizadas };