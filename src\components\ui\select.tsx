import React from 'react';

// Check icon component
const Check = ({ className }: { className?: string }) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M5 13l4 4L19 7"
    />
  </svg>
);

interface SelectProps {
  value?: string;
  onValueChange?: (value: string) => void;
  children: React.ReactNode;
}

interface SelectTriggerProps {
  children: React.ReactNode;
  className?: string;
}

interface SelectContentProps {
  children: React.ReactNode;
}

interface SelectItemProps {
  value: string;
  children: React.ReactNode;
}

interface SelectValueProps {
  placeholder?: string;
}

export const Select: React.FC<SelectProps> = ({ value, onValueChange, children }) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const [selectedValue, setSelectedValue] = React.useState(value || '');
  const [optionsMap, setOptionsMap] = React.useState<Record<string, string>>({});

  // Sincronizar el estado interno cuando cambia el prop value
  React.useEffect(() => {
    setSelectedValue(value || '');
  }, [value]);

  // Construir mapa de opciones cuando cambian los children
  React.useEffect(() => {
    const newOptionsMap: Record<string, string> = {};
    
    const extractOptions = (children: React.ReactNode) => {
      React.Children.forEach(children, child => {
        if (React.isValidElement(child)) {
          if (child.type === SelectContent) {
            extractOptions(child.props.children);
          } else if (child.type === SelectItem) {
            newOptionsMap[child.props.value] = child.props.children;
          }
        }
      });
    };
    
    extractOptions(children);
    setOptionsMap(newOptionsMap);
  }, [children]);

  const handleSelect = (itemValue: string) => {
    setSelectedValue(itemValue);
    onValueChange?.(itemValue);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      {React.Children.map(children, child => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child, {
            ...child.props,
            isOpen,
            setIsOpen,
            selectedValue,
            optionsMap,
            onSelect: handleSelect,
          });
        }
        return child;
      })}
    </div>
  );
};

export const SelectTrigger: React.FC<SelectTriggerProps & any> = ({ 
  children, 
  className = '', 
  isOpen, 
  setIsOpen 
}) => {
  return (
    <button
      type="button"
      className={`flex h-10 w-full items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${className}`}
      onClick={() => setIsOpen?.(!isOpen)}
    >
      {children}
      <svg
        className="h-4 w-4 opacity-50"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M19 9l-7 7-7-7"
        />
      </svg>
    </button>
  );
};

export const SelectContent: React.FC<SelectContentProps & any> = ({ 
  children, 
  isOpen,
  selectedValue,
  onSelect 
}) => {
  if (!isOpen) return null;

  return (
    <div className="absolute top-full z-50 w-full rounded-md border border-gray-200 bg-white py-1 shadow-md">
      {React.Children.map(children, child => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child, {
            ...child.props,
            selectedValue,
            onSelect,
          });
        }
        return child;
      })}
    </div>
  );
};

export const SelectItem: React.FC<SelectItemProps & any> = ({ 
  value, 
  children, 
  onSelect,
  selectedValue 
}) => {
  const isSelected = selectedValue === value;
  
  return (
    <div
      className={`relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-gray-100 focus:bg-gray-100 data-[disabled]:pointer-events-none data-[disabled]:opacity-50 ${
        isSelected ? 'bg-gray-100 text-gray-900' : ''
      }`}
      onClick={() => onSelect?.(value)}
      data-select-value={value}
      data-state={isSelected ? 'checked' : 'unchecked'}
    >
      <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
        {isSelected && <Check className="h-4 w-4" />}
      </span>
      {children}
    </div>
  );
};

export const SelectValue: React.FC<SelectValueProps & any> = ({
  placeholder,
  selectedValue,
  optionsMap
}) => {
  let displayValue = placeholder;
  let hasValue = false;

  if (selectedValue && selectedValue !== '') {
    // Usar el mapa de opciones para obtener el texto correcto
    if (optionsMap && optionsMap[selectedValue]) {
      displayValue = optionsMap[selectedValue];
      hasValue = true;
    } else {
      // Fallback para valores conocidos
      displayValue = selectedValue;
      hasValue = true;
    }
  }

  return (
    <span className={hasValue ? '' : 'text-gray-500'}>
      {displayValue}
    </span>
  );
};