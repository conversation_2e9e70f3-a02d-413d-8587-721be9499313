import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { TrendingUp, TrendingDown } from 'lucide-react';

interface MetricsCardProps {
  title: string;
  value: number;
  trend?: number;
  icon?: string;
  color?: string;
  loading?: boolean;
}

export const MetricsCard: React.FC<MetricsCardProps> = ({
  title,
  value,
  trend,
  icon,
  color = 'blue',
  loading = false
}) => {
  const getIconColor = (color: string) => {
    const colors = {
      blue: 'text-blue-600',
      green: 'text-green-600',
      purple: 'text-purple-600',
      yellow: 'text-yellow-600',
      red: 'text-red-600'
    };
    return colors[color as keyof typeof colors] || 'text-blue-600';
  };

  const getIcon = (iconName: string) => {
    const icons = {
      users: '👥',
      building: '🏢',
      'clipboard-check': '📋',
      star: '⭐',
      heart: '❤️',
      brain: '🧠',
      chart: '📊'
    };
    return icons[iconName as keyof typeof icons] || '📊';
  };

  if (loading) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
          </CardTitle>
          <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            <div className="h-8 bg-gray-200 rounded w-16 animate-pulse"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className={`text-2xl ${getIconColor(color)}`}>
          {getIcon(icon || 'chart')}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value.toLocaleString()}</div>
        {trend !== undefined && (
          <p className="text-xs text-muted-foreground flex items-center mt-1">
            {trend > 0 ? (
              <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
            ) : (
              <TrendingDown className="h-3 w-3 mr-1 text-red-500" />
            )}
            <span className={trend > 0 ? 'text-green-500' : 'text-red-500'}>
              {Math.abs(trend)}%
            </span>
            <span className="ml-1">vs último mes</span>
          </p>
        )}
      </CardContent>
    </Card>
  );
};