import React, { useEffect, useState } from 'react';
import { Building } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../ui/select';
import { supabase } from '../../../lib/supabase';

interface InstitutionFilterProps {
  value?: string;
  onChange: (value: string | undefined) => void;
  placeholder?: string;
}

export const InstitutionFilter: React.FC<InstitutionFilterProps> = ({
  value,
  onChange,
  placeholder = 'Todas las instituciones'
}) => {
  const [institutions, setInstitutions] = useState<Array<{ id: string; nombre: string }>>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchInstitutions();
  }, []);

  const fetchInstitutions = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('instituciones_educativas')
        .select('id, nombre')
        .order('nombre');

      if (error) throw error;
      setInstitutions(data || []);
    } catch (error) {
      console.error('Error fetching institutions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (newValue: string) => {
    onChange(newValue === 'all' ? undefined : newValue);
  };

  return (
    <div className="flex items-center space-x-2">
      <Building className="h-4 w-4 text-muted-foreground" />
      <Select value={value || 'all'} onValueChange={handleChange}>
        <SelectTrigger className="w-[200px]">
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">{placeholder}</SelectItem>
          {institutions.map((institution) => (
            <SelectItem key={institution.id} value={institution.id}>
              {institution.nombre}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};