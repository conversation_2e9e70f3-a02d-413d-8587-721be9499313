import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '../../../ui/card';
import { Badge } from '../../../ui/badge';
import { Progress } from '../../../ui/progress';
import { 
  Users, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  Shield, 
  Target,
  BookOpen,
  Calendar,
  BarChart3
} from 'lucide-react';
import { ComparisonsData } from '../../../../types/bulls-dashboard';

interface CourseOverviewData {
  courseName: string;
  totalStudents: number;
  responseRate: number;
  lastUpdate: string;
  academicPeriod: string;
  riskLevel: 'bajo' | 'medio' | 'alto' | 'critico';
  cohesionLevel: number;
  safetyPerception: number;
  keyMetrics: {
    aggressors: number;
    victims: number;
    observers: number;
    notInvolved: number;
  };
  trends: {
    metric: string;
    value: number;
    change: number;
    trend: 'up' | 'down' | 'stable';
  }[];
  alerts: {
    id: string;
    type: string;
    severity: 'bajo' | 'medio' | 'alto' | 'critico';
    description: string;
  }[];
}

interface CourseOverviewPanelProps {
  data?: CourseOverviewData;
  onNavigateToProfiles?: (studentId?: string) => void;
  onNavigateToPanel?: (panelType: string, filters?: any) => void;
}

const getRiskLevelColor = (level: string) => {
  switch (level) {
    case 'bajo': return 'bg-green-100 text-green-800';
    case 'medio': return 'bg-yellow-100 text-yellow-800';
    case 'alto': return 'bg-orange-100 text-orange-800';
    case 'critico': return 'bg-red-100 text-red-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getSeverityColor = (severity: string) => {
  switch (severity) {
    case 'bajo': return 'bg-blue-100 text-blue-800';
    case 'medio': return 'bg-yellow-100 text-yellow-800';
    case 'alto': return 'bg-orange-100 text-orange-800';
    case 'critico': return 'bg-red-100 text-red-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const TrendIcon = ({ trend }: { trend: 'up' | 'down' | 'stable' }) => {
  switch (trend) {
    case 'up': return <TrendingUp className="h-4 w-4 text-green-600" />;
    case 'down': return <TrendingDown className="h-4 w-4 text-red-600" />;
    default: return <BarChart3 className="h-4 w-4 text-gray-600" />;
  }
};

export const CourseOverviewPanel: React.FC<CourseOverviewPanelProps> = ({ 
  data, 
  onNavigateToProfiles,
  onNavigateToPanel 
}) => {

  // Función para manejar click en alertas
  const handleAlertClick = (alert: any) => {
    if (alert.type.includes('Estudiante Aislado') || alert.type.includes('Víctima') || alert.type.includes('Agresor')) {
      onNavigateToPanel?.('profiles', { alertType: alert.type });
    } else if (alert.type.includes('Incidente') || alert.type.includes('Conflicto')) {
      onNavigateToPanel?.('context', { alertType: alert.type });
    }
  };
  if (!data) {
    return <div>Cargando datos del curso...</div>; // O un skeleton loader
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <BookOpen className="h-6 w-6 text-blue-600" />
                <div>
                  <CardTitle className="text-xl">{data.courseName}</CardTitle>
                  <p className="text-sm text-gray-600">Panorama General del Curso</p>
                </div>
              </div>
              <Badge className={getRiskLevelColor(data.riskLevel)}>
                Riesgo {data.riskLevel.charAt(0).toUpperCase() + data.riskLevel.slice(1)}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{data.totalStudents}</div>
                <div className="text-sm text-gray-600">Estudiantes</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{data.responseRate}%</div>
                <div className="text-sm text-gray-600">Participación</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{data.cohesionLevel}%</div>
                <div className="text-sm text-gray-600">Cohesión</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{data.safetyPerception}%</div>
                <div className="text-sm text-gray-600">Seguridad</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Calendar className="h-5 w-5" />
              <span>Información del Período</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <div className="text-sm text-gray-600">Período Académico</div>
              <div className="font-medium">{data.academicPeriod}</div>
            </div>
            <div>
              <div className="text-sm text-gray-600">Última Actualización</div>
              <div className="font-medium">{new Date(data.lastUpdate).toLocaleDateString()}</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Key Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Distribución de Roles</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{data.keyMetrics.aggressors}</div>
              <div className="text-sm text-red-700">Agresores</div>
              <div className="text-xs text-gray-600">
                {((data.keyMetrics.aggressors / data.totalStudents) * 100).toFixed(1)}%
              </div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">{data.keyMetrics.victims}</div>
              <div className="text-sm text-orange-700">Víctimas</div>
              <div className="text-xs text-gray-600">
                {((data.keyMetrics.victims / data.totalStudents) * 100).toFixed(1)}%
              </div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{data.keyMetrics.observers}</div>
              <div className="text-sm text-blue-700">Observadores</div>
              <div className="text-xs text-gray-600">
                {((data.keyMetrics.observers / data.totalStudents) * 100).toFixed(1)}%
              </div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{data.keyMetrics.notInvolved}</div>
              <div className="text-sm text-green-700">No Implicados</div>
              <div className="text-xs text-gray-600">
                {((data.keyMetrics.notInvolved / data.totalStudents) * 100).toFixed(1)}%
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Trends */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Tendencias y Evolución</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.trends.map((trend, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <TrendIcon trend={trend.trend} />
                  <div>
                    <div className="font-medium">{trend.metric}</div>
                    <div className="text-sm text-gray-600">
                      Valor actual: {trend.value}{trend.metric.includes('Porcentaje') || trend.metric.includes('Cohesión') || trend.metric.includes('Seguridad') || trend.metric.includes('Participación') ? '%' : ''}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className={`text-sm font-medium ${
                    trend.change > 0 ? 'text-green-600' : trend.change < 0 ? 'text-red-600' : 'text-gray-600'
                  }`}>
                    {trend.change > 0 ? '+' : ''}{trend.change}{trend.metric.includes('Incidentes') ? '' : '%'}
                  </div>
                  <div className="text-xs text-gray-500">vs período anterior</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Alerts */}
      {data.alerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              <span>Alertas Activas</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.alerts.map((alert) => (
                <div 
                  key={alert.id} 
                  className="flex items-start space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                  onClick={() => handleAlertClick(alert)}
                >
                  <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5" />
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="font-medium">{alert.type}</span>
                      <Badge className={getSeverityColor(alert.severity)}>
                        {alert.severity.charAt(0).toUpperCase() + alert.severity.slice(1)}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600">{alert.description}</p>
                    <p className="text-xs text-blue-600 mt-1">Click para ver detalles →</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};