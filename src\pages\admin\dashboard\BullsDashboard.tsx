import React, { useState, useCallback } from 'react';
import { DashboardLayout } from '../../../components/dashboard/layouts/DashboardLayout';
import { useDashboardData } from '../../../hooks/dashboard/useDashboardData';
import { useDashboardFilters } from '../../../hooks/dashboard/useDashboardFilters';
import { useBullsMetrics } from '../../../hooks/dashboard/useBullsMetrics';
import { BullsGlobalFilters } from '../../../components/dashboard/bulls/BullsGlobalFilters';
import { BullsKPICards } from '../../../components/dashboard/bulls/BullsKPICards';
import { BullsDetailPanels } from '../../../components/dashboard/bulls/BullsDetailPanels';
import { Button } from '../../../components/ui/button';
import { BullsDashboardContext, useBullsDashboard } from '../../../contexts/BullsDashboardContext';

// Componente interno que contiene la lógica y la vista del dashboard
const BullsDashboardContent: React.FC = () => {
  // El estado local como el panel activo se mantiene aquí
  const [activePanel, setActivePanel] = useState<'overview' | 'sociogram' | 'profiles' | 'context' | 'comparisons' | 'recommendations' | 'bulls-criteria'>('overview');

  // Los componentes hijos ahora usarán el hook `useBullsDashboard` para obtener estos datos
  return (
    <>
      <BullsKPICards />
      <BullsDetailPanels activePanel={activePanel} onPanelChange={setActivePanel} />
    </>
  );
};

export const BullsDashboard: React.FC = () => {
  const { filters, updateFilter, setDateRange, clearFilters } = useDashboardFilters();
  const { data, loading, error, lastUpdated, refreshData } = useDashboardData(filters);
  const { kpis, sociogramData, profilesData, contextData, comparisonsData, courseOverviewData, recommendedActionsData } = useBullsMetrics(data, filters);

  if (error) {
    return (
      <DashboardLayout
        title="Dashboard BULL-S"
        subtitle="Análisis integral de convivencia escolar"
        onRefresh={refreshData}
        loading={loading}
      >
        <div className="text-center py-8">
          <div className="text-red-500">Error: {error}</div>
          <Button onClick={refreshData} className="mt-4">
            Reintentar
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  const contextValue = {
    filters, updateFilter, setDateRange, clearFilters,
    data, loading, error, lastUpdated, refreshData,
    kpis, sociogramData, profilesData, contextData, comparisonsData,
    courseOverviewData, recommendedActionsData
  };

  return (
    <BullsDashboardContext.Provider value={contextValue}>
      <DashboardLayout
        title="Dashboard BULL-S"
        subtitle="Análisis integral de convivencia escolar"
        onRefresh={refreshData}
        lastUpdated={lastUpdated}
        loading={loading}
      >
        {/* Los filtros globales ahora son parte del contenido principal del dashboard */}
        <BullsGlobalFilters />
        <BullsDashboardContent />
      </DashboardLayout>
    </BullsDashboardContext.Provider>
  );
};

export default BullsDashboard;