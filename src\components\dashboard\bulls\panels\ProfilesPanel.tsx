import React, { useState } from 'react';
import { ProfilesData, AggressorProfile, VictimProfile } from '../../../../types/bulls-dashboard';
import { Card, CardContent, CardHeader, CardTitle } from '../../../ui/card';
import { Badge } from '../../../ui/badge';
import { Button } from '../../../ui/button';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '../../../ui/tabs';
import { AlertTriangle, Shield, User, TrendingUp, Users, Eye } from 'lucide-react';
import { ResponsiveContainer, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, Legend, Cell, PieChart, Pie, Tooltip } from 'recharts';

interface ProfilesPanelProps {
  data: ProfilesData;
}

export const ProfilesPanel: React.FC<ProfilesPanelProps> = ({ data }) => {
  const [selectedAggressor, setSelectedAggressor] = useState<AggressorProfile | null>(null);
  const [selectedVictim, setSelectedVictim] = useState<VictimProfile | null>(null);
  const [viewMode, setViewMode] = useState<'overview' | 'individual'>('overview');

  const aggressorRadarData = [
    {
      trait: 'Fuerte',
      value: data.aggressorTraits.strong * 100,
      fullMark: 100
    },
    {
      trait: 'Provoca',
      value: data.aggressorTraits.provocative * 100,
      fullMark: 100
    },
    {
      trait: 'Agresivo',
      value: data.aggressorTraits.aggressive * 100,
      fullMark: 100
    }
  ];

  const victimRadarData = [
    {
      trait: 'Cobarde',
      value: data.victimTraits.coward * 100,
      fullMark: 100
    },
    {
      trait: 'Víctima',
      value: data.victimTraits.victim * 100,
      fullMark: 100
    },
    {
      trait: 'Manía',
      value: data.victimTraits.mania * 100,
      fullMark: 100
    }
  ];

  const roleDistributionData = [
    { name: 'Agresores', value: data.aggressors.length, color: '#ef4444' },
    { name: 'Víctimas', value: data.victims.length, color: '#3b82f6' },
    { name: 'Ambos roles', value: Math.floor(Math.random() * 5), color: '#f59e0b' },
    { name: 'Sin rol', value: Math.floor(Math.random() * 20), color: '#6b7280' }
  ];

  const getRiskLevel = (level: number): { label: string; color: string } => {
    if (level >= 0.8) return { label: 'Crítico', color: 'bg-red-100 text-red-800' };
    if (level >= 0.6) return { label: 'Alto', color: 'bg-orange-100 text-orange-800' };
    if (level >= 0.4) return { label: 'Medio', color: 'bg-yellow-100 text-yellow-800' };
    return { label: 'Bajo', color: 'bg-green-100 text-green-800' };
  };

  const MatrixHeatmap = () => {
    const maxIntensity = Math.max(...data.matrix.map(m => m.intensity));
    
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h4 className="font-semibold text-gray-900 text-lg">Matriz Agresor-Víctima</h4>
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-red-100 border border-gray-200 rounded"></div>
              <span>Baja intensidad</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-red-300 border border-gray-200 rounded"></div>
              <span>Media intensidad</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-red-500 border border-gray-200 rounded"></div>
              <span>Alta intensidad</span>
            </div>
          </div>
        </div>
        
        <div className="overflow-auto border border-gray-200 rounded-lg bg-white" style={{ minHeight: '600px', maxHeight: '900px' }}>
          <div className="grid gap-2 p-8" style={{ 
            gridTemplateColumns: `160px repeat(${data.victims.length}, 80px)`,
            minWidth: 'fit-content',
            minHeight: '550px'
          }}>
            {/* Header */}
            <div className="font-semibold text-gray-700 text-sm flex items-end pb-6 h-28">
              Agresores \ Víctimas
            </div>
            {data.victims.map(victim => (
              <div key={victim.id} className="text-xs text-center p-2 font-medium h-28 flex items-end justify-center">
                <div className="transform -rotate-45 origin-center whitespace-nowrap" style={{ marginBottom: '15px' }}>
                  <span title={victim.name}>
                    {victim.name.length > 15 ? victim.name.substring(0, 15) + '...' : victim.name}
                  </span>
                </div>
              </div>
            ))}
            
            {/* Rows */}
            {data.aggressors.map(aggressor => (
              <React.Fragment key={aggressor.id}>
                <div className="text-sm p-3 font-medium text-right bg-gray-50 rounded flex items-center justify-end h-18" style={{ minHeight: '72px' }}>
                  <span className="truncate" title={aggressor.name}>
                    {aggressor.name.length > 20 ? aggressor.name.substring(0, 20) + '...' : aggressor.name}
                  </span>
                </div>
                {data.victims.map(victim => {
                  const relation = data.matrix.find(m => 
                    m.aggressorId === aggressor.id && m.victimId === victim.id
                  );
                  const intensity = relation ? relation.intensity / maxIntensity : 0;
                  const isHighIntensity = intensity > 0.7;
                  const isMediumIntensity = intensity > 0.3 && intensity <= 0.7;
                  
                  return (
                    <div
                      key={`${aggressor.id}-${victim.id}`}
                      className={`w-18 border-2 border-gray-200 flex items-center justify-center text-sm font-bold rounded-lg cursor-pointer transition-all hover:scale-105 hover:shadow-md ${
                        isHighIntensity ? 'border-red-400' : isMediumIntensity ? 'border-orange-300' : 'border-gray-200'
                      }`}
                      style={{
                        height: '72px',
                        backgroundColor: intensity > 0 
                          ? `rgba(239, 68, 68, ${Math.max(intensity, 0.1)})` 
                          : '#f9fafb',
                        color: intensity > 0.5 ? 'white' : '#374151'
                      }}
                      title={`${aggressor.name} → ${victim.name}\nNominaciones: ${relation?.intensity || 0}\nIntensidad: ${(intensity * 100).toFixed(1)}%`}
                    >
                      {relation?.intensity || '0'}
                    </div>
                  );
                })}
              </React.Fragment>
            ))}
          </div>
        </div>
        
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Total de interacciones:</span>
              <span className="ml-2 text-gray-900">{data.matrix.reduce((sum, m) => sum + m.intensity, 0)}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Máxima intensidad:</span>
              <span className="ml-2 text-gray-900">{maxIntensity}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Relaciones activas:</span>
              <span className="ml-2 text-gray-900">{data.matrix.filter(m => m.intensity > 0).length}</span>
            </div>
          </div>
          <div className="mt-3 text-xs text-gray-600">
            <strong>Interpretación:</strong> Los números representan la cantidad de nominaciones de agresión. 
            Mayor intensidad de color indica mayor frecuencia de agresión reportada.
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Controles */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant={viewMode === 'overview' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('overview')}
          >
            <Eye className="h-4 w-4 mr-2" />
            Vista General
          </Button>
          <Button
            variant={viewMode === 'individual' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('individual')}
          >
            <User className="h-4 w-4 mr-2" />
            Perfiles Individuales
          </Button>
        </div>
      </div>

      {viewMode === 'overview' ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Tarjetas Resumen */}
          <div className="space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center text-red-600">
                  <AlertTriangle className="h-5 w-5 mr-2" />
                  Agresores Identificados
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold text-gray-900">{data.aggressors.length}</span>
                    <Badge variant="destructive">
                      {((data.aggressors.length / 30) * 100).toFixed(1)}% del curso
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <h5 className="font-medium text-gray-700">Top 3 Agresores:</h5>
                    {data.aggressors.slice(0, 3).map((aggressor, index) => (
                      <div key={aggressor.id} className="flex items-center justify-between text-sm">
                        <span>{index + 1}. {aggressor.name}</span>
                        <Badge className={getRiskLevel(aggressor.aggressionLevel).color}>
                          {getRiskLevel(aggressor.aggressionLevel).label}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center text-blue-600">
                  <Shield className="h-5 w-5 mr-2" />
                  Víctimas Identificadas
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold text-gray-900">{data.victims.length}</span>
                    <Badge variant="secondary">
                      {((data.victims.length / 30) * 100).toFixed(1)}% del curso
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <h5 className="font-medium text-gray-700">Top 3 Víctimas:</h5>
                    {data.victims.slice(0, 3).map((victim, index) => (
                      <div key={victim.id} className="flex items-center justify-between text-sm">
                        <span>{index + 1}. {victim.name}</span>
                        <Badge className={getRiskLevel(victim.victimizationLevel).color}>
                          {getRiskLevel(victim.victimizationLevel).label}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Distribución de Roles */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Distribución de Roles
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={250}>
                <PieChart>
                  <Pie
                    data={roleDistributionData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {roleDistributionData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      ) : (
        <Tabs defaultValue="aggressors" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="aggressors">Perfiles de Agresores</TabsTrigger>
            <TabsTrigger value="victims">Perfiles de Víctimas</TabsTrigger>
          </TabsList>
          
          <TabsContent value="aggressors" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2 space-y-4">
                {data.aggressors.map(aggressor => (
                  <Card 
                    key={aggressor.id} 
                    className={`cursor-pointer transition-all ${
                      selectedAggressor?.id === aggressor.id ? 'ring-2 ring-red-500' : 'hover:shadow-md'
                    }`}
                    onClick={() => setSelectedAggressor(aggressor)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold text-gray-900">{aggressor.name}</h4>
                          <p className="text-sm text-gray-600">{aggressor.institution}</p>
                        </div>
                        <div className="text-right">
                          <Badge className={getRiskLevel(aggressor.aggressionLevel).color}>
                            {getRiskLevel(aggressor.aggressionLevel).label}
                          </Badge>
                          <p className="text-sm text-gray-600 mt-1">
                            Nivel: {(aggressor.aggressionLevel * 100).toFixed(0)}%
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
              
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Radar de Rasgos - Agresores</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={200}>
                      <RadarChart data={aggressorRadarData}>
                        <PolarGrid />
                        <PolarAngleAxis dataKey="trait" tick={{ fontSize: 12 }} />
                        <PolarRadiusAxis angle={90} domain={[0, 100]} tick={{ fontSize: 10 }} />
                        <Radar
                          name="Agresores"
                          dataKey="value"
                          stroke="#ef4444"
                          fill="#ef4444"
                          fillOpacity={0.3}
                        />
                      </RadarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
                
                {selectedAggressor && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Perfil Detallado</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <h5 className="font-medium text-gray-700">{selectedAggressor.name}</h5>
                        <p className="text-sm text-gray-600">{selectedAggressor.institution}</p>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Fuerte:</span>
                          <span className="font-medium">{(selectedAggressor.traits.strong * 100).toFixed(0)}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Provoca:</span>
                          <span className="font-medium">{(selectedAggressor.traits.provocative * 100).toFixed(0)}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Agresivo:</span>
                          <span className="font-medium">{(selectedAggressor.traits.aggressive * 100).toFixed(0)}%</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="victims" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2 space-y-4">
                {data.victims.map(victim => (
                  <Card 
                    key={victim.id} 
                    className={`cursor-pointer transition-all ${
                      selectedVictim?.id === victim.id ? 'ring-2 ring-blue-500' : 'hover:shadow-md'
                    }`}
                    onClick={() => setSelectedVictim(victim)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold text-gray-900">{victim.name}</h4>
                          <p className="text-sm text-gray-600">{victim.institution}</p>
                        </div>
                        <div className="text-right">
                          <Badge className={getRiskLevel(victim.victimizationLevel).color}>
                            {getRiskLevel(victim.victimizationLevel).label}
                          </Badge>
                          <p className="text-sm text-gray-600 mt-1">
                            Nivel: {(victim.victimizationLevel * 100).toFixed(0)}%
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
              
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Radar de Rasgos - Víctimas</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={200}>
                      <RadarChart data={victimRadarData}>
                        <PolarGrid />
                        <PolarAngleAxis dataKey="trait" tick={{ fontSize: 12 }} />
                        <PolarRadiusAxis angle={90} domain={[0, 100]} tick={{ fontSize: 10 }} />
                        <Radar
                          name="Víctimas"
                          dataKey="value"
                          stroke="#3b82f6"
                          fill="#3b82f6"
                          fillOpacity={0.3}
                        />
                      </RadarChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
                
                {selectedVictim && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Perfil Detallado</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <h5 className="font-medium text-gray-700">{selectedVictim.name}</h5>
                        <p className="text-sm text-gray-600">{selectedVictim.institution}</p>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Cobarde:</span>
                          <span className="font-medium">{(selectedVictim.traits.coward * 100).toFixed(0)}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Víctima:</span>
                          <span className="font-medium">{(selectedVictim.traits.victim * 100).toFixed(0)}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Manía:</span>
                          <span className="font-medium">{(selectedVictim.traits.mania * 100).toFixed(0)}%</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      )}

      {/* Matriz Agresor-Víctima */}
      <Card>
        <CardHeader>
          <CardTitle>Matriz de Interacciones</CardTitle>
        </CardHeader>
        <CardContent>
          <MatrixHeatmap />
        </CardContent>
      </Card>
    </div>
  );
};