<!doctype html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="./assets/favicon-0fCLBm9g-0fCLBm9g.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Sistema completo de análisis sociométrico para la detección y prevención de bullying escolar basado en el Test BULL-S" />
    <title>BULL-S Analysis Platform</title>

    <!-- Single Page Apps for GitHub Pages -->
    <script type="text/javascript">
      // Single Page Apps for GitHub Pages
      // MIT License
      // https://github.com/rafgraph/spa-github-pages
      // This script checks to see if a redirect is present in the query string,
      // converts it back into the correct url and adds it to the
      // browser's history using window.history.replaceState(...),
      // which won't cause the browser to attempt to load the new url.
      // When the single page app is loaded further down in this file,
      // the correct url will be waiting in the browser's history for
      // the single page app to route accordingly.
      (function(l) {
        if (l.search[1] === '/' ) {
          var decoded = l.search.slice(1).split('&').map(function(s) {
            return s.replace(/~and~/g, '&')
          }).join('?');
          window.history.replaceState(null, null,
              l.pathname.slice(0, -1) + decoded + l.hash
          );
        }
      }(window.location))
    </script>
    <link rel="modulepreload" crossorigin href="./assets/react-vendor-CbKDpIlH-4dAZLOwD-4dAZLOwD.js">
    <link rel="modulepreload" crossorigin href="./assets/router-vendor-wSGMZ5EI-DEPo836n-DEPo836n.js">
    <link rel="modulepreload" crossorigin href="./assets/ui-vendor-BOQIs4ec-BcwTDcmE-BcwTDcmE.js">
    <link rel="modulepreload" crossorigin href="./assets/supabase-vendor-CzVIVmpl--3BIuIT6--3BIuIT6.js">
    <link rel="modulepreload" crossorigin href="./assets/pdf-vendor-MfzqZsA5-DFT_zJ8j-DFT_zJ8j.js">
    <link rel="modulepreload" crossorigin href="./assets/charts-vendor-BL9OqLFG-DmzU2N7Q-DmzU2N7Q.js">
    <link rel="modulepreload" crossorigin href="./assets/forms-vendor-D4y6ReHK-DRYXPAA7-DRYXPAA7.js">
    <link rel="modulepreload" crossorigin href="data:text/javascript;base64,Y29uc3QgRT0ibW9kdWxlcHJlbG9hZCIseT1mdW5jdGlvbihhLGwpe3JldHVybiBuZXcgVVJMKGEsbCkuaHJlZn0sbT17fSxnPWZ1bmN0aW9uKGwsYyx1KXtsZXQgZj1Qcm9taXNlLnJlc29sdmUoKTtpZihjJiZjLmxlbmd0aD4wKXtjb25zdCByPWRvY3VtZW50LmdldEVsZW1lbnRzQnlUYWdOYW1lKCJsaW5rIiksZT1kb2N1bWVudC5xdWVyeVNlbGVjdG9yKCJtZXRhW3Byb3BlcnR5PWNzcC1ub25jZV0iKSxoPShlPT1udWxsP3ZvaWQgMDplLm5vbmNlKXx8KGU9PW51bGw/dm9pZCAwOmUuZ2V0QXR0cmlidXRlKCJub25jZSIpKTtmPVByb21pc2UuYWxsU2V0dGxlZChjLm1hcCh0PT57aWYodD15KHQsdSksdCBpbiBtKXJldHVybjttW3RdPSEwO2NvbnN0IG89dC5lbmRzV2l0aCgiLmNzcyIpLHY9bz8nW3JlbD0ic3R5bGVzaGVldCJdJzoiIjtpZighIXUpZm9yKGxldCBzPXIubGVuZ3RoLTE7cz49MDtzLS0pe2NvbnN0IGk9cltzXTtpZihpLmhyZWY9PT10JiYoIW98fGkucmVsPT09InN0eWxlc2hlZXQiKSlyZXR1cm59ZWxzZSBpZihkb2N1bWVudC5xdWVyeVNlbGVjdG9yKGBsaW5rW2hyZWY9IiR7dH0iXSR7dn1gKSlyZXR1cm47Y29uc3Qgbj1kb2N1bWVudC5jcmVhdGVFbGVtZW50KCJsaW5rIik7aWYobi5yZWw9bz8ic3R5bGVzaGVldCI6RSxvfHwobi5hcz0ic2NyaXB0Iiksbi5jcm9zc09yaWdpbj0iIixuLmhyZWY9dCxoJiZuLnNldEF0dHJpYnV0ZSgibm9uY2UiLGgpLGRvY3VtZW50LmhlYWQuYXBwZW5kQ2hpbGQobiksbylyZXR1cm4gbmV3IFByb21pc2UoKHMsaSk9PntuLmFkZEV2ZW50TGlzdGVuZXIoImxvYWQiLHMpLG4uYWRkRXZlbnRMaXN0ZW5lcigiZXJyb3IiLCgpPT5pKG5ldyBFcnJvcihgVW5hYmxlIHRvIHByZWxvYWQgQ1NTIGZvciAke3R9YCkpKX0pfSkpfWZ1bmN0aW9uIGQocil7Y29uc3QgZT1uZXcgRXZlbnQoInZpdGU6cHJlbG9hZEVycm9yIix7Y2FuY2VsYWJsZTohMH0pO2lmKGUucGF5bG9hZD1yLHdpbmRvdy5kaXNwYXRjaEV2ZW50KGUpLCFlLmRlZmF1bHRQcmV2ZW50ZWQpdGhyb3cgcn1yZXR1cm4gZi50aGVuKHI9Pntmb3IoY29uc3QgZSBvZiByfHxbXSllLnN0YXR1cz09PSJyZWplY3RlZCImJmQoZS5yZWFzb24pO3JldHVybiBsKCkuY2F0Y2goZCl9KX07ZXhwb3J0e2cgYXMgX307Cg==">
    <script type="module" crossorigin src="./assets/index-DkE_sAqd.js"></script>
    <link rel="stylesheet" crossorigin href="./assets/index-hyKxHTs-.css">
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>
