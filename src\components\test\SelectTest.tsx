import React, { useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';

export const SelectTest = () => {
  const [selectedValue, setSelectedValue] = useState<string>('all');

  const options = [
    { value: '6B', label: '6ºB' },
    { value: '8A', label: '8ºA' },
    { value: '8B', label: '8ºB' },
  ];

  return (
    <div className="p-4 space-y-4">
      <h3>Test del componente Select</h3>
      <div className="w-64">
        <Select value={selectedValue} onValueChange={setSelectedValue}>
          <SelectTrigger>
            <SelectValue placeholder="Seleccionar curso" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Todos</SelectItem>
            {options.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <p>Valor seleccionado: {selectedValue}</p>
    </div>
  );
};