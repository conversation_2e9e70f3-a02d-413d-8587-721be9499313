import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Configurar dotenv
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuración de Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Error: Variables de entorno VITE_SUPABASE_URL y VITE_SUPABASE_ANON_KEY son requeridas');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Orden de grupos específico
const ORDEN_GRUPOS = ['6B', '8A', '8B'];

// Mapeo de preguntas
const PREGUNTAS_SOCIOMETRICAS = {
  1: "P1: ¿Con quién te gusta trabajar en equipo?",
  2: "P2: ¿Con quién te gusta jugar en el recreo?",
  3: "P3: ¿A quién invitarías a tu cumpleaños?",
  4: "P4: ¿Con quién te sientes más cómodo/a hablando?",
  5: "P5: ¿Quién te ayuda cuando tienes problemas?",
  6: "P6: ¿Con quién no te gusta trabajar en equipo?",
  7: "P7: ¿Con quién no te gusta jugar en el recreo?",
  8: "P8: ¿A quién no invitarías a tu cumpleaños?",
  9: "P9: ¿Con quién no te sientes cómodo/a hablando?",
  10: "P10: ¿Quién no te ayuda cuando tienes problemas?"
};

const PREGUNTAS_ABIERTAS = {
  11: "P11: ¿Qué tipo de conflictos has observado en tu clase?",
  12: "P12: ¿Dónde ocurren más frecuentemente los conflictos?",
  13: "P13: ¿Con qué frecuencia ocurren conflictos en tu clase?",
  14: "P14: ¿Cómo calificarías el ambiente de convivencia en tu clase?",
  15: "P15: ¿Qué sugerencias tienes para mejorar la convivencia?"
};

async function obtenerDatosCompletos() {
  try {
    console.log('📊 Obteniendo datos completos...');
    
    // Obtener estudiantes ordenados por grado y nombre
    const { data: estudiantes, error: errorEstudiantes } = await supabase
      .from('estudiantes')
      .select('*')
      .order('grado', { ascending: true })
      .order('nombre_estudiante', { ascending: true });
    
    if (errorEstudiantes) {
      throw new Error(`Error al obtener estudiantes: ${errorEstudiantes.message}`);
    }
    
    console.log(`✅ ${estudiantes.length} estudiantes obtenidos`);
    
    // Obtener respuestas
    const { data: respuestas, error: errorRespuestas } = await supabase
      .from('respuestas')
      .select('*')
      .order('estudiante_id', { ascending: true })
      .order('pregunta_id', { ascending: true });
    
    if (errorRespuestas) {
      throw new Error(`Error al obtener respuestas: ${errorRespuestas.message}`);
    }
    
    console.log(`✅ ${respuestas.length} respuestas obtenidas`);
    
    return { estudiantes, respuestas };
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    throw error;
  }
}

function crearMapeoEstudiantes(estudiantes) {
  const mapeo = {};
  estudiantes.forEach(est => {
    mapeo[est.id] = {
      nombre_completo: `${est.nombre_estudiante} ${est.apellido_estudiante}`,
      codigo: est.codigo_anonimizado,
      grado: est.grado,
      genero: est.genero,
      edad: est.edad
    };
  });
  return mapeo;
}

function interpretarRespuestaSociometrica(respuestaTexto, mapeoEstudiantes) {
  if (!respuestaTexto) return [];
  
  try {
    const ids = JSON.parse(respuestaTexto);
    if (Array.isArray(ids)) {
      return ids.map(id => {
        const estudiante = mapeoEstudiantes[id];
        return estudiante ? `${estudiante.nombre_completo} (${estudiante.codigo})` : 'Estudiante no encontrado';
      });
    }
  } catch (e) {
    // Si no es JSON válido, devolver como texto
    return [respuestaTexto];
  }
  
  return [];
}

function interpretarRespuestaAbierta(respuestaTexto) {
  if (!respuestaTexto) return 'Sin respuesta';
  
  try {
    const parsed = JSON.parse(respuestaTexto);
    
    if (typeof parsed === 'object' && parsed.selected && parsed.other) {
      // Respuesta con selección y campo "otro"
      const seleccionados = Array.isArray(parsed.selected) ? parsed.selected.join(', ') : parsed.selected;
      return `${seleccionados}${parsed.other ? '; Otro: ' + parsed.other : ''}`;
    } else if (typeof parsed === 'object' && parsed.selected) {
      // Solo selección
      return Array.isArray(parsed.selected) ? parsed.selected.join(', ') : parsed.selected;
    } else {
      // Objeto complejo
      return JSON.stringify(parsed);
    }
  } catch (e) {
    // Es texto plano
    return respuestaTexto;
  }
}

function organizarRespuestasPorEstudiante(respuestas) {
  const respuestasPorEstudiante = {};
  
  respuestas.forEach(respuesta => {
    if (!respuestasPorEstudiante[respuesta.estudiante_id]) {
      respuestasPorEstudiante[respuesta.estudiante_id] = [];
    }
    respuestasPorEstudiante[respuesta.estudiante_id].push(respuesta);
  });
  
  return respuestasPorEstudiante;
}

function determinarTipoPregunta(respuestaTexto) {
  if (respuestaTexto) {
    try {
      const parsed = JSON.parse(respuestaTexto);
      if (Array.isArray(parsed) && parsed.length > 0) {
        // Verificar si los elementos parecen UUIDs (formato de estudiantes)
        const primerElemento = parsed[0];
        if (typeof primerElemento === 'string' && primerElemento.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
          return 'sociometrica';
        }
      }
    } catch (e) {
      // No es JSON, probablemente respuesta abierta
    }
  }
  return 'abierta';
}

function organizarPorGrupos(estudiantes, respuestas) {
  console.log('📝 Organizando estudiantes por grupos...');
  
  const mapeoEstudiantes = crearMapeoEstudiantes(estudiantes);
  const respuestasPorEstudiante = organizarRespuestasPorEstudiante(respuestas);
  
  // Separar estudiantes por grupo
  const estudiantesPorGrupo = {};
  
  estudiantes.forEach(estudiante => {
    const grado = estudiante.grado;
    if (!estudiantesPorGrupo[grado]) {
      estudiantesPorGrupo[grado] = {
        con_respuestas: [],
        sin_respuestas: []
      };
    }
    
    const respuestasEstudiante = respuestasPorEstudiante[estudiante.id] || [];
    const tieneRespuestas = respuestasEstudiante.length > 0;
    
    const datosEstudiante = {
      nombre_completo: `${estudiante.nombre_estudiante} ${estudiante.apellido_estudiante}`,
      codigo: estudiante.codigo_anonimizado,
      genero: estudiante.genero,
      edad: estudiante.edad,
      grado: estudiante.grado
    };
    
    if (tieneRespuestas) {
      // Organizar respuestas por pregunta
      const respuestasPorPregunta = {};
      
      respuestasEstudiante.forEach(respuesta => {
        const tipo = determinarTipoPregunta(respuesta.respuesta_texto);
        
        if (!respuestasPorPregunta[respuesta.pregunta_id]) {
          respuestasPorPregunta[respuesta.pregunta_id] = {
            tipo: tipo,
            respuesta_texto: respuesta.respuesta_texto,
            fecha_respuesta: respuesta.fecha_respuesta
          };
        }
      });
      
      // Crear estructura de 15 preguntas
      const preguntasEstructuradas = {};
      
      // Preguntas 1-10 (sociométricas)
      for (let i = 1; i <= 10; i++) {
        preguntasEstructuradas[`P${i}`] = {
          titulo: PREGUNTAS_SOCIOMETRICAS[i],
          tipo: 'sociometrica',
          respuestas: []
        };
      }
      
      // Preguntas 11-15 (abiertas)
      for (let i = 11; i <= 15; i++) {
        preguntasEstructuradas[`P${i}`] = {
          titulo: PREGUNTAS_ABIERTAS[i],
          tipo: 'abierta',
          respuesta: 'Sin respuesta'
        };
      }
      
      // Llenar con respuestas reales
      Object.keys(respuestasPorPregunta).forEach(preguntaId => {
        const respuestaData = respuestasPorPregunta[preguntaId];
        
        // Intentar mapear pregunta_id a número de pregunta
        // Como no tenemos mapeo directo, usaremos el orden de aparición
        const respuestasOrdenadas = Object.keys(respuestasPorPregunta).sort();
        const indicePregunta = respuestasOrdenadas.indexOf(preguntaId) + 1;
        
        if (indicePregunta <= 15) {
          const clavePregunta = `P${indicePregunta}`;
          
          if (respuestaData.tipo === 'sociometrica' && indicePregunta <= 10) {
            preguntasEstructuradas[clavePregunta].respuestas = interpretarRespuestaSociometrica(
              respuestaData.respuesta_texto, 
              mapeoEstudiantes
            );
          } else if (respuestaData.tipo === 'abierta' && indicePregunta >= 11) {
            preguntasEstructuradas[clavePregunta].respuesta = interpretarRespuestaAbierta(
              respuestaData.respuesta_texto
            );
          }
        }
      });
      
      datosEstudiante.preguntas = preguntasEstructuradas;
      estudiantesPorGrupo[grado].con_respuestas.push(datosEstudiante);
    } else {
      estudiantesPorGrupo[grado].sin_respuestas.push(datosEstudiante);
    }
  });
  
  return estudiantesPorGrupo;
}

function generarReportePorGrupos(estudiantesPorGrupo) {
  console.log('📄 Generando reporte por grupos...');
  
  const reporte = {
    metadata: {
      fecha_generacion: new Date().toISOString(),
      estructura: "Estudiantes organizados por grupos (6B, 8A, 8B)",
      descripcion: "Reporte con estudiantes que presentaron cuestionario y estudiantes que no contestaron"
    },
    grupos: []
  };
  
  // Procesar grupos en el orden específico
  ORDEN_GRUPOS.forEach(grado => {
    if (estudiantesPorGrupo[grado]) {
      const grupoData = {
        grado: grado,
        total_estudiantes: estudiantesPorGrupo[grado].con_respuestas.length + estudiantesPorGrupo[grado].sin_respuestas.length,
        estudiantes_con_respuestas: estudiantesPorGrupo[grado].con_respuestas.length,
        estudiantes_sin_respuestas: estudiantesPorGrupo[grado].sin_respuestas.length,
        estudiantes_que_presentaron_cuestionario: estudiantesPorGrupo[grado].con_respuestas,
        estudiantes_que_no_contestaron: estudiantesPorGrupo[grado].sin_respuestas
      };
      
      reporte.grupos.push(grupoData);
    }
  });
  
  return reporte;
}

function generarCSVPorGrupos(reporte) {
  console.log('📊 Generando CSV por grupos...');
  
  const nombreArchivo = `reporte-por-grupos-${new Date().toISOString().split('T')[0]}.csv`;
  const rutaArchivo = path.join(__dirname, '..', nombreArchivo);
  
  let csvContent = 'Grado,Nombre_Estudiante,Codigo,Genero,Edad,Estado,P1_R1,P1_R2,P1_R3,P2_R1,P2_R2,P2_R3,P3_R1,P3_R2,P3_R3,P4_R1,P4_R2,P4_R3,P5_R1,P5_R2,P5_R3,P6_R1,P6_R2,P6_R3,P7_R1,P7_R2,P7_R3,P8_R1,P8_R2,P8_R3,P9_R1,P9_R2,P9_R3,P10_R1,P10_R2,P10_R3,P11,P12,P13,P14,P15\n';
  
  reporte.grupos.forEach(grupo => {
    // Estudiantes que presentaron cuestionario
    grupo.estudiantes_que_presentaron_cuestionario.forEach(estudiante => {
      let fila = `"${grupo.grado}","${estudiante.nombre_completo}","${estudiante.codigo}","${estudiante.genero}",${estudiante.edad},"Presentó cuestionario"`;
      
      // Agregar respuestas P1-P10 (3 respuestas cada una)
      for (let i = 1; i <= 10; i++) {
        const pregunta = estudiante.preguntas[`P${i}`];
        const resp1 = pregunta.respuestas[0] || '';
        const resp2 = pregunta.respuestas[1] || '';
        const resp3 = pregunta.respuestas[2] || '';
        fila += `,"${resp1.replace(/"/g, '""')}","${resp2.replace(/"/g, '""')}","${resp3.replace(/"/g, '""')}"`;
      }
      
      // Agregar respuestas P11-P15 (1 respuesta cada una)
      for (let i = 11; i <= 15; i++) {
        const pregunta = estudiante.preguntas[`P${i}`];
        const respuesta = pregunta.respuesta || 'Sin respuesta';
        fila += `,"${respuesta.replace(/"/g, '""')}"`;
      }
      
      csvContent += fila + '\n';
    });
    
    // Estudiantes que no contestaron
    grupo.estudiantes_que_no_contestaron.forEach(estudiante => {
      let fila = `"${grupo.grado}","${estudiante.nombre_completo}","${estudiante.codigo}","${estudiante.genero}",${estudiante.edad},"No contestó"`;
      
      // Agregar campos vacíos para todas las preguntas
      for (let i = 0; i < 45; i++) { // 30 campos para P1-P10 (3 cada una) + 15 campos para P11-P15
        fila += ',""';
      }
      
      csvContent += fila + '\n';
    });
  });
  
  fs.writeFileSync(rutaArchivo, csvContent, 'utf8');
  console.log(`📊 CSV por grupos generado: ${rutaArchivo}`);
  
  return rutaArchivo;
}

function generarResumenPorGrupos(reporte) {
  const totalEstudiantes = reporte.grupos.reduce((acc, grupo) => acc + grupo.total_estudiantes, 0);
  const totalConRespuestas = reporte.grupos.reduce((acc, grupo) => acc + grupo.estudiantes_con_respuestas, 0);
  const totalSinRespuestas = reporte.grupos.reduce((acc, grupo) => acc + grupo.estudiantes_sin_respuestas, 0);
  
  let resumenTexto = `REPORTE DE ESTUDIANTES POR GRUPOS\n` +
    `Fecha de generación: ${new Date().toLocaleString('es-ES')}\n` +
    `=======================================================\n\n` +
    `ESTRUCTURA DEL REPORTE:\n` +
    `- Estudiantes que presentaron el cuestionario (ordenados por grupos)\n` +
    `- Grupos: 6B, 8A, 8B\n` +
    `- Para cada estudiante: Nombre, Género, Edad, Grado\n` +
    `- P1-P10: 3 respuestas cada una (sociométricas)\n` +
    `- P11-P15: 1 respuesta cada una (abiertas)\n` +
    `- Estudiantes que no contestaron\n\n` +
    `RESUMEN GENERAL:\n` +
    `- Total de estudiantes: ${totalEstudiantes}\n` +
    `- Estudiantes que presentaron cuestionario: ${totalConRespuestas}\n` +
    `- Estudiantes que no contestaron: ${totalSinRespuestas}\n\n` +
    `DISTRIBUCIÓN POR GRUPO:\n`;
  
  reporte.grupos.forEach(grupo => {
    resumenTexto += `\n${grupo.grado}:\n`;
    resumenTexto += `  - Total estudiantes: ${grupo.total_estudiantes}\n`;
    resumenTexto += `  - Presentaron cuestionario: ${grupo.estudiantes_con_respuestas}\n`;
    resumenTexto += `  - No contestaron: ${grupo.estudiantes_sin_respuestas}\n`;
  });
  
  resumenTexto += `\n\nESTRUCTURA DE PREGUNTAS:\n`;
  
  // Agregar descripción de preguntas
  Object.keys(PREGUNTAS_SOCIOMETRICAS).forEach(num => {
    resumenTexto += `${PREGUNTAS_SOCIOMETRICAS[num]}\n`;
  });
  
  Object.keys(PREGUNTAS_ABIERTAS).forEach(num => {
    resumenTexto += `${PREGUNTAS_ABIERTAS[num]}\n`;
  });
  
  const nombreResumen = `resumen-por-grupos-${new Date().toISOString().split('T')[0]}.txt`;
  const rutaResumen = path.join(__dirname, '..', nombreResumen);
  fs.writeFileSync(rutaResumen, resumenTexto, 'utf8');
  console.log(`📈 Resumen por grupos generado: ${rutaResumen}`);
  
  return rutaResumen;
}

async function main() {
  try {
    console.log('🚀 Iniciando generación de reporte por grupos...');
    
    // Obtener todos los datos
    const { estudiantes, respuestas } = await obtenerDatosCompletos();
    
    // Organizar por grupos
    const estudiantesPorGrupo = organizarPorGrupos(estudiantes, respuestas);
    
    // Generar reporte estructurado por grupos
    const reportePorGrupos = generarReportePorGrupos(estudiantesPorGrupo);
    
    // Guardar reporte JSON
    const nombreArchivoJSON = `reporte-por-grupos-${new Date().toISOString().split('T')[0]}.json`;
    const rutaArchivoJSON = path.join(__dirname, '..', nombreArchivoJSON);
    fs.writeFileSync(rutaArchivoJSON, JSON.stringify(reportePorGrupos, null, 2), 'utf8');
    console.log(`📄 Reporte JSON por grupos generado: ${rutaArchivoJSON}`);
    
    // Generar CSV
    const rutaCSV = generarCSVPorGrupos(reportePorGrupos);
    
    // Generar resumen
    const rutaResumen = generarResumenPorGrupos(reportePorGrupos);
    
    console.log('\n✅ Proceso completado exitosamente!');
    console.log('\n📁 Archivos generados:');
    console.log(`   - JSON por grupos: ${path.basename(rutaArchivoJSON)}`);
    console.log(`   - CSV por grupos: ${path.basename(rutaCSV)}`);
    console.log(`   - Resumen: ${path.basename(rutaResumen)}`);
    
    console.log('\n📋 ESTRUCTURA DEL REPORTE:');
    console.log('   - Estudiantes que presentaron el cuestionario');
    console.log('   - Ordenado por grupos: 6B, 8A, 8B');
    console.log('   - Nombre, género, edad, grado');
    console.log('   - P1-P10: 3 respuestas cada una');
    console.log('   - P11-P15: 1 respuesta cada una');
    console.log('   - Estudiantes que no contestaron');
    
  } catch (error) {
    console.error('❌ Error en el proceso:', error.message);
    process.exit(1);
  }
}

// Ejecutar si es llamado directamente
if (import.meta.url === `file://${process.argv[1]}` || process.argv[1].endsWith(path.basename(__filename))) {
  main();
}

// También ejecutar directamente como fallback para Windows
main();

export { obtenerDatosCompletos, organizarPorGrupos, generarReportePorGrupos };