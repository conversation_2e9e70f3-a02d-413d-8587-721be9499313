/**
 * Script para corregir la estructura de la base de datos
 * Agrega las columnas faltantes y actualiza los datos
 */
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');\n\n// Cargar variables de entorno\nrequire('dotenv').config();\n\n// Crear cliente de Supabase\nconst supabaseUrl = process.env.VITE_SUPABASE_URL;\nconst supabaseKey = process.env.VITE_SUPABASE_SERVICE_KEY || process.env.VITE_SUPABASE_ANON_KEY;\n\nif (!supabaseUrl || !supabaseKey) {\n  console.error('Error: Se requieren las variables de entorno VITE_SUPABASE_URL y VITE_SUPABASE_SERVICE_KEY');\n  process.exit(1);\n}\n\nconst supabase = createClient(supabaseUrl, supabaseKey);\n\n// Leer el archivo SQL\nconst sqlFilePath = path.join(__dirname, '..', 'sql', 'add_missing_columns.sql');\nconst sqlContent = fs.readFileSync(sqlFilePath, 'utf8');\n\n// Ejecutar el script SQL\nasync function fixDatabaseStructure() {\n  console.log('Corrigiendo estructura de la base de datos...');\n  \n  try {\n    // Ejecutar el script completo\n    const { data, error } = await supabase.rpc('exec_sql', { \n      sql: sqlContent \n    });\n    \n    if (error) {\n      console.error('Error al ejecutar el script:', error);\n      return;\n    }\n    \n    console.log('✅ Estructura de base de datos corregida exitosamente');\n    console.log('✅ Columnas agregadas a las tablas');\n    console.log('✅ Datos de ejemplo actualizados');\n    \n    // Verificar que las tablas existen y tienen las columnas correctas\n    console.log('\\n🔍 Verificando estructura de tablas...');\n    \n    const { data: columns, error: columnsError } = await supabase\n      .from('information_schema.columns')\n      .select('table_name, column_name, data_type')\n      .in('table_name', ['instituciones', 'grupos', 'estudiantes'])\n      .eq('table_schema', 'public')\n      .order('table_name, ordinal_position');\n    \n    if (columnsError) {\n      console.error('Error al verificar columnas:', columnsError);\n    } else {\n      console.log('\\n📋 Estructura de tablas:');\n      let currentTable = '';\n      columns?.forEach(col => {\n        if (col.table_name !== currentTable) {\n          console.log(`\\n📁 ${col.table_name}:`);\n          currentTable = col.table_name;\n        }\n        console.log(`  - ${col.column_name} (${col.data_type})`);\n      });\n    }\n    \n  } catch (err) {\n    console.error('Error al corregir la estructura:', err);\n    process.exit(1);\n  }\n}\n\n// Ejecutar el script\nfixDatabaseStructure().catch(err => {\n  console.error('Error al ejecutar el script:', err);\n  process.exit(1);\n});"