import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Progress } from '../../ui/progress';

interface KPIWidgetProps {
  title: string;
  current: number;
  target: number;
  unit?: string;
  color?: string;
  loading?: boolean;
}

export const KPIWidget: React.FC<KPIWidgetProps> = ({
  title,
  current,
  target,
  unit = '',
  color = 'blue',
  loading = false
}) => {
  const percentage = target > 0 ? Math.min((current / target) * 100, 100) : 0;
  const isAchieved = percentage >= 100;

  const getColorClass = (color: string) => {
    const colors = {
      blue: 'text-blue-600',
      green: 'text-green-600',
      purple: 'text-purple-600',
      yellow: 'text-yellow-600',
      red: 'text-red-600'
    };
    return colors[color as keyof typeof colors] || 'text-blue-600';
  };

  const getProgressColor = (color: string) => {
    const colors = {
      blue: 'bg-blue-500',
      green: 'bg-green-500',
      purple: 'bg-purple-500',
      yellow: 'bg-yellow-500',
      red: 'bg-red-500'
    };
    return colors[color as keyof typeof colors] || 'bg-blue-500';
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">
            <div className="h-6 bg-gray-200 rounded w-32 animate-pulse"></div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="h-8 bg-gray-200 rounded w-24 animate-pulse"></div>
            <div className="h-2 bg-gray-200 rounded-full animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-baseline justify-between">
            <div className={`text-3xl font-bold ${getColorClass(color)}`}>
              {current.toLocaleString()}{unit}
            </div>
            <div className="text-sm text-muted-foreground">
              Meta: {target.toLocaleString()}{unit}
            </div>
          </div>
          
          <Progress 
            value={percentage} 
            className={`h-2 ${getProgressColor(color)}`}
          />
          
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">
              {percentage.toFixed(1)}% completado
            </span>
            <span className={`font-medium ${isAchieved ? 'text-green-600' : 'text-muted-foreground'}`}>
              {isAchieved ? '✓ Meta alcanzada' : `${(target - current).toLocaleString()}${unit} restantes`}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};